

import * as JulianDateTime from './JulianDateTime'


import { jmx, Logger, jolokiaService } from '@hawtio/react'

import { IJolokiaSimple } from '@jolokia.js/simple'

import {
    <PERSON>ge,
    Bullseye,
    Button,
    EmptyState, EmptyStateVariant, EmptyStateHeader, EmptyStateIcon,
    Divider,
    Drawer, DrawerContent,
    ExpandableSection,
    Grid, GridItem,
    Split, SplitItem,
    Text, TextContent, TextInput, TextVariants,
    Checkbox,
} from '@patternfly/react-core'

import {
    Table,
    Thead,
    Tr,
    Th, ThProps,
    Tbody,
    Td,
    InnerScrollContainer
} from '@patternfly/react-table';

import { SearchIcon } from '@patternfly/react-icons/dist/esm/icons'

import React, { useEffect, useRef, useState } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';
import './GlobalPlugins.css';
import {
    processControlSlavesItemsMap,
    heightDelta
} from "./ProcessControlHelpers"



// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-jobmanager-process-control");

const logPrefix = "** JobManagerProcessControl ** ";

//log.setLevel(Logger.INFO);

log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Job manager processor slave
// ********************************************************************************************************
// ********************************************************************************************************

type ProcessControlViewProps = {
    isProcessControlView: boolean;
    handleCheckboxChange: (checked: boolean) => void;
};

const adminPath = "com.csc.gtmonitor.*:type=Hawtio";

const ProcessControl: React.FunctionComponent = ({ isProcessControlView, handleCheckboxChange }: ProcessControlViewProps) => {

    type ProcessControlExecutionsType = {
        retrievedData: any,
        computedData: any
    }

    // ********************************************************************************************************

    const [processControlSlaves, setProcessControlSlaves] = useState<ProcessControlExecutionsType>({
        retrievedData: false,
        computedData: []
    })

    const [jobManagerProcessorDetails, setJobManagerProcessorDetails] = useState<ProcessControlExecutionsType>({
        retrievedData: false,
        computedData: []
    })
    const defaultSlaveCount = 1;

    // ********************************************************************************************************

    const defaultSortIndex = 0;
    const defaultSortDirection = 'asc';

    const activeSortIndex = useRef<any>(defaultSortIndex);
    const activeSortDirection = useRef<any>(defaultSortDirection);



    // ********************************************************************************************************
    // ********************************************************************************************************
    // Jolokia
    // ********************************************************************************************************
    // ********************************************************************************************************

    let jolokia: IJolokiaSimple | null = null;

    async function refreshJolokia() {
        jolokia = null;
        try {
            jolokia = await jolokiaService.getJolokia();
            log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        }
        catch (error) {
            log.error(logPrefix, "retrieveJolokia, error: ", error);
        }
    }


    // ********************************************************************************************************
    // ********************************************************************************************************
    // Retrieve Data
    // ********************************************************************************************************
    // ********************************************************************************************************
    useEffect(() => {
        computeJobManagerExecutions();
    }, []);

    const retrieveJobManagerProcessorSlavesSignature = "retrieveJobManagerProcessorSlaves(java.lang.String)";
    const retrieveJobManagerProcessorSignature = "retrieveJobManagerProcessor(java.lang.String)";


    async function retrievJobManagerProcessorSlaveDetails(processControlSlaveItem: any): Promise<any> {
        const jobManagerProcessorSlaveName = processControlSlaveItem?.name;

        let jolokia = null;
        try {
            jolokia = await jolokiaService.getJolokia();
        } catch (error) {
            log.error(logPrefix, "retrieveJolokia, error: ", error);
            return null;
        }

        if (!jolokia) return null;

        try {
            const response = await jolokia.search(adminPath);
            if (response?.length > 0) {
                const mbean = response[0];
                const operation = "retrieveJobManagerProcessorSlave(java.lang.String,java.lang.String)";
                const result = await jolokia.execute(mbean, operation, jobManagerProcessorSlaveName, "");
                const json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
                return json?.job_manager_slave ?? -1;
            }
        } catch (error) {
            log.debug(logPrefix, "retrievJobManagerProcessorSlaveDetails, exception: ", error);
        }

        return null;
    }

    async function retrieveJobManagerProcessorSlaves(processControlSlaves: any, newArgs?: any) {
        const args: any = newArgs || {};

        if (!jolokia) return;

        try {
            const response = await jolokia.search(adminPath);
            if (response?.length > 0) {
                const mbean = response[0];
                const operation = retrieveJobManagerProcessorSlavesSignature;
                const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
                const json: any = result === null ? null : JSON.parse(result.toString());

                const items = json?.resources?.items ?? [];

                const enrichedItems = await Promise.all(
                    items.map(async (item: any) => {
                        return await retrievJobManagerProcessorSlaveDetails(item);
                    })
                );

                if (json.resources) {
                    json.resources.items = enrichedItems;
                }
                processControlSlaves.retrievedData = json;
            }
        } catch (e) {
            log.debug(logPrefix, "retrieveJobManagerProcessorSlaves, exception: ", e);
        }
    }

    async function retrieveJobManagerProcessor(jobManagerProcessorDetails: any, newArgs?: any) {
        const args: any = newArgs || {};



        if (jolokia !== null) {
            try {
                const response = await jolokia.search(adminPath);
                if (response != null && response.length > 0) {
                    const mbean = response[0];
                    const operation = retrieveJobManagerProcessorSignature;
                    log.debug(logPrefix, "retrieveJobManagerProcessor, execute : ", JSON.stringify(args));
                    const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
                    const json: any = (result === null) ? null : JSON.parse(result.toString());
                    log.debug(logPrefix, "retrieveJobManagerProcessor, result: ", json);
                    jobManagerProcessorDetails.retrievedData = json;
                }
            }
            catch (e) {
                log.debug(logPrefix, "retrieveJobManagerProcessor, exception: ", e);
            }
        }
    }

    // ********************************************************************************************************
    // ********************************************************************************************************
    // Compute Data
    // ********************************************************************************************************
    // ********************************************************************************************************

    // ********************************************************************************************************

    log.debug(logPrefix, "Displaying Process Control Executions ****************************************************************************");

    // ********************************************************************************************************

    const ProcessControlItems = (props: any) => {

        const [activeSortRefresh, setActiveSortRefresh] = useState<boolean>(false);
        const toggleActiveSortRefresh = () => { setActiveSortRefresh(!activeSortRefresh); }

        const setActiveSortIndex = (sortIndex: any) => { activeSortIndex.current = sortIndex; toggleActiveSortRefresh(); }
        const getActiveSortIndex = (): any => { return activeSortIndex.current; }

        const setActiveSortDirection = (sortDirection: any) => { activeSortDirection.current = sortDirection; toggleActiveSortRefresh(); }
        const getActiveSortDirection = (): any => { return activeSortDirection.current; }

        let processControlSlavesItems: { [key: number]: any }[] = processControlSlaves.computedData.resources?.items;
        if (processControlSlavesItems === null || processControlSlavesItems === undefined) {
            processControlSlavesItems = [];
        }
        debugger
        let jobManagerProcessorItems: { [key: number]: any }[] = jobManagerProcessorDetails.computedData.job_manager_processor;
        if (jobManagerProcessorItems === null || jobManagerProcessorItems === undefined) {
            jobManagerProcessorItems = [];
        }

        function extractNumber(str: string): number {
            const match = str.match(/\d+$/);
            return match ? parseInt(match[0], 10) : 0;
        }
        let sortedProcessControlSlavesItems = processControlSlavesItems;
        if (activeSortIndex !== null) {
            sortedProcessControlSlavesItems = processControlSlavesItems.sort((a, b) => {
                const column = processControlSlavesItemsMap[getActiveSortIndex()];
                const key: any = column.key;
                const aValue: any = a[key];
                const bValue: any = b[key];

                if (key === 'name') {
                    const aNum = extractNumber(aValue);
                    const bNum = extractNumber(bValue);
                    return getActiveSortDirection() === 'asc' ? aNum - bNum : bNum - aNum;
                }

                if (typeof aValue === 'number') {
                    return getActiveSortDirection() === 'asc'
                        ? aValue - bValue
                        : bValue - aValue;
                } else {
                    return getActiveSortDirection() === 'asc'
                        ? (aValue ?? '').toString().localeCompare((bValue ?? '').toString())
                        : (bValue ?? '').toString().localeCompare((aValue ?? '').toString());
                }
            });
        }

        const getSortParams = (columnIndex: number): ThProps['sort'] => ({
            sortBy: {
                index: getActiveSortIndex(),
                direction: getActiveSortDirection(),
                defaultDirection: 'asc' // starting sort direction when first sorting a column. Defaults to 'asc'
            },
            onSort: (_event, index, direction) => {
                setActiveSortIndex(index);
                setActiveSortDirection(direction);
            },
            columnIndex
        });

        // ********************************************************************************************************

        const isProcessSlavesItemsSelectable = (processControlSlaveItem: any) => processControlSlaveItem.name !== ''; // Arbitrary logic for this example

        const selectableProcessSlavesItems = processControlSlavesItems.filter(isProcessSlavesItemsSelectable);

        const [selectableProcessSlavesItemsNums, setSelectableProcessSlavesItemsNums] = useState<string[]>([]);

        const setProcessSlavesItemsSelected = (processControlSlaveItem: any, isSelecting = true) =>
            setSelectableProcessSlavesItemsNums((prevSelected) => {
                const otherSelectableProcessSlavesItemsNums = prevSelected.filter((w) => w !== processControlSlaveItem.name);
                return isSelecting && isProcessSlavesItemsSelectable(processControlSlaveItem) ? [...otherSelectableProcessSlavesItemsNums, processControlSlaveItem.name]
                    : otherSelectableProcessSlavesItemsNums;
            });

        const selectAllProcessSlavesItems = (isSelecting = true) =>
            setSelectableProcessSlavesItemsNums(isSelecting ? selectableProcessSlavesItems.map((processControlSlaveItem: any) => processControlSlaveItem.name) : []);

        const areSeveralProcessSlavesItemsSelected = () => { return (selectableProcessSlavesItemsNums.length > 0) };

        const isProcessSlavesItemsSelected = (processControlSlaveItem: any) => selectableProcessSlavesItemsNums.includes(processControlSlaveItem.name);

        const [recentSelectedRowIndex, setRecentSelectedRowIndex] = useState<number | null>(null);

        // ********************************************************************************************************

        const shiftKey = useRef(false);
        const setShiftKey = (state: boolean) => { shiftKey.current = state; }
        const isShiftKey = (): boolean => { return shiftKey.current; }

        const controlKey = useRef(false);
        const setControlKey = (state: boolean) => { controlKey.current = state; }
        const isControlKey = (): boolean => { return controlKey.current; }

        // ********************************************************************************************************

        const onSelectProcessSlaveItem = (processControlSlaveItem: any, rowIndex: number, isSelecting: boolean) => {
            // If the user does control + selecting the checkboxes, then selected checkbox should be added to existing ones
            // If the user does shift + selecting the checkboxes, then all intermediate checkboxes should be selected
            if (isShiftKey() && recentSelectedRowIndex !== null) {
                const numberSelected = rowIndex - recentSelectedRowIndex;
                const intermediateIndexes =
                    numberSelected > 0
                        ? Array.from(new Array(numberSelected + 1), (_x, i) => i + recentSelectedRowIndex)
                        : Array.from(new Array(Math.abs(numberSelected) + 1), (_x, i) => i + rowIndex);
                intermediateIndexes.forEach((index) => setProcessSlavesItemsSelected(processControlSlavesItems[index], isSelecting));
            }

            else
                if (isControlKey())
                    setProcessSlavesItemsSelected(processControlSlaveItem, isSelecting);
                else
                    if (isProcessSlavesItemsSelectable(processControlSlaveItem) && !isProcessSlavesItemsSelected(processControlSlaveItem))
                        setSelectableProcessSlavesItemsNums([processControlSlaveItem.name]);
                    else
                        setSelectableProcessSlavesItemsNums([]);

            setRecentSelectedRowIndex(rowIndex);
        };

        // ********************************************************************************************************

        useEffect(() => {
            const onKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Shift')
                    setShiftKey(true);
                if (e.key === 'Control')
                    setControlKey(true);
            };

            const onKeyUp = (e: KeyboardEvent) => {
                if (e.key === 'Shift')
                    setShiftKey(false);
                if (e.key === 'Control')
                    setControlKey(false);
            };

            document.addEventListener('keydown', onKeyDown);
            document.addEventListener('keyup', onKeyUp);

            return () => {
                document.removeEventListener('keydown', onKeyDown);
                document.removeEventListener('keyup', onKeyUp);
            };
        }, []);

        // ********************************************************************************************************

        const displayValue = (value: any, type: string): string => {
            switch (type) {
                case "int": return value.toString();
                case "string": return value;
                case "julianTime": return JulianDateTime.formatJulianAsTime(value);
                case "julianDate": return JulianDateTime.formatJulianAsDate(value);
                default: return value;  // let it be automatically converted ...
            }
        }

        const ProcessControlSlaveItem = (props: any) => {
            const { processControlSlaveItem, rowIndex, itemsMap } = props;


            return (
                <Tr
                    tabIndex={-1}
                    style={{ cursor: 'pointer', fontFamily: 'monospace' }}
                    onClick={() => onSelectProcessSlaveItem(processControlSlaveItem, rowIndex, true)}
                >
                    <Td
                        tabIndex={-1}
                        className="custom-compact-table_padding"
                        stickyMinWidth="auto"
                        select={{
                            rowIndex: rowIndex,
                            onSelect: (_event, isSelecting) =>
                                onSelectProcessSlaveItem(processControlSlaveItem, rowIndex, isSelecting),
                            isSelected: isProcessSlavesItemsSelected(processControlSlaveItem),
                            isDisabled: !isProcessSlavesItemsSelectable(processControlSlaveItem),
                        }}
                    />

                    {/* Data columns */}
                    {itemsMap.map((column: any, columnIndex: number) => {

                        return (
                            <Td
                                tabIndex={-1}
                                key={column.key}
                                className="custom-compact-table_padding"
                                dataLabel={column.key}
                                style={{
                                    textAlign: column.align,
                                    padding: column.padding,
                                    fontSize: 'inherit',
                                }}
                            >
                                {displayValue(processControlSlaveItem[column.key], column.type)}
                            </Td>
                        );
                    })}
                </Tr>
            );
        };

        //*********************************************************************************************************
        const ProcessControSearch = ({
        }: any) => {
            const [formSlaveCount, setFormSlaveCount] = useState<number>(processControlSlavesItems?.length ?? defaultSlaveCount);
            const [searchExpanded, setSearchExpanded] = useState<boolean>(true);


            async function updateJobManagerProcessorSlaveCount(formSlaveCount: any): Promise<any> {

                let jolokia = null;
                try {
                    jolokia = await jolokiaService.getJolokia();
                    log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
                }
                catch (error) {
                    log.error(logPrefix, "retrieveJolokia, error: ", error);
                }
                let json: any = null;
                if (jolokia !== null) {
                    const response = await jolokia.search(adminPath);
                    if (response != null && response.length > 0) {
                        const mbean = response[0];
                        const operation = "updateJobManagerProcessorSlaveCount(java.lang.Integer)";
                        const result = await jolokia.execute(mbean, operation, formSlaveCount);
                        log.debug(logPrefix, "updateJobManagerProcessorSlaveCount, result: ", result);
                        json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
                    }
                }
            }


            useEffect(() => {
                if (processControlSlavesItems?.length !== undefined) {
                    setFormSlaveCount(processControlSlavesItems.length);
                }
            }, [processControlSlavesItems?.length]);

            const onSlaveCountChange = (_event: React.FormEvent<HTMLInputElement>, value: string) => {
                setFormSlaveCount((value !== undefined && value !== null) ? parseInt(value) : defaultSlaveCount);
            }

            const handleApplyClick = async () => {
                await updateJobManagerProcessorSlaveCount(formSlaveCount);
                await computeJobManagerExecutions();
            };

            return (
                <div style={{ position: 'relative' }}>
                    <ExpandableSection
                        toggleText="General Information"
                        isIndented
                        isExpanded={searchExpanded}
                        onToggle={(_event, isExpanded) => setSearchExpanded(isExpanded)}
                    >
                        <Grid tabIndex={-1} hasGutter={false}>
                            <GridItem tabIndex={-1} span={8}>
                                <Grid tabIndex={-1} hasGutter style={{ alignItems: "center" }}>
                                    <GridItem tabIndex={-1} span={2}>
                                        <TextContent tabIndex={-1}>
                                            <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>Status</Text>
                                        </TextContent>
                                    </GridItem>
                                    <GridItem tabIndex={-1} span={2} >
                                        <TextContent tabIndex={-1}>
                                            <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>{jobManagerProcessorItems?.status ?? 'N/A'}</Text>
                                        </TextContent>
                                    </GridItem>
                                    <GridItem tabIndex={-1} span={2}></GridItem>
                                    <GridItem tabIndex={-1} span={2}>
                                        <TextContent tabIndex={-1}>
                                            <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>Slave Count</Text>
                                        </TextContent>
                                    </GridItem>
                                    {processControlSlavesItems?.length !== 0 ? (
                                        <GridItem tabIndex={-1} span={2}>
                                            <TextInput
                                                className="font-size-13"
                                                id="searchId"
                                                type="number"
                                                value={formSlaveCount}
                                                onChange={onSlaveCountChange}
                                            />
                                        </GridItem>
                                    ) : (
                                        <GridItem tabIndex={-1} span={2} >
                                            <TextContent tabIndex={-1}>
                                                <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>{processControlSlavesItems?.length ?? 'N/A'}</Text>
                                            </TextContent>
                                        </GridItem>
                                    )}
                                </Grid>
                            </GridItem>
                            <GridItem tabIndex={-1} span={8}>
                                <Grid tabIndex={-1} hasGutter style={{ alignItems: "center" }}>
                                    <GridItem tabIndex={-1} span={2}>
                                        <TextContent tabIndex={-1}>
                                            <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>Poll Time</Text>
                                        </TextContent>
                                    </GridItem>
                                    <GridItem tabIndex={-1} span={2} >
                                        <TextContent tabIndex={-1}>
                                            <Text className="font-size-13" tabIndex={-1} component={TextVariants.p}>{jobManagerProcessorItems?.pollTime ?? 'N/A'}</Text>
                                        </TextContent>
                                    </GridItem>
                                    <GridItem tabIndex={-1} span={1}></GridItem>
                                    <GridItem tabIndex={-1} span={4}>
                                        <Split tabIndex={-1} hasGutter style={{ alignItems: "center" }}>
                                            <SplitItem tabIndex={-1} isFilled >
                                            </SplitItem>
                                            <SplitItem tabIndex={-1} >
                                                <Button
                                                    className="apply-button"
                                                    variant="primary"
                                                    size="xs"
                                                    onClick={() => handleApplyClick()}
                                                    isDisabled={formSlaveCount === processControlSlavesItems?.length}
                                                >
                                                    Apply
                                                </Button>
                                            </SplitItem>
                                            <SplitItem tabIndex={-1}>
                                            </SplitItem>
                                        </Split>
                                    </GridItem>
                                </Grid>
                            </GridItem>
                        </Grid>
                    </ExpandableSection>
                    <div className="top-central-position">
                        <div className="checkbox-title-container">
                            <TextContent tabIndex={-1} className="day_summary_style">
                                <Text tabIndex={-1} component={TextVariants.p}>
                                    Job Control
                                </Text>
                            </TextContent>
                        </div>
                        <div className="checkbox-container">
                            <Checkbox
                                id="checkbox-reversed"
                                name="checkbox-reversed"
                                onChange={(_, checked) => handleCheckboxChange(checked)}
                                isChecked={isProcessControlView}
                                className="font-size-12"
                            />
                        </div>
                    </div>
                </div>
            )
        };

        const ProcessControlExecutionsTable = () => {
            async function pauseJobManagerProcessor(): Promise<any> {
                await refreshJolokia();

                let json: any = null;
                if (jolokia !== null) {
                    const response = await jolokia.search(adminPath);
                    if (response != null && response.length > 0) {
                        const mbean = response[0];
                        const operation = "pauseJobManagerProcessor()";
                        log.debug(logPrefix, "pauseJobManagerProcessor, execute : ");
                        const result = await jolokia.execute(mbean, operation);
                        json = (result === null) ? null : JSON.parse(result.toString());
                    }
                }
            }

            async function resumeJobManagerProcessor(): Promise<any> {
                await refreshJolokia();

                let json: any = null;
                if (jolokia !== null) {
                    const response = await jolokia.search(adminPath);
                    if (response != null && response.length > 0) {
                        const mbean = response[0];
                        const operation = "resumeJobManagerProcessor()";
                        log.debug(logPrefix, "resumeJobManagerProcessor, execute : ");
                        const result = await jolokia.execute(mbean, operation);
                        json = (result === null) ? null : JSON.parse(result.toString());
                    }
                }
            }

            async function killJobManagerProcessorSlave(processControlSlaveItem: any): Promise<any> {
                log.debug(logPrefix, "killJobManagerProcessorSlave : " + processControlSlaveItem?.name + " / " + processControlSlaveItem)

                const jobManagerProcessorSlaveName = processControlSlaveItem?.name;

                let jolokia = null;
                try {
                    jolokia = await jolokiaService.getJolokia();
                    log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
                }
                catch (error) {
                    log.error(logPrefix, "retrieveJolokia, error: ", error);
                }
                let json: any = null;
                if (jolokia !== null) {
                    const response = await jolokia.search(adminPath);
                    if (response != null && response.length > 0) {
                        const mbean = response[0];
                        const operation = "killJobManagerProcessorSlave(java.lang.String)";
                        const result = await jolokia.execute(mbean, operation, jobManagerProcessorSlaveName);
                        log.debug(logPrefix, "killJobManagerProcessorSlave, result: ", result);
                        json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
                    }
                }
            }


            const onPauseSlave = async () => {
                await pauseJobManagerProcessor();
                await computeJobManagerExecutions()
            }

            const onUnpauseSlave = async () => {
                await resumeJobManagerProcessor();
                await computeJobManagerExecutions();
            }

            const onKillSlave = async () => {
                const processControlSlaveItem: any = processControlSlavesItems.find((item: any) => item.name === selectableProcessSlavesItemsNums[0]);
                await killJobManagerProcessorSlave(processControlSlaveItem)
                await computeJobManagerExecutions();
            }

            return (
                <div>
                    <Divider tabIndex={-1} component="hr" className="padding-top-10" />
                    <Split tabIndex={-1} hasGutter style={{ alignItems: 'center' }}>
                        <SplitItem tabIndex={-1} className="padding-left-10">
                            <TextContent tabIndex={-1}>
                                <Text className="font-size-13" tabIndex={-1}>Selected:</Text>
                            </TextContent>
                        </SplitItem>
                        <SplitItem tabIndex={-1}>
                            <Badge tabIndex={-1} className="font-size-12">{selectableProcessSlavesItemsNums.length}</Badge>
                        </SplitItem>
                        {jobManagerProcessorItems?.slaveCount !== 0 ? (

                            <SplitItem tabIndex={-1} className="padding-top-10">
                                <Button
                                    className="font-size-13"
                                    variant="primary"
                                    size="xs"
                                    onClick={() => onPauseSlave()}
                                >
                                    Pause Job Manager
                                </Button>
                            </SplitItem>
                        ) : (

                            <SplitItem tabIndex={-1} className="padding-top-10">
                                <Button
                                    className="font-size-13"
                                    variant="primary"
                                    size="xs"
                                    onClick={() => onUnpauseSlave()}
                                >
                                    Unpause Job Manager
                                </Button>
                            </SplitItem>
                        )}
                        <SplitItem tabIndex={-1} className="padding-top-10">
                            <Button
                                className="font-size-13"
                                variant="primary"
                                size="xs"
                                onClick={() => onKillSlave()}
                                isDisabled={selectableProcessSlavesItemsNums.length !== 1}
                            >
                                Kill Slave
                            </Button>
                        </SplitItem>
                    </Split>

                    <GraphTalkComponentDiv delta={heightDelta}>
                        <InnerScrollContainer>
                            <Table
                                className="custom-compact-table"
                                tabIndex={-1}
                                isStriped={sortedProcessControlSlavesItems.length > 0}
                                aria-label="Process Control Executions table"
                                variant="compact"
                                borders={false}
                                isStickyHeader
                            >
                                <Thead tabIndex={-1}>
                                    <Tr tabIndex={-1}>
                                        <Th
                                            tabIndex={-1}
                                            stickyMinWidth="auto"
                                            select={{
                                                onSelect: (_event, isSelecting) => selectAllProcessSlavesItems(isSelecting),
                                                isSelected: areSeveralProcessSlavesItemsSelected(),
                                            }}
                                            aria-label="Process Control Executions Table Select"
                                        />

                                        {processControlSlavesItemsMap.map((column: any, columnIndex: number) => {
                                            return (
                                                <Th
                                                    key={column.key}
                                                    className="pf-v5-c-table__th truncate-header"
                                                    tabIndex={-1}
                                                    sort={getSortParams(columnIndex)}
                                                    modifier="truncate"
                                                    style={{
                                                        width: column.width,
                                                        minWidth: column.width,
                                                        justifyItems: column.align,
                                                    }}
                                                >
                                                    {column.header}
                                                </Th>
                                            );
                                        })}
                                    </Tr>
                                </Thead>

                                <Tbody tabIndex={-1}>
                                    {sortedProcessControlSlavesItems.length > 0 ? (
                                        sortedProcessControlSlavesItems.slice().map((item: any, index: number) => (
                                            <ProcessControlSlaveItem
                                                key={index}
                                                tabIndex={-1}
                                                processControlSlaveItem={item}
                                                rowIndex={index}
                                                itemsMap={processControlSlavesItemsMap}
                                            />
                                        ))
                                    ) : (
                                        <Tr tabIndex={-1}>
                                            <Td tabIndex={-1} colSpan={processControlSlavesItemsMap.length + 1}>
                                                <Bullseye tabIndex={-1}>
                                                    <EmptyState tabIndex={-1} variant={EmptyStateVariant.xs}>
                                                        <EmptyStateHeader
                                                            tabIndex={-1}
                                                            titleText="No Process Control Executions Found"
                                                            icon={<EmptyStateIcon icon={SearchIcon} className="font-size-14" />}
                                                        />
                                                    </EmptyState>
                                                </Bullseye>
                                            </Td>
                                        </Tr>
                                    )}
                                </Tbody>
                            </Table>
                        </InnerScrollContainer>
                    </GraphTalkComponentDiv>
                </div>
            );
        };


        // ********************************************************************************************************

        return (
            <Drawer tabIndex={-1} isInline isExpanded={true}>
                <DrawerContent tabIndex={-1}>
                    <ProcessControSearch />
                    <ProcessControlExecutionsTable />
                </DrawerContent>
            </Drawer>
        )
    }

    // ********************************************************************************************************

    async function computeJobManagerExecutions(args?: any) {
        await refreshJolokia();

        let jobManagerProcessorDetailsNew = { ...jobManagerProcessorDetails };
        let processControlSlavesNew = { ...processControlSlaves };

        await retrieveJobManagerProcessor(jobManagerProcessorDetailsNew, args);
        await retrieveJobManagerProcessorSlaves(processControlSlavesNew, args);
        try {
            jobManagerProcessorDetailsNew.computedData = jobManagerProcessorDetailsNew.retrievedData;
            processControlSlavesNew.computedData = processControlSlavesNew.retrievedData;
        } catch (e) { }

        setJobManagerProcessorDetails(jobManagerProcessorDetailsNew);
        setProcessControlSlaves(processControlSlavesNew);
    }

    // ********************************************************************************************************

    log.debug(logPrefix, "Displaying GtMonitor ****************************************************************************");

    return (
        <GraphTalkComponent tabIndex={-1} title="Process Control" onCompute={computeJobManagerExecutions}>
            <ProcessControlItems tabIndex={-1} />
        </GraphTalkComponent>
    )
}

export default ProcessControl;