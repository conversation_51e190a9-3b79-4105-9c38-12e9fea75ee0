{
  "compilerOptions" : {
    "baseUrl"                           : ".",
    "target"                            : "es5",
    "lib"                               : [
                                            "dom",
                                            "dom.iterable",
                                            "esnext"
                                          ],
    "allowJs"                           : true,
    "skipLibCheck"                      : true,
    "esModuleInterop"                   : true,
    "allowSyntheticDefaultImports"      : true,
    "strict"                            : true,
    "forceConsistentCasingInFileNames"  : true,
    "noFallthroughCasesInSwitch"        : true,
    "noImplicitReturns"                 : true,
    "noImplicitThis"                    : true,
    "module"                            : "esnext",
    "moduleResolution"                  : "node",
    "resolveJsonModule"                 : true,
    "isolatedModules"                   : true,
    "noEmit"                            : true,
    "jsx"                               : "react-jsx",
    "importHelpers"                     : true,
  },

  "include" : [
    "src/graphtalkplugin",
    "src/graphtalkplugin/**/*",
    "node_modules/*",
    ".yarn/cache/*",
    ".yarn/cache/**/*"
  ]
}
