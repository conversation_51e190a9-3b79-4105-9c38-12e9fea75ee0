import * as JulianDateTime from './JulianDateTime'

export const jobManagerItemsMap = [
    {key : "num",         header : "Job Id",   type : "int",           align : 'left',     padding : '0 0 0 15px',   width: "40px", },
    {key : "name",        header : "Name",     type : "string",        align : 'left',     padding : '0 15px 0 0',   width: "200px", },
    {key : "user",        header : "User",     type : "string",        align : 'left',     padding : '0 15px 0 0',   width: "150px", },
    {key : "start_date",  header : "Date",     type : "julianDate",    align : 'center',   padding : '0 5px 0 0',    width: "120px", },
    {key : "status",      header : "Status",   type : "string",        align : 'left',     padding : '0 0 0 15px',   width: "120px", },
  ];

  export const statusList = [
          { content: '',            value: '' },
          { content: 'Cancelled',   value: 'cancelled' },
          { content: 'Done',        value: 'done' },
          { content: 'Pending',     value: 'pending' },
          { content: 'Processing',  value: 'processing' },
          { content: 'Suspended',   value: 'suspended' },
          { content: 'Error',       value: 'error' }
        ];    
        
  export const formNumbersList = [
    { content: 'Default', value: 50 },
    { content: '10',      value: 10 },
    { content: '25',      value: 25 },
    { content: '50',      value: 50 },
    { content: '100',     value: 100 },
    { content: '200',     value: 200 },
    { content: '500',     value: 500 },
    { content: 'All',     value: Infinity }
  ]; 
  
  export const AccordionSectionKeys = {
    JOB_INFORMATION: 'job-information',
    USER_PARAMETERS: 'user-parameters',
    ERROR: 'error'
  };

  export const getJobInformationProp = (jobManagerDetails?: any) => {
    return [
      { name: "Job Id",           value: jobManagerDetails?.num },
      { name: "Name",             value: jobManagerDetails?.name },
      { name: "User",             value: jobManagerDetails?.user },
      { name: "Status",           value: jobManagerDetails?.status },
      { name: "Submitted Date",   value: getDates("Submitted Date", jobManagerDetails) },
      { name: "Submitted Time",   value: getDates("Submitted Time", jobManagerDetails) },
      { name: "Start Date",       value: getDates("Start Date", jobManagerDetails) },
      { name: "Start Time",       value: getDates("Start Time", jobManagerDetails) },
      { name: "Transaction",      value: jobManagerDetails?.gt_transaction ?? "N/A" },
      { name: "dependencies",     value: jobManagerDetails?.commit_frequency ?? "N/A" },
      ];
    };

  interface ExecutionProps {
    post_date?: string;
    post_time?: string;
    start_date?: string;
    start_time?: string;
    }
  
  export const getDates = (type: string, props: ExecutionProps = {}): string => {
    const formatDate = (date?: string) => (date ? JulianDateTime.formatJulianAsDate(date) : "N/A");
    const formatTime = (time?: string) => (time ? JulianDateTime.formatJulianAsTime(time) : "N/A");
  
    switch (type) {
      case "Submitted Date":
        return `${formatDate(props?.post_date)}`;
      case "Submitted Time":
        return `${formatTime(props?.post_time)}`;
      case "Start Date":
        return `${formatDate(props?.start_date)}`;
      case "Start Time":
        return `${formatTime(props?.start_time)}`;
      default:
        return "N/A";
    }
  };

  
  enum DeltaValue {
    SearchExpanded = -220,
    SearchCollapsed = -45,
  }

  export const getDeltaValue = (isSearchExpanded: boolean) => {
  
      return isSearchExpanded ? DeltaValue.SearchExpanded : DeltaValue.SearchCollapsed;
  };