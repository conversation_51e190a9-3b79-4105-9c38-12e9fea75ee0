import React from 'react'

import { But<PERSON>, Flex, FlexItem, Icon, Modal } from '@patternfly/react-core'

import { PlusCircleIcon } from '@patternfly/react-icons'

import { useState } from 'react'


// ********************************************************************************************************
// ********************************************************************************************************
// SystemOptions
// ********************************************************************************************************
// ********************************************************************************************************

type Props = {
  component?      : any,
  item?           : any,
  context?        : any,
  isModal?        : boolean,
  serverPoolName? : string,
  serverName?     : string,
  groupName?      : string,
  optionName?     : string
};

export const SystemOptions = (props : Props) : any => {
  const systemOptions = props.component?.systemOptions;
  const item = props.item;
  const context = props.context;

  if (!context?.canAccessCounters)
    return <></>

  if (!systemOptions)
    return <></>

  let serverPoolName = null;
  let serverName     = null;

  if (typeof item === "string") {
    serverPoolName = item.trim();
  }
  else {
    serverPoolName = (item?.name === null || item?.name === undefined) ? null : item?.name?.trim();
    serverName     = (item?.subname === null || item?.subname === undefined) ? null : item?.subname?.trim();
  }

  serverPoolName  = (serverPoolName === "") ? null : serverPoolName;
  serverName      = (serverName === "") ? null : serverName;

  if (serverPoolName !== null && serverName !== null)
    return <SystemOptionsButtonModal serverPoolName = {serverPoolName} serverName = {serverName}/>
  if (serverPoolName !== null)
    return <SystemOptionsButtonModal serverPoolName = {serverPoolName}/>
  if (serverName !== null)
    return <SystemOptionsButtonModal serverName = {serverName}/>

  return <SystemOptionsButtonModal/>
}

// ********************************************************************************************************

export const SystemOptionsButtonModal: React.FunctionComponent<Props> = (props) => {

  const [isSystemOptionsOpen, setIsSystemOptionsOpen] = useState(false);

  const handleSystemOptionsClick = () => {
    setIsSystemOptionsOpen(true);
  }

  const handleSystemOptionsClose = () => {
    setIsSystemOptionsOpen(false);
  }

  return (
    <Flex tabIndex = {-1}>
      <FlexItem tabIndex = {-1} onClick = {(e : any) => {e.stopPropagation();}}>
        <Button
          tabIndex  = {0}
          variant   = "plain"
          size      = "default"
          onClick   = {handleSystemOptionsClick}
          icon      = {<Icon status = "info" iconSize = "md" style = {{padding: "0px 0px 0px 0px"}}><PlusCircleIcon/></Icon>}
          style     = {{padding: "0px 0px 0px 0px"}}
        />
        <Modal
          isOpen            = {isSystemOptionsOpen}
          onClose           = {handleSystemOptionsClose}
          width             = "50%"
          height            = "60%"
          hasNoBodyWrapper  = {true}
          className         = "dialog"
          aria-label        = "SystemOptionsButtonModal"
        >
          <div style={{padding: "20px"}}>
            <h3>System Options</h3>
            <p>System Options content will be implemented here...</p>
            <p>Server Pool: {props.serverPoolName || 'N/A'}</p>
            <p>Server Name: {props.serverName || 'N/A'}</p>
          </div>
        </Modal>
      </FlexItem>
    </Flex>
  )
}

export default SystemOptions;
