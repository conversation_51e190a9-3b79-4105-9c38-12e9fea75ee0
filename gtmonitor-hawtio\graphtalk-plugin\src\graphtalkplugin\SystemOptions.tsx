import { IJolokiaSimple } from '@jolokia.js/simple'

import { Logger, jolokiaService } from '@hawtio/react'

import { Table, Thead, Tr, Th, Tbody, Td } from '@patternfly/react-table';

import { Button, Flex, FlexItem, Icon, Modal, Split, SplitItem } from '@patternfly/react-core'

import { PlusCircleIcon, AngleRightIcon, AngleDownIcon, FolderOpenIcon, FolderIcon } from '@patternfly/react-icons'

import { ChartGroup, Chart, ChartLine, ChartAxis, ChartThemeColor, ChartLegend } from '@patternfly/react-charts'

import { useState, useMemo, useCallback, useRef, useEffect } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import './GlobalPlugins.css';


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-systemoptions");

const logPrefix = "** SystemOptions ** ";

log.setLevel(Logger.INFO);

log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// SystemOptions
// ********************************************************************************************************
// ********************************************************************************************************

type Props = {
  isModal?        : boolean,
  serverPoolName? : string;
  serverName?     : string;
  groupName?      : string;
  optionName?     : string
};

type SystemOptionsItemsProps = {
  expandedRows? : Record<string, boolean>;
  toggleRow?    : (k: string) => void;
};

type OptionItem = {
  serverPoolName? : string;
  groupName?      : string;
  optionName?     : string;
  [key: string]   : any;
};

type OptionTree = {
  [serverPoolName: string]: {
    [groupName: string]: {
      [optionName: string]: OptionItem[];
    };
  };
};

const SystemOptions: React.FunctionComponent<Props> = (props: Props) => {

  const propIsModal         = props.isModal         ? props.isModal : false;
  const propServerPoolName  = props.serverPoolName  ? props.serverPoolName : null;
  const propServerName      = props.serverName      ? props.serverName : null;
  const propGroupName       = props.groupName       ? props.groupName : null;
  const propOptionName      = props.optionName      ? props.optionName : null;

  const maxHistory = 100;

  type SystemOptionsType = {
    retrievedData : any,
    computedData  : any,
    historyData   : any;
  }

  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRow = (key: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const [systemOptions, setSystemOptions] = useState<SystemOptionsType>({
    retrievedData : null,
    computedData  : null,
    historyData   : []
  });

  let jolokia : IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  const adminPath = "com.csc.gtmonitor.*:type=Hawtio";

  const retrieveSystemOptionsSignature = "retrieveSystemOptions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)";

  async function retrieveSystemOptions(): Promise<any> {
    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);

        if (response != null && response.length > 0) {
          const mbean = response[0];

          log.debug(logPrefix, "retrieveSystemOptions, args: ", propServerPoolName, " / ", propServerName, " / ", propGroupName, " / ", propOptionName);

          const result = await jolokia.execute(mbean, retrieveSystemOptionsSignature, propServerPoolName, propServerName, propGroupName, propOptionName);

          log.debug(logPrefix, "retrieveSystemOptions, result: ", result);

          const json: any = (result === null) ? null : JSON.parse(result.toString());

          if (json !== null)
            log.debug(logPrefix, "retrieveSystemOptions, json: ", json);
          else
            log.debug(logPrefix, "retrieveSystemOptions, cannot retrieve system options");

          return json;
        }
      }
      catch (e) {
        log.error(logPrefix, "retrieveSystemOptions, exception: ", e);
      }
    }

    return null;
  }

  async function retrieveData(options: any): Promise<any> {
    if (options !== undefined) {
      options.retrievedData = await retrieveSystemOptions();
      if (options.retrievedData == null)
        log.error(logPrefix, "retrieveData, cannot retrieve system options");
      else
        log.debug(logPrefix, "retrieveData, system options retrieved: " + options.retrievedData);
    }
    else
      log.error(logPrefix, "retrieveData, options is undefined");
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  async function computeSystemOptions(): Promise<any> {
    await refreshJolokia();

    try {
      let optionsNew = { ...systemOptions };

      await retrieveData(optionsNew);

      log.debug(logPrefix, "computeSystemOptions, options retrieved: " + optionsNew);

      optionsNew.computedData = optionsNew.retrievedData;

      setSystemOptions(optionsNew);

      log.debug(logPrefix, "computeSystemOptions, system options computed");
    }
    catch (e) {
      log.error(logPrefix, "computeSystemOptions, exception: ", e);
    }
  }

  // ********************************************************************************************************

  const SystemOptionsItems = (props: SystemOptionsItemsProps) => {

    const { expandedRows = {}, toggleRow } = props;
    const showServerNameInstead = !!propServerName;
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);

    const historyData = useMemo(() => systemOptions.historyData || [], [systemOptions.historyData]);

    useEffect(() => {
      const handleResize = () => {
        setWindowWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    const treeData = useMemo(() => {
      if (!systemOptions.computedData) return {};
      return systemOptions.computedData;
    }, [systemOptions.computedData]);

    const renderRows = (data: any, parentKey = '', treeLevel = 0) => {
      const rows: any[] = [];

      Object.entries(data).forEach(([key, value]) => {
        const currentKey = parentKey ? `${parentKey}-${key}` : key;
        const isExpanded = expandedRows[currentKey] || false;

        const cells = Array(5).fill(null).map((_, i) => (
          <Td key = {`${currentKey}-${i}`}>{i === treeLevel ? key : ''}</Td>
        ));

        const isOptionRow = Array.isArray(value) && currentKey.split('-').pop() === key;

        if (isOptionRow) {
          rows.push(
            <Tr key={currentKey}>
              {cells}
            </Tr>
          );
        } else {
          const hasChildren = typeof value === 'object' && value !== null && !Array.isArray(value);
          const icon = hasChildren ? (isExpanded ? <AngleDownIcon /> : <AngleRightIcon />) : null;

          rows.push(
            <Tr 
              key={currentKey} 
              onClick={() => hasChildren && toggleRow && toggleRow(currentKey)}
              style={{ cursor: hasChildren ? 'pointer' : 'default' }}
            >
              <Td>
                <Flex alignItems={{ default: 'alignItemsCenter' }}>
                  {icon && <FlexItem>{icon}</FlexItem>}
                  <FlexItem>{key}</FlexItem>
                </Flex>
              </Td>
              {cells.slice(1)}
            </Tr>
          );

          if (isExpanded && hasChildren) {
            rows.push(...renderRows(value, currentKey, treeLevel + 1));
          }
        }
      });

      return rows;
    };

    return (
      <Table
        tabIndex  = {-1}
        className = "systemoptions-custom-table"
        isTreeTable
        variant   = "compact"
        borders   = {false}
        isStriped = {true}
        isStickyHeader
        style     = {{ width: '100%', position: 'relative' }}
      >
        <Thead tabIndex = {-1}>
          <Tr tabIndex = {-1} style = {{ fontSize: "inherit" }}>
            <Th tabIndex = {-1} style = {{ width: "15%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 25px", whiteSpace: 'normal' }}>
              {showServerNameInstead ? "Server Name" : "Server Pool"}
            </Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Option Group</Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Option</Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "right", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Current Values</Th>
            <Th tabIndex = {-1} aria-label = "Chart column" style = {{ width: "55%", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {renderRows(treeData)}
        </Tbody>
      </Table>
    );
  };

  const title = !propIsModal
    ? "System Options"
    : propServerName
      ? propServerName + ' System Options'
      : propServerPoolName
        ? propServerPoolName + ' System Options'
        : "System Options";

  return (
    <GraphTalkComponent key = "SystemOptions" tabIndex = {-1} title = {title} onCompute = {computeSystemOptions}>
      <GraphTalkComponentDiv hasScroll>
        <SystemOptionsItems expandedRows = {expandedRows} toggleRow = {toggleRow}
        />
      </GraphTalkComponentDiv>
    </GraphTalkComponent>
  );
}

// ********************************************************************************************************

export const SystemOptionsButtonModal: React.FunctionComponent<Props> = (props) => {

  const [isSystemOptionsOpen, setIsSystemOptionsOpen] = useState(false);

  const handleSystemOptionsClick = () => {
    setIsSystemOptionsOpen(true);
  }

  const handleSystemOptionsClose = () => {
    setIsSystemOptionsOpen(false);
  }

  return (
    <Flex tabIndex = {-1}>
      <FlexItem tabIndex = {-1} onClick = {(e : any) => {e.stopPropagation();}}>
        <Button
          tabIndex  = {0}
          variant   = "plain"
          size      = "default"
          onClick   = {handleSystemOptionsClick}
          icon      = {<Icon status = "info" iconSize = "md" style = {{padding: "0px 0px 0px 0px"}}><PlusCircleIcon/></Icon>}
          style     = {{padding: "0px 0px 0px 0px"}}
        />
        <Modal
          isOpen            = {isSystemOptionsOpen}
          onClose           = {handleSystemOptionsClose}
          width             = "100%"
          height            = "100%"
          hasNoBodyWrapper  = {true}
          className         = "dialog"
          aria-label        = "SystemOptionsButtonModal"
        >
          <SystemOptions isModal = {true} {...props}/>
        </Modal>
      </FlexItem>
    </Flex>
  )
}

export default SystemOptions;
