## Counters

The Counters plugin displays information about GraphTalk Counters.

### Overview

The Counters page provides a detailed view of various counters related to GraphTalk. This page is useful for monitoring and analyzing the performance and usage of different components within the system.

### Table Description

The table on the Counters page displays the following information:

- **Server Pool**: The server pool to which the counter belongs.
- **Server Name**: The server name to which the counter belongs.
- **Counter Group**: The group to which the counter belongs.
- **Counter**: The specific counter being monitored.
- **Last Values**: The last recorded values for the counter.

Can expand or collapse each row to view more detailed information about each counter. Clicking on a row will expand it to show a graphical representation of the counter values over time.

Can click on the folder icons. The first one will expand and show all the information for the given Server Pool without the counter graphs. The second one will expand and show all the graphs for a given counter group.

### Graphs Description

The graphs provide a visual representation of the counter values in real time. Each graph shows the trend of the counter values, allowing to easily identify any spikes or drops in the values.

### Refresh Button

The refresh button allows to refresh the page.

### Auto-Refresh Button

The auto refresh button allows to automatically refresh the page at a specified interval. The refresh interval, in seconds, can be defined using the input field next to the auto refresh button. If don't activate auto refresh then counters are not displayed! With Auto Refresh, the counters values and the counters graphs are dynamically updated.

### Usage

1. **Viewing Counters**: Navigate to the Counters page to view the list of counters and their last recorded values.
2. **Expanding Rows**: Click on a row or on folder icon to expand and view the graphical representation of the counter values.
3. **Analyzing Trends**: Use the graphs to analyze the trends in the counter values in real time. Identify any spikes or drops that may require further investigation.

This detailed view helps in monitoring the system's performance and usage, allowing for timely identification and resolution of any issues.
