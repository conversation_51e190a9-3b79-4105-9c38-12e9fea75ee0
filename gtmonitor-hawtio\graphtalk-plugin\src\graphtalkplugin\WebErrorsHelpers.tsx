export const webErrorsItemsMap = [
    {key : "num",         header : "Number",          type : "int",           align : 'left',     padding : '0 0 0 10px'},
    {key : "userId",      header : "User",            type : "string",        align : 'left',     padding : '0 0 0 15px'},
    {key : "startDate",   header : "Date",            type : "julianDate",    align : 'center',   padding : '0 0 0 0'},
    {key : "startTime",   header : "Time",            type : "julianTime",    align : 'center',   padding : '0 0 0 0'},
    {key : "elapsed",     header : "Elapsed (ms)",    type : "ms",            align : 'right',    padding : '0 40px 0 0'},
    {key : "language",    header : "Language",        type : "string",        align : 'left',     padding : '0 0 0 5px'},
    {key : "level",       header : "Level",           type : "string",        align : 'left',     padding : '0 0 0 5px'}
  ];    
        
  export const formNumbersList = [
    { content: 'Default', value: 50 },
    { content: '10',      value: 10 },
    { content: '25',      value: 25 },
    { content: '50',      value: 50 },
    { content: '100',     value: 100 },
    { content: '200',     value: 200 },
    { content: '500',     value: 500 },
    { content: 'All',     value: Infinity }
  ]; 

  enum DeltaValue {
    SearchExpanded = -190,
    SearchCollapsed = -40,
  }

  export const getDeltaValue = (isSearchExpanded: boolean) => {
  
      return isSearchExpanded ? DeltaValue.SearchExpanded : DeltaValue.SearchCollapsed;
  };