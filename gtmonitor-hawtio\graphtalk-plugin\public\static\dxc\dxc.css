:root {
--dxc--color-white              : #ffffff;
--dxc--color-black              : #000000;

--dxc--color-cool-gray-1        : #d9d9d6;
--dxc--color-medium-gray        : #969696;
--dxc--color-dark-gray          : #63666a;

--dxc--color-bright-teal        : #00968f;
--dxc--color-dark-teal          : #006975;
--dxc--color-orange             : #ed9b33;
--dxc--color-bright-purple      : #5f249f;
--dxc--color-dark-purple        : #330072;
--dxc--color-dark-purple-100    : #3f0072;
--dxc--color-dark-purple-200    : #4c188a;
--dxc--color-dark-purple-300    : #4b0c7e;
--dxc--color-dark-purple-400    : #572496;
--dxc--color-blue               : #00a3e1;
--dxc--color-green              : #6cc24a;
--dxc--color-gold               : #ffcd00;
--dxc--color-yellow             : #f9f048;

--dxc--color-link-blue          : #0088c9;

--pf-global--BackgroundColor--dark-100: #151515;
--pf-global--BackgroundColor--dark-300: #212427;
--pf-global--BackgroundColor--dark-200: #3c3f42;
--pf-global--BackgroundColor--dark-400: #575255;


--dxc--color-primary            : var(--dxc--color-bright-purple);
--dxc--color-secondary-white    : var(--dxc--color-white);
--dxc--color-secondary-gray     : var(--dxc--color-cool-gray);
--dxc--color-neutral-medium     : var(--dxc--color-medium-gray);
--dxc--color-neutral-dark       : var(--dxc--color-dark-gray);
--dxc--color-neutral-black      : var(--dxc--color-black);
--dxc--color-accent-1           : var(--dxc--color-bright-teal);
--dxc--color-accent-2           : var(--dxc--color-dark-teal);
--dxc--color-accent-3           : var(--dxc--color-orange);
--dxc--color-accent-4           : var(--dxc--color-dark-purple);
--dxc--color-accent-5           : var(--dxc--color-blue);
--dxc--color-accent-6           : var(--dxc--color-green);
--dxc--color-accent-7           : var(--dxc--color-gold);
--dxc--color-accent-8           : var(--dxc--color-yellow);
}

:root {
  --pf-global--BackgroundColor--100                   : #fff;
  --pf-global--BackgroundColor--150                   : #fafafa;
  --pf-global--BackgroundColor--200                   : #f0f0f0;
  --pf-global--BackgroundColor--light-100             : #fff;
  --pf-global--BackgroundColor--light-200             : #fafafa;
  --pf-global--BackgroundColor--light-300             : #f0f0f0;
  --pf-global--BackgroundColor--dark-100              : var(--dxc--color-dark-purple-100);
  --pf-global--BackgroundColor--dark-200              : var(--dxc--color-dark-purple-200);
  --pf-global--BackgroundColor--dark-300              : var(--dxc--color-dark-purple-300);
  --pf-global--BackgroundColor--dark-400              : var(--dxc--color-dark-purple-400);
  --pf-global--BackgroundColor--dark-transparent-100  : rgba(3, 3, 3, 0.62);
  --pf-global--BackgroundColor--dark-transparent-200  : rgba(3, 3, 3, 0.32);
  --pf-global--Color--100                             : var(--dxc--color-dark-purple-100);
  --pf-global--Color--200                             : var(--dxc--color-dark-purple-200);
  --pf-global--Color--300                             : var(--dxc--color-dark-purple-300);
  --pf-global--Color--400                             : var(--dxc--color-dark-purple-400);
  --pf-global--Color--light-100                       : #fff;
  --pf-global--Color--light-200                       : #f0f0f0;
  --pf-global--Color--light-300                       : #d2d2d2;
  --pf-global--Color--dark-100                        : var(--dxc--color-dark-purple-100);
  --pf-global--Color--dark-200                        : var(--dxc--color-dark-purple-200);
  --pf-global--active-color--100                      : #06c;
  --pf-global--active-color--200                      : #bee1f4;
  --pf-global--active-color--300                      : #2b9af3;
  --pf-global--active-color--400                      : #73bcf7;
  --pf-global--disabled-color--100                    : #6a6e73;
  --pf-global--disabled-color--200                    : #d2d2d2;
  --pf-global--disabled-color--300                    : #f0f0f0;
  --pf-global--primary-color--100                     : #06c;
  --pf-global--primary-color--200                     : var(--dxc--color-dark-purple-200);
  --pf-global--primary-color--light-100               : #73bcf7;
  --pf-global--primary-color--dark-100                : #06c;
  --pf-global--secondary-color--100                   : var(--dxc--color-dark-purple-100);
  --pf-global--custom-color--100                      : #73c5c5;
  --pf-global--custom-color--200                      : #009596;
  --pf-global--custom-color--300                      : var(--dxc--color-dark-purple-300);
  --pf-global--success-color--100                     : #3e8635;
  --pf-global--success-color--200                     : #1e4f18;
  --pf-global--info-color--100                        : #2b9af3;
  --pf-global--info-color--200                        : var(--dxc--color-dark-purple-200);
  --pf-global--warning-color--100                     : #f0ab00;
  --pf-global--warning-color--200                     : #795600;
  --pf-global--danger-color--100                      : #c9190b;
  --pf-global--danger-color--200                      : #a30000;
  --pf-global--danger-color--300                      : #470000;
  --pf-global--link--Color                            : #06c;
  --pf-global--link--Color--hover                     : var(--dxc--color-dark-purple);
  --pf-global--link--Color--light                     : #2b9af3;
  --pf-global--link--Color--light--hover              : #73bcf7;
  --pf-global--link--Color--dark                      : #06c;
  --pf-global--link--Color--dark--hover               : var(--dxc--color-dark-purple);
  --pf-global--link--Color--visited                   : var(--dxc--color-dark-purple);
  --pf-global--BorderColor--100                       : #d2d2d2;
  --pf-global--BorderColor--200                       : var(--dxc--color-dark-purple-200);
  --pf-global--BorderColor--300                       : #f0f0f0;
  --pf-global--BorderColor--dark-100                  : #d2d2d2;
  --pf-global--BorderColor--light-100                 : #b8bbbe;
  --pf-global--icon--Color--light                     : var(--dxc--color-bright-purple);
  --pf-global--icon--Color--dark                      : var(--dxc--color-dark-purple);
  --pf-global--icon--Color--light--light              : #f0f0f0;
  --pf-global--icon--Color--dark--light               : #fff;
  --pf-global--icon--Color--light--dark               : var(--dxc--color-bright-purple);
  --pf-global--icon--Color--dark--dark                : var(--dxc--color-dark-purple);
}


:root {
/*
  --pf-v5-global--palette--black-600: var(--pf-global--palette--black-600);
  --pf-v5-global--palette--black-700: var(--pf-global--palette--black-700);
  --pf-v5-global--palette--black-800: var(--pf-global--palette--black-800);
  --pf-v5-global--palette--black-850: var(--pf-global--palette--black-850);
  --pf-v5-global--palette--black-900: var(--pf-global--palette--black-900);
  --pf-v5-global--palette--black-1000: var(--pf-global--palette--black-1000);
  --pf-v5-global--palette--blue-50: var(--pf-global--palette--blue-50);
  --pf-v5-global--palette--blue-100: var(--pf-global--palette--blue-100);
  --pf-v5-global--palette--blue-200: var(--pf-global--palette--blue-200);
  --pf-v5-global--palette--blue-300: var(--pf-global--palette--blue-300);
  --pf-v5-global--palette--blue-400: var(--pf-global--palette--blue-400);
  --pf-v5-global--palette--blue-500: var(--pf-global--palette--blue-500);
  --pf-v5-global--palette--blue-600: var(--pf-global--palette--blue-600);
  --pf-v5-global--palette--blue-700: var(--pf-global--palette--blue-700);
  --pf-v5-global--palette--cyan-50: var(--pf-global--palette--cyan-50);
  --pf-v5-global--palette--cyan-100: var(--pf-global--palette--cyan-100);
  --pf-v5-global--palette--cyan-200: var(--pf-global--palette--cyan-200);
  --pf-v5-global--palette--cyan-300: var(--pf-global--palette--cyan-300);
  --pf-v5-global--palette--cyan-400: var(--pf-global--palette--cyan-400);
  --pf-v5-global--palette--cyan-500: var(--pf-global--palette--cyan-500);
  --pf-v5-global--palette--cyan-600: var(--pf-global--palette--cyan-600);
  --pf-v5-global--palette--cyan-700: var(--pf-global--palette--cyan-700);
  --pf-v5-global--palette--gold-50: var(--pf-global--palette--gold-50);
  --pf-v5-global--palette--gold-100: var(--pf-global--palette--gold-100);
  --pf-v5-global--palette--gold-200: var(--pf-global--palette--gold-200);
  --pf-v5-global--palette--gold-300: var(--pf-global--palette--gold-300);
  --pf-v5-global--palette--gold-400: var(--pf-global--palette--gold-400);
  --pf-v5-global--palette--gold-500: var(--pf-global--palette--gold-500);
  --pf-v5-global--palette--gold-600: var(--pf-global--palette--gold-600);
  --pf-v5-global--palette--gold-700: var(--pf-global--palette--gold-700);
  --pf-v5-global--palette--green-50: var(--pf-global--palette--green-50);
  --pf-v5-global--palette--green-100: var(--pf-global--palette--green-100);
  --pf-v5-global--palette--green-200: var(--pf-global--palette--green-200);
  --pf-v5-global--palette--green-300: var(--pf-global--palette--green-300);
  --pf-v5-global--palette--green-400: var(--pf-global--palette--green-400);
  --pf-v5-global--palette--green-500: var(--pf-global--palette--green-500);
  --pf-v5-global--palette--green-600: var(--pf-global--palette--green-600);
  --pf-v5-global--palette--green-700: var(--pf-global--palette--green-700);
  --pf-v5-global--palette--light-blue-100: var(--pf-global--palette--light-blue-100);
  --pf-v5-global--palette--light-blue-200: var(--pf-global--palette--light-blue-200);
  --pf-v5-global--palette--light-blue-300: var(--pf-global--palette--light-blue-300);
  --pf-v5-global--palette--light-blue-400: var(--pf-global--palette--light-blue-400);
  --pf-v5-global--palette--light-blue-500: var(--pf-global--palette--light-blue-500);
  --pf-v5-global--palette--light-blue-600: var(--pf-global--palette--light-blue-600);
  --pf-v5-global--palette--light-blue-700: var(--pf-global--palette--light-blue-700);
  --pf-v5-global--palette--light-green-100: var(--pf-global--palette--light-green-100);
  --pf-v5-global--palette--light-green-200: var(--pf-global--palette--light-green-200);
  --pf-v5-global--palette--light-green-300: var(--pf-global--palette--light-green-300);
  --pf-v5-global--palette--light-green-400: var(--pf-global--palette--light-green-400);
  --pf-v5-global--palette--light-green-500: var(--pf-global--palette--light-green-500);
  --pf-v5-global--palette--light-green-600: var(--pf-global--palette--light-green-600);
  --pf-v5-global--palette--light-green-700: var(--pf-global--palette--light-green-700);
  --pf-v5-global--palette--orange-50: var(--pf-global--palette--orange-50);
  --pf-v5-global--palette--orange-100: var(--pf-global--palette--orange-100);
  --pf-v5-global--palette--orange-200: var(--pf-global--palette--orange-200);
  --pf-v5-global--palette--orange-300: var(--pf-global--palette--orange-300);
  --pf-v5-global--palette--orange-400: var(--pf-global--palette--orange-400);
  --pf-v5-global--palette--orange-500: var(--pf-global--palette--orange-500);
  --pf-v5-global--palette--orange-600: var(--pf-global--palette--orange-600);
  --pf-v5-global--palette--orange-700: var(--pf-global--palette--orange-700);
  --pf-v5-global--palette--purple-50: var(--pf-global--palette--purple-50);
  --pf-v5-global--palette--purple-100: var(--pf-global--palette--purple-100);
  --pf-v5-global--palette--purple-200: var(--pf-global--palette--purple-200);
  --pf-v5-global--palette--purple-300: var(--pf-global--palette--purple-300);
  --pf-v5-global--palette--purple-400: var(--pf-global--palette--purple-400);
  --pf-v5-global--palette--purple-500: var(--pf-global--palette--purple-500);
  --pf-v5-global--palette--purple-600: var(--pf-global--palette--purple-600);
  --pf-v5-global--palette--purple-700: var(--pf-global--palette--purple-700);
  --pf-v5-global--palette--red-50: var(--pf-global--palette--red-50);
  --pf-v5-global--palette--red-100: var(--pf-global--palette--red-100);
  --pf-v5-global--palette--red-200: var(--pf-global--palette--red-200);
  --pf-v5-global--palette--red-300: var(--pf-global--palette--red-300);
  --pf-v5-global--palette--red-400: var(--pf-global--palette--red-400);
  --pf-v5-global--palette--red-500: var(--pf-global--palette--red-500);
  --pf-v5-global--palette--white: var(--pf-global--palette--white);
*/
  --pf-v5-global--BackgroundColor--100: var(--pf-global--BackgroundColor--100);
  --pf-v5-global--BackgroundColor--150: var(--pf-global--BackgroundColor--150);
  --pf-v5-global--BackgroundColor--200: var(--pf-global--BackgroundColor--200);
  --pf-v5-global--BackgroundColor--light-100: var(--pf-global--BackgroundColor--light-100);
  --pf-v5-global--BackgroundColor--light-200: var(--pf-global--BackgroundColor--light-200);
  --pf-v5-global--BackgroundColor--light-300: var(--pf-global--BackgroundColor--light-300);
  --pf-v5-global--BackgroundColor--dark-100: var(--pf-global--BackgroundColor--dark-100);
  --pf-v5-global--BackgroundColor--dark-200: var(--pf-global--BackgroundColor--dark-200);
  --pf-v5-global--BackgroundColor--dark-300: var(--pf-global--BackgroundColor--dark-300);
  --pf-v5-global--BackgroundColor--dark-400: var(--pf-global--BackgroundColor--dark-400);
  --pf-v5-global--BackgroundColor--dark-transparent-100: var(--pf-global--BackgroundColor--dark-transparent-100);
  --pf-v5-global--BackgroundColor--dark-transparent-200: var(--pf-global--BackgroundColor--dark-transparent-200);
  --pf-v5-global--Color--100: var(--pf-global--Color--100);
  --pf-v5-global--Color--200: var(--pf-global--Color--200);
  --pf-v5-global--Color--300: var(--pf-global--Color--300);
  --pf-v5-global--Color--400: var(--pf-global--Color--400);
  --pf-v5-global--Color--light-100: var(--pf-global--Color--light-100);
  --pf-v5-global--Color--light-200: var(--pf-global--Color--light-200);
  --pf-v5-global--Color--light-300: var(--pf-global--Color--light-300);
  --pf-v5-global--Color--dark-100: var(--pf-global--Color--dark-100);
  --pf-v5-global--Color--dark-200: var(--pf-global--Color--dark-200);
  --pf-v5-global--active-color--100: var(--pf-global--active-color--100);
  --pf-v5-global--active-color--200: var(--pf-global--active-color--200);
  --pf-v5-global--active-color--300: var(--pf-global--active-color--300);
  --pf-v5-global--active-color--400: var(--pf-global--active-color--400);
  --pf-v5-global--disabled-color--100: var(--pf-global--disabled-color--100);
  --pf-v5-global--disabled-color--200: var(--pf-global--disabled-color--200);
  --pf-v5-global--disabled-color--300: var(--pf-global--disabled-color--300);
  --pf-v5-global--primary-color--100: var(--pf-global--primary-color--100);
  --pf-v5-global--primary-color--200: var(--pf-global--primary-color--200);
  --pf-v5-global--primary-color--light-100: var(--pf-global--primary-color--light-100);
  --pf-v5-global--primary-color--dark-100: var(--pf-global--primary-color--dark-100);
  --pf-v5-global--secondary-color--100: var(--pf-global--secondary-color--100);
  --pf-v5-global--custom-color--100: var(--pf-global--custom-color--100);
  --pf-v5-global--custom-color--200: var(--pf-global--custom-color--200);
  --pf-v5-global--custom-color--300: var(--pf-global--custom-color--300);
  --pf-v5-global--success-color--100: var(--pf-global--success-color--100);
  --pf-v5-global--success-color--200: var(--pf-global--success-color--200);
  --pf-v5-global--info-color--100: var(--pf-global--info-color--100);
  --pf-v5-global--info-color--200: var(--pf-global--info-color--200);
  --pf-v5-global--warning-color--100: var(--pf-global--warning-color--100);
  --pf-v5-global--warning-color--200: var(--pf-global--warning-color--200);
  --pf-v5-global--danger-color--100: var(--pf-global--danger-color--100);
  --pf-v5-global--danger-color--200: var(--pf-global--danger-color--200);
  --pf-v5-global--danger-color--300: var(--pf-global--danger-color--300);
  --pf-v5-global--link--Color: var(--pf-global--link--Color);
  --pf-v5-global--link--Color--hover: var(--pf-global--link--Color--hover);
  --pf-v5-global--link--Color--light: var(--pf-global--link--Color--light);
  --pf-v5-global--link--Color--light--hover: var(--pf-global--link--Color--light--hover);
  --pf-v5-global--link--Color--dark: var(--pf-global--link--Color--dark);
  --pf-v5-global--link--Color--dark--hover: var(--pf-global--link--Color--dark--hover);
  --pf-v5-global--link--Color--visited: var(--pf-global--link--Color--visited);
  --pf-v5-global--BorderColor--100: var(--pf-global--BorderColor--100);
  --pf-v5-global--BorderColor--200: var(--pf-global--BorderColor--200);
  --pf-v5-global--BorderColor--300: var(--pf-global--BorderColor--300);
  --pf-v5-global--BorderColor--dark-100: var(--pf-global--BorderColor--dark-100);
  --pf-v5-global--BorderColor--light-100: var(--pf-global--BorderColor--light-100);
  --pf-v5-global--icon--Color--light: var(--pf-global--icon--Color--light);
  --pf-v5-global--icon--Color--dark: var(--pf-global--icon--Color--dark);
  --pf-v5-global--icon--Color--light--light: var(--pf-global--icon--Color--light--light);
  --pf-v5-global--icon--Color--dark--light: var(--pf-global--icon--Color--dark--light);
  --pf-v5-global--icon--Color--light--dark: var(--pf-global--icon--Color--light--dark);
  --pf-v5-global--icon--Color--dark--dark: var(--pf-global--icon--Color--dark--dark);
}

:where(h1,h2,h3,h4) {
	color               : var(--dxc--color-dark-purple);
}

:where(p,a) {
	color: currentColor;
}

:where(*) {
  outline-color       : var(--dxc--color-dark-purple);
  caret-color         : var(--dxc--color-dark-purple);
}

.pf-c-background-image {
  --pf-c-background-image--BackgroundImage              : url(static/dxc/dxc_background_image_576_324.jpg)    !IMPORTANT;
  --pf-c-background-image--BackgroundImage-2x           : url(static/dxc/dxc_background_image_1152_648.jpg)   !IMPORTANT;
  --pf-c-background-image--BackgroundImage--sm          : url(static/dxc/dxc_background_image_768_432.jpg)    !IMPORTANT;
  --pf-c-background-image--BackgroundImage--sm-2x       : url(static/dxc/dxc_background_image_1536_864.jpg)   !IMPORTANT;
  --pf-c-background-image--BackgroundImage--lg          : url(static/dxc/dxc_background_image_2000_1125.jpg)  !IMPORTANT;
  --pf-c-background-image--Filter                       : url(#image_overlay)                                 !IMPORTANT;
}

.pf-v5-c-background-image {
  --pf-c-background-image--BackgroundImage              : url(static/dxc/dxc_background_image_576_324.jpg)    !IMPORTANT;
  --pf-c-background-image--BackgroundImage-2x           : url(static/dxc/dxc_background_image_1152_648.jpg)   !IMPORTANT;
  --pf-c-background-image--BackgroundImage--sm          : url(static/dxc/dxc_background_image_768_432.jpg)    !IMPORTANT;
  --pf-c-background-image--BackgroundImage--sm-2x       : url(static/dxc/dxc_background_image_1536_864.jpg)   !IMPORTANT;
  --pf-c-background-image--BackgroundImage--lg          : url(static/dxc/dxc_background_image_2000_1125.jpg)  !IMPORTANT;
  --pf-c-background-image--Filter                       : url(#image_overlay)                                 !IMPORTANT;
}

.pf-c-login__main-body .pf-c-form__label-text {
  color               : var(--dxc--color-dark-purple) !IMPORTANT;
}

.pf-c-login__main-body .pf-c-form__label-text {
  color               : var(--dxc--color-dark-purple) !IMPORTANT;
}

.pf-v5-c-login__main-body .pf-v5-c-form__label-text {
  color               : var(--dxc--color-dark-purple) !IMPORTANT;
}

.pf-v5-c-login__main-body .pf-v5-c-form__label-text {
  color               : var(--dxc--color-dark-purple) !IMPORTANT;
}

.pf-c-about-modal-box {
  --pf-c-about-modal-box--BackgroundColor               : var(--pf-global--BackgroundColor--dark-100)         !IMPORTANT;
  --pf-c-about-modal-box__hero--sm--BackgroundImage     : url(static/dxc/dxc_background_image_1984_1116.jpg)  !IMPORTANT;
  --pf-c-about-modal-box__hero--sm--BackgroundSize      : contain                                             !IMPORTANT;
  --pf-c-about-modal-box__hero--sm--BackgroundPosition  : center center                                       !IMPORTANT;
}

.pf-v5-c-about-modal-box {
  --pf-v5-c-about-modal-box--BackgroundColor               : var(--pf-global--BackgroundColor--dark-100)         !IMPORTANT;
  --pf-v5-c-about-modal-box__hero--sm--BackgroundImage     : url(static/dxc/dxc_background_image_1984_1116.jpg)  !IMPORTANT;
  --pf-v5-c-about-modal-box__hero--sm--BackgroundSize      : contain                                             !IMPORTANT;
  --pf-v5-c-about-modal-box__hero--sm--BackgroundPosition  : center center                                       !IMPORTANT;
}

.pf-c-about-modal-box h1,
.pf-c-about-modal-box h2,
.pf-c-about-modal-box h3,
.pf-c-about-modal-box h4,
.pf-c-about-modal-box h5,
.pf-c-about-modal-box h6,
.pf-c-about-modal-box a,
.pf-c-about-modal-box p {
  color               : var(--dxc--color-white)       !IMPORTANT;
}

.pf-v5-c-about-modal-box h1,
.pf-v5-c-about-modal-box h2,
.pf-v5-c-about-modal-box h3,
.pf-v5-c-about-modal-box h4,
.pf-v5-c-about-modal-box h5,
.pf-v5-c-about-modal-box h6,
.pf-v5-c-about-modal-box a,
.pf-v5-c-about-modal-box p {
  color               : var(--dxc--color-white)       !IMPORTANT;
}

.pf-c-about-modal-box .pf-c-button {
  color               : var(--dxc--color-white)       !IMPORTANT;
  background-color    : var(--dxc--color-dark-purple) !IMPORTANT;
}

.pf-v5-c-about-modal-box .pf-v5-c-button {
  color               : var(--dxc--color-white)       !IMPORTANT;
  background-color    : var(--dxc--color-dark-purple) !IMPORTANT;
}


.pf-v5-c-pagination__nav-page-select {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-c-pagination__nav-page-select {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-v5-c-label__text {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-c-label__text {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-v5-c-form__label-text {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-c-form__label-text {
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-v5-c-accordion__toggle {
  --pf-v5-c-accordion__toggle--m-expanded__toggle-text--FontWeight: normal         !IMPORTANT;
}

.pf-c-accordion__toggle {
  --pf-v5-c-accordion__toggle--m-expanded__toggle-text--FontWeight: normal         !IMPORTANT;
}

.pf-v5-c-accordion__toggle[aria-expanded="true"] {
  --pf-v5-c-accordion__toggle--m-expanded__toggle-text--Color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-c-accordion__toggle[aria-expanded="true"] {
  --pf-v5-c-accordion__toggle--m-expanded__toggle-text--Color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-v5-c-expandable-section__toggle-text{
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-c-expandable-section__toggle-text{
  color: var(--pf-v5-global--Color--dark-100)         !IMPORTANT;
}

.pf-v5-c-drawer.pf-m-inline > .pf-v5-c-drawer__main > .pf-v5-c-drawer__content {
  overflow-x: hidden         !IMPORTANT;
}

.pf-c-drawer.pf-m-inline > .pf-v5-c-drawer__main > .pf-v5-c-drawer__content {
  overflow-x: hidden         !IMPORTANT;
}

