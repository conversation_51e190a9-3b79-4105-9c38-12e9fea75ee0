{"name": "yarn", "installationMethod": "tar", "version": "1.22.21", "packageManager": "yarn@1.22.17", "license": "BSD-2-<PERSON><PERSON>", "preferGlobal": true, "description": "📦🐈 Fast, reliable, and secure dependency management.", "resolutions": {"sshpk": "^1.14.2"}, "engines": {"node": ">=4.0.0"}, "repository": "yarnpkg/yarn", "bin": {"yarn": "./bin/yarn.js", "yarnpkg": "./bin/yarn.js"}, "scripts": {"preinstall": ":; (node ./preinstall.js > /dev/null 2>&1 || true)"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"yarn": "file:"}}