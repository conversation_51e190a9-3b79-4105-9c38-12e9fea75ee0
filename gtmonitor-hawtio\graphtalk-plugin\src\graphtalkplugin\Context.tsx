
import { workspace, MBeanNode, MBeanTree, Logger } from '@hawtio/react'

// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-context");

const logPrefix = "** Context ** ";

//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Context Info
// ********************************************************************************************************
// ********************************************************************************************************

export async function getContext() {
  const mbeanTree : MBeanTree | null = await workspace.getTree();

  const regexJmxGtMonitor = /com\.csc\.gtmonitor\.([^.]+)/;
  const gtMonitorNode : MBeanNode | null = (mbeanTree === null) ? null : mbeanTree.find(node => node.name.match(regexJmxGtMonitor) !== null);

  const gtMonitorHawtioNode : MBeanNode | null = (gtMonitorNode === null || gtMonitorNode === undefined) ? null : gtMonitorNode.get("Hawtio", false);

  const canAccessGtMonitor                        : boolean = (gtMonitorHawtioNode !== null);
  const canAccessWebErrors                        : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveWebErrors");
  const canAccessBatchManager                     : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveBatchManager");
  const canAccessJobManager                       : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveJobManager");
  const canAccessJobManagerProcessor              : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveJobManagerProcessor");
  const canAccessCounters                         : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveCounters");
  const canAccessInfo                             : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveInfo");
  const canAccessEnvSections                      : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveEnvSections");

  const canDeleteWebError                         : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("deleteWebError");

  const canUpdateBatchManagerExecutionStatus      : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("updateBatchManagerExecutionStatus");
  const canDeleteBatchManagerExecution            : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("deleteBatchManagerExecution");

  const canDeleteJobManagerJob                    : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("deleteJobManagerJob");
  const canSuspendResumeJobManagerJob             : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("suspendResumeJobManagerJob");
  const canCancelJobManagerJob                    : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("deleteJobManagerJob");
  
  const canPauseJobManagerProcessor               : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("pauseJobManagerProcessor");
  const canResumeJobManagerProcessor              : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("resumeJobManagerProcessor");
  const canUpdateJobManagerSlaveCount             : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("updateJobManagerSlaveCount");
  const canKillJobManagerProcessorSlave           : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("killJobManagerProcessorSlave");

  const canComputeDigest                          : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("computeDigest");
  const canAccessRBACUsers                        : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveRBACUsers");
  const canAccessRBACPolicies                     : boolean = (gtMonitorHawtioNode === null) ? false : gtMonitorHawtioNode.hasInvokeRights("retrieveRBACPolicies");
  const canAccessSecurity                         : boolean = canComputeDigest || canAccessRBACUsers || canAccessRBACPolicies;

  const context = {
    mbeanTree                               : mbeanTree,

    gtMonitorNode                           : gtMonitorNode,
    gtMonitorHawtioNode                     : gtMonitorHawtioNode,

    canAccessGtMonitor                      : canAccessGtMonitor,
    canAccessWeberrors                      : canAccessWebErrors,
    canAccessBatchManager                   : canAccessBatchManager,
    canAccessJobManager                     : canAccessJobManager,
    canAccessJobManagerProcessor            : canAccessJobManagerProcessor,
    canAccessCounters                       : canAccessCounters,
    canAccessInfo                           : canAccessInfo,
    canAccessEnvSections                    : canAccessEnvSections,
    canAccessSecurity                       : canAccessSecurity,

    canDeleteWebError                       : canDeleteWebError,

    canUpdateBatchManagerExecutionStatus    : canUpdateBatchManagerExecutionStatus,
    canDeleteBatchManagerExecution          : canDeleteBatchManagerExecution,
   
    canDeleteJobManagerJob                  : canDeleteJobManagerJob,
    canSuspendResumeJobManagerJob           : canSuspendResumeJobManagerJob,
    canCancelJobManagerJob                  : canCancelJobManagerJob,

    canPauseJobManagerProcessor             : canPauseJobManagerProcessor,
    canResumeJobManagerProcessor            : canResumeJobManagerProcessor,
    canUpdateJobManagerSlaveCount           : canUpdateJobManagerSlaveCount,
    canKillJobManagerProcessorSlave         : canKillJobManagerProcessorSlave,

    canComputeDigest                        : canComputeDigest,
    canAccessRBACUsers                      : canAccessRBACUsers,
    canAccessRBACPolicies                   : canAccessRBACPolicies
  };

  log.debug(logPrefix, 'context: ', context);

  return context;
}
