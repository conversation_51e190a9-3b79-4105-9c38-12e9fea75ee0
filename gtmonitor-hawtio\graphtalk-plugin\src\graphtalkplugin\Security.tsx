import { IJolokiaSimple } from '@jolokia.js/simple'

import { Logger, jolokiaService } from '@hawtio/react'

import {
  Bullseye,
  Button,
  Flex, FlexItem,
  Form, FormGroup,
  Grid, GridItem,
  InputGroup, InputGroupItem,
  KeyTypes,
  Label,
  PageSection,
  Title,
  Spinner,
  Text, TextContent, TextVariants,
  TextInput, TextArea,
  Truncate
} from '@patternfly/react-core'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import { getContext } from './Context';

import React, { useEffect, useState } from 'react'

import EyeIcon from '@patternfly/react-icons/dist/esm/icons/eye-icon';
import EyeSlashIcon from '@patternfly/react-icons/dist/esm/icons/eye-slash-icon';

import './GraphTalk.css'


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-security");

const logPrefix = "** Security ** ";

//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Security
// ********************************************************************************************************
// ********************************************************************************************************

const Security: React.FunctionComponent = () => {

  const adminPath = "com.csc.gtmonitor.*:type=Hawtio";


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia: IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // RBAC Users and Roles
  // ********************************************************************************************************
  // ********************************************************************************************************

  const RBACUsers = () => {

    if (!context?.canAccessRBACUsers)
      return <></>

    const [userRoles, setUserRoles] = useState<Record<string, string[]>>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [expanded, setExpanded] = useState<boolean>(true);

    const toggleExpanded = () => {
      setExpanded(!expanded);
    }

    const isExpanded = () => {
      return expanded;
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleExpanded();
      }
    }

    useEffect(() => {
      const fetchUserRoles = async () => {
        await refreshJolokia();

        if (jolokia !== null) {
          try {
            const mbeans = await jolokia.search("com.csc.gtmonitor.*:type=Hawtio");
            if (mbeans !== null && mbeans.length > 0) {
              const result = await jolokia.execute(mbeans[0], "retrieveRBACUsers");
              log.debug(logPrefix, "UserRoles result:", result);
              const parsed = typeof result === "string" ? JSON.parse(result) : result;
              setUserRoles(parsed ?? {});
            }
          }
          catch (e) {
            log.debug(logPrefix, "UserRoles fetch failed:", e);
          }
          finally {
            setLoading(false);
          }
        }
      };

      fetchUserRoles();
    }, []);

    return (
      <PageSection>
        <Title
          tabIndex      = {0}
          headingLevel  = "h2"
          style         = {{cursor : "pointer", paddingLeft : "0px", paddingBottom : "14px"}}
          onKeyDown     = {handleKeyDown}
          onClick       = {() => toggleExpanded()}
        >
          Users
        </Title>
        {
          isExpanded() && (
          loading
          ?
          <Bullseye tabIndex = {-1} style = {{marginLeft : "6px"}}>
            <Spinner tabIndex = {-1} size = "xl" />
          </Bullseye>
          :
          Object.entries(userRoles)
            .sort(([userA, _rolesA], [userB, _rolesB]) => {
              if (userA < userB) return -1;
              if (userA > userB) return 1;
              return 0;
            })
            .map(([user, roles]) => (
              <Grid tabIndex = {-1} key = {user} style = {{paddingLeft : "6px", paddingBottom : "6px"}}>
                <GridItem tabIndex = {-1} span = {2}>
                  <TextContent tabIndex = {-1}>
                    <Text tabIndex = {-1} component = {TextVariants.p} style = {{fontSize : "14px", fontWeight : "bold"}}>
                      <Truncate tabIndex = {-1} content = {user}/>
                    </Text>
                  </TextContent>
                </GridItem>
                <GridItem tabIndex = {-1} span = {10}>
                  <Flex tabIndex = {-1} flexWrap = {{default : "wrap"}} gap = {{default : "gapSm"}}>
                  {
                    roles
                      .sort((roleA, roleB) => {
                        if (roleA < roleB) return -1;
                        if (roleA > roleB) return 1;
                        return 0;
                      })
                      .map((role, roleIndex) => (
                        <FlexItem tabIndex = {-1} key = {roleIndex}>
                          <Label tabIndex = {-1}>
                            <TextContent tabIndex = {-1}>
                              <Text tabIndex = {-1} className = "dxc-c-label">{role}</Text>
                            </TextContent>
                          </Label>
                        </FlexItem>
                      ))
                  }
                  </Flex>
                </GridItem>
              </Grid>
            ))
          )
        }
      </PageSection>
    );
  };


  // ********************************************************************************************************
  // ********************************************************************************************************
  // RBAC Roles and Policies
  // ********************************************************************************************************
  // ********************************************************************************************************

  const RBACPolicies = () => {

    if (!context?.canAccessRBACPolicies)
      return <></>

    const [policies, setPolicies] = useState<Record<string, string[]>>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [expanded, setExpanded] = useState<boolean>(true);

    const toggleExpanded = () => {
      setExpanded(!expanded);
    }

    const isExpanded = () => {
      return expanded;
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleExpanded();
      }
    }

    useEffect(() => {
      const fetchPolicies = async () => {
        await refreshJolokia();

        if (jolokia !== null) {
          try {
            const mbeans = await jolokia.search("com.csc.gtmonitor.*:type=Hawtio");
            if (mbeans !== null && mbeans.length > 0) {
              const result = await jolokia.execute(mbeans[0], "retrieveRBACPolicies");
              log.debug(logPrefix, "Policieis result:", result);
              const parsed = typeof result === "string" ? JSON.parse(result) : result;
              setPolicies(parsed ?? {});
            }
          }
          catch (e) {
            log.debug(logPrefix, "Policies fetch failed:", e);
          }
          finally {
            setLoading(false);
          }
        }
      };

      fetchPolicies();
    }, []);

    return (
      <PageSection tabIndex = {-1}>
        <Title
          tabIndex      = {0}
          headingLevel  = "h2"
          style         = {{cursor : "pointer", paddingLeft : "0px", paddingBottom : "14px"}}
          onKeyDown     = {handleKeyDown}
          onClick       = {() => toggleExpanded()}
        >
          Policies
        </Title>
        {
          isExpanded() && (
          loading
          ?
          <Bullseye tabIndex = {-1} style = {{marginLeft : "6px"}}>
            <Spinner tabIndex = {-1} size = "xl" />
          </Bullseye>
          :
          Object.entries(policies)
            .sort(([roleA, _rolePoliciesA], [roleB, _rolePoliciesB]) => {
              if (roleA < roleB) return -1;
              if (roleA > roleB) return 1;
              return 0;
            })
            .map(([role, rolePolicies]) => (
              <Grid tabIndex = {-1} key = {role} style = {{marginBottom : "7px", marginLeft : "6px"}}>
                <GridItem tabIndex = {-1} span = {2}>
                  <TextContent tabIndex = {-1}>
                    <Text tabIndex = {-1} component = {TextVariants.p} style = {{fontSize : "14px", fontWeight : "bold"}}>
                      <Truncate tabIndex = {-1} content = {role}/>
                    </Text>
                  </TextContent>
                </GridItem>
                <GridItem tabIndex = {-1} span = {10}>
                {
                  rolePolicies.map((rolePolicy : any, rolePolicyIndex : number) => (
                    <Grid tabIndex = {-1} key = {rolePolicyIndex}>
                      <GridItem tabIndex = {-1} span = {1}>
                        <TextContent tabIndex = {-1}>
                          <Text tabIndex = {-1} component = {TextVariants.p} style = {{fontSize : "12px"}}>{rolePolicy.update ? "update" : rolePolicy.view ? "view" : "none"}</Text>
                        </TextContent>
                      </GridItem>
                      <GridItem tabIndex = {-1} span = {11}>
                        <TextContent tabIndex = {-1}>
                          <Text tabIndex = {-1} component = {TextVariants.p} style = {{fontSize : "12px"}}>
                            <Truncate tabIndex = {-1} content = {rolePolicy.item}/>
                          </Text>
                        </TextContent>
                      </GridItem>
                    </Grid>
                  ))
                }
                </GridItem>
              </Grid>
            ))
          )
        }
      </PageSection>
    );
  };


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Password Digest
  // ********************************************************************************************************
  // ********************************************************************************************************

  const PasswordDigest = () => {

    if (!context?.canComputeDigest)
      return <></>

    const [password, setPassword] = useState<string>("");
    const [digest, setDigest] = useState<string>("");
    const [passwordHidden, setPasswordHidden] = useState<boolean>(true);
    const [submitPassword, setSubmitPassword] = useState<boolean>(false);
    const [expanded, setExpanded] = useState<boolean>(true);

    const toggleExpanded = () => {
      setExpanded(!expanded);
    }

    const isExpanded = () => {
      return expanded;
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleExpanded();
      }
    }

    const computeDigestSignature = "computeDigest(java.lang.String)";

    async function computeDigest(password: string): Promise<any> {
      await refreshJolokia();

      if (jolokia !== null) {
        try {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            log.debug(logPrefix, "computeDigest, args: ", password);
            const digest = await jolokia.execute(mbean, computeDigestSignature, password);
            log.debug(logPrefix, "computeDigest, result: ", digest);
            if (digest !== null)
              log.debug(logPrefix, "computeDigest, json: ", digest);
            else
              log.debug(logPrefix, "computeDigest, cannot compute digest");
            return digest;
          }
        }
        catch (e) {
          log.error(logPrefix, "computeDigest, exception: ", e);
        }
      }

      return null;
    }

    useEffect(() => {
      if (password !== undefined && password !== null && password !== "")
        computeDigest(password)
          .then((digest: any) => { setDigest(digest ? digest : "") })
          .catch (error => {
            setDigest("cannot compute digest");
          })
          .finally(() => { })
      else
        setDigest("");
    }, [submitPassword])

    return (
      <PageSection tabIndex = {-1}>
        <Title
          tabIndex      = {0}
          headingLevel  = "h2"
          style         = {{cursor : "pointer", paddingLeft : "0px", paddingBottom : "14px"}}
          onKeyDown     = {handleKeyDown}
          onClick       = {() => toggleExpanded()}
        >
          Password Digest
        </Title>
        {
          isExpanded() &&
          <Form tabIndex = {-1} style = {{marginLeft : "6px"}}>
            <FormGroup tabIndex = {-1} label = "Password" isRequired>
              <InputGroup tabIndex = {-1}>
                <InputGroupItem tabIndex={-1} isFill>
                  <TextInput
                    tabIndex    = {0}
                    id          = "password"
                    type        = {passwordHidden ? "password" : "text"}
                    value       = {password}
                    onChange    = {(_event, value) => { setPassword(value ? value : ""); if (digest !== "") setDigest(""); }}
                    aria-label  = "input password"
                  />
                </InputGroupItem>
                <InputGroupItem tabIndex = {-1}>
                  <Button
                    tabIndex    = {0}
                    variant     = "control"
                    onClick     = {() => setPasswordHidden(!passwordHidden)}
                    aria-label  = {passwordHidden ? "Show password" : "Hide password"}
                    icon        = {passwordHidden ? <EyeIcon/> : <EyeSlashIcon/>}
                  />
                </InputGroupItem>
              </InputGroup>
              <InputGroup tabIndex = {-1}>
                  <Button
                    tabIndex    = {0}
                    variant     = "primary"
                    size        = "sm"
                    onClick     = {() => setSubmitPassword(!submitPassword)}
                    aria-label  = "submit"
                  >
                  Compute Digest
                </Button>
              </InputGroup>
            </FormGroup>
            <FormGroup label = "Digest">
              <TextArea
                tabIndex          = {0}
                id                = "digest"
                value             = {digest}
                resizeOrientation = "vertical"
                autoResize        = {true}
                readOnlyVariant   = "default"
                aria-label        = "digest"
              />
            </FormGroup>
          </Form>
       }
      </PageSection>
    )
    
  }

  // ********************************************************************************************************
  // ********************************************************************************************************

  const [context, setContext] = useState<any | null>(null);

  async function retrieveContext() {
    const context = await getContext();
    setContext(context);
    log.debug(logPrefix, "context: ", context);
  }

  // ********************************************************************************************************

  async function computeSecurity() {
    await retrieveContext();
  }

  // ********************************************************************************************************

  return (
    <GraphTalkComponent tabIndex = {-1} title = "Security" hasRefresh = {false} onCompute = {computeSecurity}>
      <GraphTalkComponentDiv hasScroll>
        <PasswordDigest/>
        <RBACUsers/>
        <RBACPolicies/>
      </GraphTalkComponentDiv>
    </GraphTalkComponent>

  )
}

export default Security;
