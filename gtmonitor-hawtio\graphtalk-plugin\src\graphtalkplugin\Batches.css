  .detach-icon-color{
    background-color: var(--pf-v5-global--Color--dark-100);
  }

  .multi-line-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    white-space: nowrap;
  }

  .pf-v5-c-menu-toggle.pf-m-plain {
    font-size: 13px !important; 
  }

  .custom-compact-info-table {
    line-height: 1;
  }

  .pf-v5-c-accordion__toggle-text {
    font-size: 14px;
  }

  .pf-v5-c-expandable-section__toggle-text{
    font-size: 14px;
  }

  .day_summary_style{
    margin-left: 0px;
    position: absolute;
    right: 10px;
    font-size: 14px;
    white-space: nowrap;
  }

  .info_tables_body_style {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: auto;
  }
  
  .slices_table_style {
    font-size: 12px !important;
    padding: 4px 10px !important;
    text-overflow: clip !important;
    text-align: right !important;
  }
  
  .errors_table_style {
    font-size: 12px !important;
    padding: 4px 30px !important;
    text-align: right !important;
    text-overflow: clip !important;
  }

  .pf-v5-c-drawer__body{
    margin: 0;
    padding: 0;
  }

  .table-scroll-container {
    overflow-y: auto;
    max-height: 100%;
  }

  .flex-column-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }