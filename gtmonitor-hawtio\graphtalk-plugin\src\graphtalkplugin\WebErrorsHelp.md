## Web Errors

The Web Errors plugin displays information about GraphTalk Web Errors

### Search Criteria

The search criteria allows to search for specific batches using various criteria:

- **Number of errors**: Can filter errors by the number.
- **No**: Can search for errors by their unique number.
- **User**: Can search for errors by their user.
- **Date between**: Can filter errors between two dates.

### List of Web Errors

The table displays the following information about each web error:

- **Number**: The unique number of the web error.
- **User**: The user of the web error.
- **Date**: The date of the web error.
- **Time**: The time when of the web error.
- **Elapsed (ms)**: Elapsed time in milliseconds.
- **Language**: The language.
- **Level**: The level of the web error.

The table can be sorted by clicking on the column headers and filtered using the search section.

### Refresh Button

The refresh button allows to refresh the page.

### Auto-Refresh Button

The auto refresh button allows to automatically refresh the page at a specified interval. The refresh interval, in seconds, can be defined using the input field next to the auto refresh button. 

### Search Button

The search button allows to search for web errors according to the set criteria.

### Reset Button

The reset button allows to refresh the list of web errors. Clicking the reset button will update the list of web errorsto reflect the current state.

### Previous button

The previous button allows to see the previous date on which there were web errors.

### Next button

The next button allows to see the next date on which there were web errors.

### Today button

The today button allows to see if there are web errors on today's date.

### Error Details Tab

The error details tab allows to view detailed information about the error. Clicking on the еrror details tab will display a detailed view of the error.

### Related Request Tab

The related request tab allows to view raw content of the request that triggered this error

### Error Properties Tab

The error properties tab allows to view information about the properties of the error.