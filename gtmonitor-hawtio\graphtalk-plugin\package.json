{"name": "graphtalk-plugin", "version": "99.0.00", "description": "Hawtio GraphTalk plugin", "main": "./src/main/webapp/app/graphtalkplugin.js", "private": true, "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "devbuild": "webpack --mode development", "test": "jest --passWithNoTests", "replace-version": "replace __PACKAGE_VERSION_PLACEHOLDER__ $npm_package_version ./build/static/js -r --include=\"*.js\""}, "dependencies": {"@babel/helpers": "^7.27.0", "@babel/runtime": "^7.27.0", "@hawtio/react": "~1.6", "@patternfly/patternfly": "~5.4", "@patternfly/react-charts": "~7.4", "@patternfly/react-core": "~5.4", "@patternfly/react-icons": "~5.4", "@patternfly/react-table": "~5.4", "@types/react": "~18.3", "@types/react-dom": "~18.3", "axios": "^1.8.4", "cross-spawn": "7.0.5", "http-proxy-middleware": "2.0.9", "nanoid": "^3.3.11", "path-to-regexp": "0.1.12", "react": "~18.3", "react-dom": "~18.3", "react-router-dom": "^7.5.2", "tslib": "~2.8"}, "devDependencies": {"@babel/cli": "^7.26.10", "@babel/core": "^7.26.10", "@babel/helpers": "^7.27.0", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.10", "@babel/runtime": "^7.27.0", "@cyclonedx/yarn-plugin-cyclonedx": "^3.0.2", "@hawtio/backend-middleware": "^1.0.5", "@jolokia.js/simple": "^2.1.7", "@swc/core": "~1.7", "@yarnpkg/pnpify": "~4.1", "@yarnpkg/sdks": "~3.2", "body-parser": "^1.20.3", "copy-webpack-plugin": "~12.0", "css-loader": "~7.1", "file-selector": "~1.2", "html-webpack-plugin": "~5.6", "jest": "~29.7", "jolokia.js": "^2.1.7", "mini-css-extract-plugin": "~2.9", "path-browserify": "~1.0", "qs": "^6.13.0", "replace": "~1.2", "style-loader": "~4.0", "swc-loader": "~0.2", "typescript": "~5.5", "webpack": "~5.99", "webpack-cli": "~6.0", "webpack-dev-server": "~5.2"}, "resolutions": {"typescript": "~5.5", "axios": "1.8.4", "nanoid": "3.3.11", "cross-spawn": "7.0.5", "path-to-regexp": "0.1.12", "react-router-dom": "7.5.2", "http-proxy-middleware": "2.0.9", "@babel/helpers": "7.27.0", "@babel/runtime": "7.27.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.3.1"}