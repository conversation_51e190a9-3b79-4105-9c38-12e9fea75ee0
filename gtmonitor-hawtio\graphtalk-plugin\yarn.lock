# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@adobe/css-tools@npm:^4.4.0":
  version: 4.4.3
  resolution: "@adobe/css-tools@npm:4.4.3"
  checksum: 10/701379c514b7a43ca6681705a93cd57ad79565cfef9591122e9499897550cf324a5e5bb1bc51df0e7433cf0e91b962c90f18ac459dcc98b2431daa04aa63cb20
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/f3451525379c68a73eb0a1e65247fbf28c0cccd126d93af21c75fceff77773d43c0d4a2d51978fb131aff25b5f2cb41a9fe48cc296e61ae65e679c4f6918b0ab
  languageName: node
  linkType: hard

"@arcanis/slice-ansi@npm:^1.1.1":
  version: 1.1.1
  resolution: "@arcanis/slice-ansi@npm:1.1.1"
  dependencies:
    grapheme-splitter: "npm:^1.0.4"
  checksum: 10/14ed60cb45750d386c64229ac7bab20e10eedc193503fa4decff764162d329d6d3363ed2cd3debec833186ee54affe4f824f6e8eff531295117fd1ebda200270
  languageName: node
  linkType: hard

"@babel/cli@npm:^7.26.10":
  version: 7.27.2
  resolution: "@babel/cli@npm:7.27.2"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    "@nicolo-ribaudo/chokidar-2": "npm:2.1.8-no-fsevents.3"
    chokidar: "npm:^3.6.0"
    commander: "npm:^6.2.0"
    convert-source-map: "npm:^2.0.0"
    fs-readdir-recursive: "npm:^1.1.0"
    glob: "npm:^7.2.0"
    make-dir: "npm:^2.1.0"
    slash: "npm:^2.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  dependenciesMeta:
    "@nicolo-ribaudo/chokidar-2":
      optional: true
    chokidar:
      optional: true
  bin:
    babel: ./bin/babel.js
    babel-external-helpers: ./bin/babel-external-helpers.js
  checksum: 10/00fcb58170173d2e501cc9f43c8a1fd2266085cdbb005287164068e6fe566be1a11e885efe3abdeff6f928cb1c84d5c31aa3c50e7b6b3647bc47ce3a231e9d1b
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.10.4, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10/721b8a6e360a1fa0f1c9fe7351ae6c874828e119183688b533c477aa378f1010f37cc9afbfc4722c686d1f5cdd00da02eab4ba7278a0c504fa0d7a321dcd4fdf
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10/04c343b8a25955bbbe1569564c63ac481a74710eb2e7989b97bd10baf2f0f3b1aa1b6c6122749806e92d70cfc22c10c757ff62336eb10a28ea98ab2b82bc0c2c
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.23.9, @babel/core@npm:^7.26.10":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/28c01186d5f2599e41f92c94fd14a02cfdcf4b74429b4028a8d16e45c1b08d3924c4275e56412f30fcd2664e5ddc2200f1c06cee8bffff4bba628ff1f20c6e70
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3, @babel/generator@npm:^7.7.2":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/f5e6942670cb32156b3ac2d75ce09b373558823387f15dd1413c27fe9eb5756a7c6011fc7f956c7acc53efb530bfb28afffa24364d46c4e9ffccc4e5c8b3b094
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10/63863a5c936ef82b546ca289c9d1b18fabfc24da5c4ee382830b124e2e79b68d626207febc8d4bffc720f50b2ee65691d7d12cc0308679dee2cd6bdc926b7190
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/bd53c30a7477049db04b655d11f4c3500aea3bcbc2497cf02161de2ecf994fec7c098aabbcebe210ffabc2ecbdb1e3ffad23fb4d3f18723b814f423ea1749fe8
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/701579b49046cd42f6a6b1e693e6827df8623185adf0911c4d68a219a082d8fd4501672880d92b6b96263d1c92a3beb891b3464a662a55e69e7539d8db9277da
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/dea272628cd8874f127ab7b2ee468620aabc1383d38bb40c49a9c7667db2258cdfe6620a1d1412f5f0706583f6301b4b7ad3d5932f24df7fe72e66bf9bc0be45
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/dc2ebdd7bc880fff8cd09a5b0bd208e53d8b7ea9070f4b562dd3135ea6cd68ef80cf4a74f40424569a00c00eabbcdff67b2137a874c4f82f3530246dad267a3b
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/533a5a2cf1c9a8770d241b86d5f124c88e953c831a359faf1ac7ba1e632749c1748281b83295d227fe6035b202d81f3d3a1ea13891f150c6538e040668d6126a
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/58e792ea5d4ae71676e0d03d9fef33e886a09602addc3bd01388a98d87df9fcfd192968feb40ac4aedb7e287ec3d0c17b33e3ecefe002592041a91d8a1998a8d
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/47abc90ceb181b4bdea9bf1717adf536d1b5e5acb6f6d8a7a4524080318b5ca8a99e6d58677268c596bad71077d1d98834d2c3815f2443e6d3f287962300f15d
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10/0fb7ee824a384529d6b74f8a58279f9b56bfe3cce332168067dddeab2552d8eeb56dc8eaf86c04a3a09166a316cb92dfc79c4c623cd034ad4c563952c98b464f
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10/96136c2428888e620e2ec493c25888f9ceb4a21099dcf3dd4508ea64b58cdedbd5a9fb6c7b352546de84d6c24edafe482318646932a22c449ebd16d16c22d864
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/0747397ba013f87dbf575454a76c18210d61c7c9af0f697546b4bcac670b54ddc156330234407b397f0c948738c304c228e0223039bc45eab4fbf46966a5e8cc
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/72e3f8bef744c06874206bf0d80a0abbedbda269586966511c2491df4f6bf6d47a94700810c7a6737345a545dfb8295222e1e72f506bcd0b40edb3f594f739ea
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/4f380c5d0e0769fa6942a468b0c2d7c8f0c438f941aaa88f785f8752c103631d0904c7b4e76207a3b0e6588b2dec376595370d92ca8f8f1b422c14a69aa146d4
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10/0ae29cc2005084abdae2966afdb86ed14d41c9c37db02c3693d5022fba9f5d59b011d039380b8e537c34daf117c549f52b452398f576e908fb9db3c7abbb3a00
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10/75041904d21bdc0cd3b07a8ac90b11d64cd3c881e89cb936fa80edd734bf23c35e6bd1312611e8574c4eab1f3af0f63e8a5894f4699e9cfdf70c06fcf4252320
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10/db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/effa5ba1732764982db52295a0003d0d6b527edf70d8c649f5a521808decbc47fc8f3c21cd31f7b6331192289f3bf5617141bce778fec45dcaedf5708d9c3140
  languageName: node
  linkType: hard

"@babel/helpers@npm:7.27.0":
  version: 7.27.0
  resolution: "@babel/helpers@npm:7.27.0"
  dependencies:
    "@babel/template": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
  checksum: 10/0dd40ba1e5ba4b72d1763bb381384585a56f21a61a19dc1b9a03381fe8e840207fdaa4da645d14dc028ad768087d41aad46347cc6573bd69d82f597f5a12dc6f
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/0ad671be7994dba7d31ec771bd70ea5090aa34faf73e93b1b072e3c0a704ab69f4a7a68ebfb9d6a7fa455e0aa03dfa65619c4df6bae1cf327cba925b1d233fc4
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/fe65257d5b82558bc6bc0f3a5a7a35b4166f71bed3747714dafb6360fadb15f036d568bc1fbeedae819165008c8feb646633ab91c0e3a95284963972f4fa9751
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/eb7f4146dc01f1198ce559a90b077e58b951a07521ec414e3c7d4593bf6c4ab5c2af22242a7e9fec085e20299e0ba6ea97f44a45e84ab148141bf9eb959ad25e
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/621cfddfcc99a81e74f8b6f9101fd260b27500cb1a568e3ceae9cc8afe9aee45ac3bca3900a2b66c612b1a2366d29ef67d4df5a1c975be727eaad6906f98c2c6
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 10/f07aa80272bd7a46b7ba11a4644da6c9b6a5a64e848dfaffdad6f02663adefd512e1aaebe664c4dd95f7ed4f80c872c7f8db8d8e34b47aae0930b412a28711a0
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/dfa68da5f68c0fa9deff1739ac270a5643ea07540b26a2a05403bc536c96595f0fe98a5eac9f9b3501b79ce57caa3045a94c75d5ccbfed946a62469a370ecdc2
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fab70f399aa869275690ec6c7cedb4ef361d4e8b6f55c3d7b04bfee61d52fb93c87cec2c65d73cddbaca89fb8ef5ec0921fce675c9169d9d51f18305ab34e78a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3a10849d83e47aec50f367a9e56a6b22d662ddce643334b087f9828f4c3dd73bdc5909aaeabe123fed78515767f9ca43498a0e621c438d1cd2802d7fae3c9648
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fb661d630808d67ecb85eabad25aac4e9696a20464bad4c4a6a0d3d40e4dc22557d47e9be3d591ec06429cf048cfe169b8891c373606344d51c4f3ac0f91d6d0
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7, @babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/97973982fff1bbf86b3d1df13380567042887c50e2ae13a400d02a8ff2c9742a60a75e279bfb73019e1cd9710f04be5e6ab81f896e6678dcfcec8b135e8896cf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1, @babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/c6d1324cff286a369aa95d99b8abd21dd07821b5d3affd5fe7d6058c84cff9190743287826463ee57a7beecd10fa1e4bc99061df532ee14e188c1c8937b13e3a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1, @babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/87836f7e32af624c2914c73cd6b9803cf324e07d43f61dbb973c6a86f75df725e12540d91fac7141c14b697aa9268fd064220998daced156e96ac3062d7afb41
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/62c2cc0ae2093336b1aa1376741c5ed245c0987d9e4b4c5313da4a38155509a7098b5acce582b6781cc0699381420010da2e3086353344abe0a6a0ec38961eb7
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/92e8ba589e8b128255846375e13fee30a3b77c889578f1f30da57ee26133f397dbbc81b27e1f19c12080b096930e62bce1dcbaa7a1453d296f51eb8bda3b8d39
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/d79d7a7ae7d416f6a48200017d027a6ba94c09c7617eea8b4e9c803630f00094c1a4fc32bf20ce3282567824ce3fcbda51653aac4003c71ea4e681b331338979
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7fb4988ca80cf1fc8345310d5edfe38e86b3a72a302675cdd09404d5064fe1d1fe1283ebe658ad2b71445ecef857bfb29a748064306b5f6c628e0084759c2201
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/bc911f0aa15bc9a5e0e1130681c1a6abd05300f6c8c02af9c97b0eaaae43b0f2936b34a5efc1a166a8e296c421c574a0e04dd0d6dc62adaab1246a387e6cfe26
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/475a6e5a9454912fe1bdc171941976ca10ea4e707675d671cdb5ce6b6761d84d1791ac61b6bca81a2e5f6430cb7b9d8e4b2392404110e69c28207a754e196294
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-static-block@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 10/2d49de0f5ffc66ae873be1d8c3bf4d22e51889cc779d534e4dbda0f91e36907479e5c650b209fcfc80f922a6c3c2d76c905fc2f5dc78cc9a836f8c31b10686c4
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/4ac2224fa68b933c80b4755300d795e055f6fb18c51432e9a4c048edcd6c64cae097eb9063d25f6c7e706ecd85a4c0b89b6f89b320b5798e3139c9cc4ff99f61
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/101f6d4575447070943d5a9efaa5bea8c552ea3083d73a9612f1a16d38b0a0a7b79a5feb65c6cc4e4fcabf28e85a570b97ccd3294da966e8fbbb6dfb97220eda
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.27.1, @babel/plugin-transform-destructuring@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/d5b1868d079551c0a2e923419613efe18a987548219bb378c61ab7e005d4f3ea590067f93996df6d896177c1cae1396b4aae9163c8a4ee77e9ffbc11a78fb88d
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2173e5b13f403538ffc6bd57b190cedf4caf320abc13a99e5b2721864e7148dbd3bd7c82d92377136af80432818f665fdd9a1fd33bc5549a4c91e24e5ce2413c
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/987b718d2fab7626f61b72325c8121ead42341d6f46ad3a9b5e5f67f3ec558c903f1b8336277ffc43caac504ce00dd23a5456b5d1da23913333e1da77751f08d
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/2a109613535e6ac79240dced71429e988affd6a5b3d0cd0f563c8d6c208c51ce7bf2c300bc1150502376b26a51f279119b3358f1c0f2d2f8abca3bcd62e1ae46
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7a9fbc8d17148b7f11a1d1ca3990d2c2cd44bd08a45dcaf14f20a017721235b9044b20e6168b6940282bb1b48fb78e6afbdfb9dd9d82fde614e15baa7d579932
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/dbbedd24724c2d590ef59d32cb1fef34e99daba41c5b621f9f4c4da23e15c2bb4b1e3d954c314645016391404cf00f1e4ddec8f1f7891438bcde9aaf16e16ee0
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/85082923eca317094f08f4953d8ea2a6558b3117826c0b740676983902b7236df1f4213ad844cb38c2dae104753dbe8f1cc51f01567835d476d32f5f544a4385
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/705c591d17ef263c309bba8c38e20655e8e74ff7fd21883a9cdaf5bf1df42d724383ad3d88ac01f42926e15b1e1e66f2f7f8c4e87de955afffa290d52314b019
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/26a2a183c3c52a96495967420a64afc5a09f743a230272a131668abf23001e393afa6371e6f8e6c60f4182bea210ed31d1caf866452d91009c1daac345a52f23
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2c05a02f63b49f47069271b3405a66c3c8038de5b995b0700b1bd9a5e2bb3e67abd01e4604629302a521f4d8122a4233944aefa16559fd4373d256cc5d3da57f
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/0a76d12ab19f32dd139964aea7da48cecdb7de0b75e207e576f0f700121fe92367d788f328bf4fb44b8261a0f605c97b44e62ae61cddbb67b14e94c88b411f95
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/2757955d81d65cc4701c17b83720745f6858f7a1d1d58117e379c204f47adbeb066b778596b6168bdbf4a22c229aab595d79a9abc261d0c6bfd62d4419466e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/804121430a6dcd431e6ffe99c6d1fbbc44b43478113b79c677629e7f877b4f78a06b69c6bfb2747fd84ee91879fe2eb32e4620b53124603086cf5b727593ebe8
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/5ca9257981f2bbddd9dccf9126f1368de1cb335e7a5ff5cca9282266825af5b18b5f06c144320dcf5d2a200d2b53b6d22d9b801a55dc0509ab5a5838af7e61b7
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/9059243a977bc1f13e3dccfc6feb6508890e7c7bb191f7eb56626b20672b4b12338051ca835ab55426875a473181502c8f35b4df58ba251bef63b25866d995fe
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/06d7bf76ac4688a36ae8e8d2dde1c3b8bab4594362132b74a00d5a32e6716944d68911b9bc53df60e59f4f9c7f1796525503ce3e3eed42f842d7775ccdfd836e
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7388932863b4ee01f177eb6c2e2df9e2312005e43ada99897624d5565db4b9cef1e30aa7ad2c79bbe5373f284cfcddea98d8fe212714a24c6aba223272163058
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/a711c92d9753df26cefc1792481e5cbff4fe4f32b383d76b25e36fa865d8023b1b9aa6338cf18f5c0e864c71a7fbe8115e840872ccd61a914d9953849c68de7d
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/620d78ee476ae70960989e477dc86031ffa3d554b1b1999e6ec95261629f7a13e5a7b98579c63a009f9fdf14def027db57de1f0ae1f06fb6eaed8908ff65cf68
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/15333f4888ffedc449a2a21a0b1ca7983e089f43faa00cfb71d2466e20221a5fd979cdb1a3f57bc20fc62c67bd3ff3dde054133fb6324a58be8f64d20aefacd2
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/049b958911de86d32408cd78017940a207e49c054ae9534ab53a32a57122cc592c0aae3c166d6f29bd1a7d75cc779d71883582dd76cb28b2fbb493e842d8ffca
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.27.2":
  version: 7.27.3
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.27.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.3"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7cc7be29a99010aac04fd78383f06d550b26460ea5367489e58ae484f0ed2f176966f0196bea0c2114a9872dd854a482bca38a9fad661c9d10d102c7195d53fd
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/46b819cb9a6cd3cfefe42d07875fee414f18d5e66040366ae856116db560ad4e16f3899a0a7fddd6773e0d1458444f94b208b67c0e3b6977a27ea17a5c13dbf6
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/f4356b04cf21a98480f9788ea50f1f13ee88e89bb6393ba4b84d1f39a4a84c7928c9a4328e8f4c5b6deb218da68a8fd17bf4f46faec7653ddc20ffaaa5ba49f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/34b0f96400c259a2722740d17a001fe45f78d8ff052c40e29db2e79173be72c1cfe8d9681067e3f5da3989e4a557402df5c982c024c18257587a41e022f95640
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/47db574f8f3adf7a5d85933c9a2a2dee956ceda9e00fb4e03e9a9d600b559f06cba2da7c5e78a12b05dcf993cf147634edf0391f3f20a6b451830f41be47fe68
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/c76f8f6056946466116e67eb9d8014a2d748ade2062636ab82045c1dac9c233aff10e597777bc5af6f26428beb845ceb41b95007abef7d0484da95789da56662
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/d4466d42a02c5a318d9d7b8102969fd032b17ff044918dfd462d5cc49bd11f5773ee0794781702afdf4727ba11e9be6cbea1e396bc0a7307761bb9a56399012a
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/7caec27d5ed8870895c9faf4f71def72745d69da0d8e77903146a4e135fd7bed5778f5f9cebb36c5fba86338e6194dd67a08c033fc84b4299b7eceab6d9630cb
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/ae4e203df1cb44418001fc0f5c75d7079ab342a1d629d6c0f581a3e521d0f6e5f7d5b351cb009e396782db579b29ceb66f260a873e0b8cd4c6901449af7edaa2
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/f6cb385fe0e798bff7e9b20cf5912bf40e180895ff3610b1ccdce260f3c20daaebb3a99dc087c8168a99151cd3e16b94f4689fd5a4b01cf1834b45c133e620b2
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/dea0b66742d2863b369c06c053e11e15ba785892ea19cccf7aef3c1bdaa38b6ab082e19984c5ea7810d275d9445c5400fcc385ad71ce707ed9256fadb102af3b
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fbba6e2aef0b69681acb68202aa249c0598e470cc0853d7ff5bd0171fd6a7ec31d77cfabcce9df6360fc8349eded7e4a65218c32551bd3fc0caaa1ac899ac6d4
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3edd28b07e1951f32aa2d380d9a0e0ed408c64a5cea2921d02308541042aca18f146b3a61e82e534d4d61cb3225dbc847f4f063aedfff6230b1a41282e95e8a2
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/e1414a502efba92c7974681767e365a8cda6c5e9e5f33472a9eaa0ce2e75cea0a9bef881ff8dda37c7810ad902f98d3c00ead92a3ac3b73a79d011df85b5a189
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/93aad782503b691faef7c0893372d5243df3219b07f1f22cfc32c104af6a2e7acd6102c128439eab15336d048f1b214ca134b87b0630d8cd568bf447f78b25ce
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/812d736402a6f9313b86b8adf36740394400be7a09c48e51ee45ab4a383a3f46fc618d656dd12e44934665e42ae71cf143e25b95491b699ef7c737950dbdb862
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/069b37c5beeb613635e65a3024d6f5f3da09c82137e055a7d413bfd2778d623879bd7b2985466fb66f8a32e805a9bf6aa7e336e6bfcf0304c869bb850e8400c9
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/87b9e49dee4ab6e78f4cdcdbdd837d7784f02868a96bfc206c8dbb17dd85db161b5a0ecbe95b19a42e8aea0ce57e80249e1facbf9221d7f4114d52c3b9136c9e
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/5d99c89537d1ebaac3f526c04b162cf95a47d363d4829f78c6701a2c06ab78a48da66a94f853f85f44a3d72153410ba923e072bed4b7166fa097f503eb14131d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/a34d89a2b75fb78e66d97c3dc90d4877f7e31f43316b52176f95a5dee20e9bb56ecf158eafc42a001676ddf7b393d9e67650bad6b32f5405780f25fb83cd68e3
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/295126074c7388ab05c82ef3ed0907a1ee4666bbdd763477ead9aba6eb2c74bdf65669416861ac93d337a4a27640963bb214acadc2697275ce95aab14868d57f
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.26.9":
  version: 7.27.2
  resolution: "@babel/preset-env@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.27.1"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions": "npm:^7.27.1"
    "@babel/plugin-syntax-import-attributes": "npm:^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-to-generator": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoping": "npm:^7.27.1"
    "@babel/plugin-transform-class-properties": "npm:^7.27.1"
    "@babel/plugin-transform-class-static-block": "npm:^7.27.1"
    "@babel/plugin-transform-classes": "npm:^7.27.1"
    "@babel/plugin-transform-computed-properties": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.1"
    "@babel/plugin-transform-dotall-regex": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-dynamic-import": "npm:^7.27.1"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.27.1"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.27.1"
    "@babel/plugin-transform-for-of": "npm:^7.27.1"
    "@babel/plugin-transform-function-name": "npm:^7.27.1"
    "@babel/plugin-transform-json-strings": "npm:^7.27.1"
    "@babel/plugin-transform-literals": "npm:^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.27.1"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.27.1"
    "@babel/plugin-transform-modules-amd": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-umd": "npm:^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-new-target": "npm:^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.27.1"
    "@babel/plugin-transform-numeric-separator": "npm:^7.27.1"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.27.2"
    "@babel/plugin-transform-object-super": "npm:^7.27.1"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
    "@babel/plugin-transform-private-methods": "npm:^7.27.1"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.27.1"
    "@babel/plugin-transform-property-literals": "npm:^7.27.1"
    "@babel/plugin-transform-regenerator": "npm:^7.27.1"
    "@babel/plugin-transform-regexp-modifiers": "npm:^7.27.1"
    "@babel/plugin-transform-reserved-words": "npm:^7.27.1"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.27.1"
    "@babel/plugin-transform-spread": "npm:^7.27.1"
    "@babel/plugin-transform-sticky-regex": "npm:^7.27.1"
    "@babel/plugin-transform-template-literals": "npm:^7.27.1"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.27.1"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    core-js-compat: "npm:^3.40.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3748b5e5582bee12f2b21ee4af9552a0ea8851fdfa8e54cdab142ac9191b7e9b1673d23056c0d2c3c6fd554eb85873664acfc9829c4f14a8ae7676548184eff6
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 10/039aba98a697b920d6440c622aaa6104bb6076d65356b29dad4b3e6627ec0354da44f9621bafbeefd052cd4ac4d7f88c9a2ab094efcb50963cb352781d0c6428
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.26.10":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/9d8e75326b3c93fa016ba7aada652800fc77bc05fcc181888700a049935e8cf1284b549de18a5d62ef3591d02f097ea6de1111f7d71a991aaf36ba74657bd145
  languageName: node
  linkType: hard

"@babel/runtime@npm:7.27.0":
  version: 7.27.0
  resolution: "@babel/runtime@npm:7.27.0"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10/e6966e03b695feb4c0ac0856a4355231c2580bf9ebd0298f47739f85c0ea658679dd84409daf26378d42c86c1cbe7e33feab709b14e784254b6c441d91606465
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.0, @babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2, @babel/template@npm:^7.3.3":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/fed15a84beb0b9340e5f81566600dbee5eccd92e4b9cc42a944359b1aa1082373391d9d5fc3656981dff27233ec935d0bc96453cf507f60a4b079463999244d8
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/4debb80b9068a46e188e478272f3b6820e16d17e2651e82d0a0457176b0c3b2489994f0a0d6e8941ee90218b0a8a69fe52ba350c1aa66eb4c72570d6b2405f91
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.27.0, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.3.3, @babel/types@npm:^7.4.4":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10/174741c667775680628a09117828bbeffb35ea543f59bf80649d0d60672f7815a0740ddece3cca87516199033a039166a6936434131fce2b6a820227e64f91ae
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 10/1a1f0e356a3bb30b5f1ced6f79c413e6ebacf130421f15fac5fcd8be5ddf98aedb4404d7f5624e3285b700e041f9ef938321f3ca4d359d5b716f96afa120d88d
  languageName: node
  linkType: hard

"@bitauth/libauth@npm:^3.0.0":
  version: 3.0.0
  resolution: "@bitauth/libauth@npm:3.0.0"
  checksum: 10/3158091c462f16cc1e9df8b4bbf35e6fa3f85dde1d8f6337344d4c0102b4f0b55a0768472d6ef88c297a9050772b6ee0ff4bc65174f5c4e6af7721dd9d7eac1c
  languageName: node
  linkType: hard

"@cyclonedx/yarn-plugin-cyclonedx@npm:^3.0.2":
  version: 3.1.0
  resolution: "@cyclonedx/yarn-plugin-cyclonedx@npm:3.1.0"
  bin:
    cyclonedx-yarn: bin/cyclonedx-yarn-cli.js
  checksum: 10/2e2bc8d9d42df821eec47673ec9a3254bf9f53472d28d12ea1314efef070551e6c56fe93a5d9795ca4108be4a6c5ab2b84ec00c40080ec481ff6df558b15f415
  languageName: node
  linkType: hard

"@discoveryjs/json-ext@npm:^0.6.1":
  version: 0.6.3
  resolution: "@discoveryjs/json-ext@npm:0.6.3"
  checksum: 10/6cb35ce92c8f1e9533250da9a893def63cce4f9a4f67677259bf11619d83858ca9c010171f49b22d83153b7b7ff65c39bbbf0edf4734d67e864de1044b7a943c
  languageName: node
  linkType: hard

"@hawtio/backend-middleware@npm:^1.0.5":
  version: 1.0.6
  resolution: "@hawtio/backend-middleware@npm:1.0.6"
  dependencies:
    "@bitauth/libauth": "npm:^3.0.0"
    "@types/express": "npm:^5.0.0"
    "@types/jest": "npm:^29.5.14"
    "@types/node": "npm:^22.9.0"
    axios: "npm:^1.7.7"
    express: "npm:^4.21.1"
    js-logger: "npm:^1.6.1"
  checksum: 10/aa51fa9789e707f0f65511f709ae67ff43efdbc095d15077e2a1b831121e0efce2478f9b926f57c3f70b994a216c70b5662be00a55c2aedd85bcefece74dfdd1
  languageName: node
  linkType: hard

"@hawtio/camel-model-v4_4@npm:@hawtio/camel-model@~4.4.3":
  version: 4.4.3
  resolution: "@hawtio/camel-model@npm:4.4.3"
  checksum: 10/60bb20f1f94cf7d29e16f34e70e5e8dbe89e547b28f3fd2293780b4667f8b49af5e0cbba285123e0f58a65565d880ba43f7921ac226e1a3918a4dfc975115cdf
  languageName: node
  linkType: hard

"@hawtio/camel-model-v4_8@npm:@hawtio/camel-model@~4.8.1":
  version: 4.8.5
  resolution: "@hawtio/camel-model@npm:4.8.5"
  checksum: 10/d39cb0c146eb597d7d41c57092ebcf0918e1738303508f349f9dccb9baf5d2ea947c8df72cc4127dc10c53d088f0e2a88f3d342c46afbe9f8d5d195675a8fe5f
  languageName: node
  linkType: hard

"@hawtio/react@npm:~1.6":
  version: 1.6.3
  resolution: "@hawtio/react@npm:1.6.3"
  dependencies:
    "@hawtio/camel-model-v4_4": "npm:@hawtio/camel-model@~4.4.3"
    "@hawtio/camel-model-v4_8": "npm:@hawtio/camel-model@~4.8.1"
    "@jolokia.js/simple": "npm:^2.1.8"
    "@module-federation/utilities": "npm:^3.1.16"
    "@monaco-editor/react": "npm:^4.6.0"
    "@patternfly/react-charts": "npm:~7.3.0"
    "@patternfly/react-code-editor": "npm:~5.3.3"
    "@patternfly/react-core": "npm:~5.3.3"
    "@patternfly/react-table": "npm:~5.3.3"
    "@testing-library/dom": "npm:^10.4.0"
    "@testing-library/jest-dom": "npm:^6.5.0"
    "@testing-library/react": "npm:^16.0.1"
    "@testing-library/user-event": "npm:^14.5.2"
    "@thumbmarkjs/thumbmarkjs": "npm:^0.16.0"
    "@types/dagre": "npm:^0.7.52"
    "@types/dagre-layout": "npm:^0.8.5"
    "@types/jest": "npm:^29.5.13"
    "@types/jquery": "npm:^3.5.31"
    "@types/node": "npm:^22.7.5"
    "@types/react": "npm:^18.3.11"
    "@types/react-dom": "npm:^18.3.1"
    "@types/react-router-dom": "npm:^5.3.3"
    dagre: "npm:^0.8.5"
    eventemitter3: "npm:^5.0.1"
    jolokia.js: "npm:^2.1.8"
    jquery: "npm:^3.7.1"
    js-logger: "npm:^1.6.1"
    jwt-decode: "npm:^4.0.0"
    keycloak-js: "npm:^26.0.6"
    monaco-editor: "npm:^0.52.0"
    oauth4webapi: "npm:^2.17.0"
    react: "npm:^18.3.1"
    react-dom: "npm:^18.3.1"
    react-markdown: "npm:^8.0.7"
    react-monaco-editor: "npm:^0.56.2"
    react-router-dom: "npm:^6.27.0"
    react-split: "npm:^2.0.14"
    reactflow: "npm:^11.11.4"
    superstruct: "npm:^2.0.2"
    typescript: "npm:^5.4.5"
    xml-formatter: "npm:^3.6.3"
  peerDependencies:
    "@patternfly/react-core": ^5
    keycloak-js: ^21
    react: ^16.8 || ^17 || ^18
    react-dom: ^16.8 || ^17 || ^18
  peerDependenciesMeta:
    keycloak-js:
      optional: true
  checksum: 10/6bf2e4844c91f6a98c82d7a99a16266db8b7b870410e555651bccbb05471acecc4d8887f10097a7d86dbe41497c412b708ac117a3506d802d01a4b59ae172a59
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: "npm:^5.3.1"
    find-up: "npm:^4.1.0"
    get-package-type: "npm:^0.1.0"
    js-yaml: "npm:^3.13.1"
    resolve-from: "npm:^5.0.0"
  checksum: 10/b000a5acd8d4fe6e34e25c399c8bdbb5d3a202b4e10416e17bfc25e12bab90bb56d33db6089ae30569b52686f4b35ff28ef26e88e21e69821d2b85884bd055b8
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10/a9b1e49acdf5efc2f5b2359f2df7f90c5c725f2656f16099e8b2cd3a000619ecca9fc48cf693ba789cf0fd989f6e0df6a22bc05574be4223ecdbb7997d04384b
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10/4a80c750e8a31f344233cb9951dee9b77bf6b89377cb131f8b3cde07ff218f504370133a5963f6a786af4d2ce7f85642db206ff7a15f99fe58df4c38ac04899e
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/reporters": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-changed-files: "npm:^29.7.0"
    jest-config: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-resolve-dependencies: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10/ab6ac2e562d083faac7d8152ec1cc4eccc80f62e9579b69ed40aedf7211a6b2d57024a6cd53c4e35fd051c39a236e86257d1d99ebdb122291969a0a04563b51e
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10/90b5844a9a9d8097f2cf107b1b5e57007c552f64315da8c1f51217eeb0a9664889d3f145cdf8acf23a84f4d8309a6675e27d5b059659a004db0ea9546d1c81a8
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
  checksum: 10/ef8d379778ef574a17bde2801a6f4469f8022a46a5f9e385191dc73bb1fc318996beaed4513fbd7055c2847227a1bed2469977821866534593a6e52a281499ee
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10/fea6c3317a8da5c840429d90bfe49d928e89c9e89fceee2149b93a11b7e9c73d2f6e4d7cdf647163da938fc4e2169e4490be6bae64952902bc7a701033fd4880
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10/9b394e04ffc46f91725ecfdff34c4e043eb7a16e1d78964094c9db3fde0b1c8803e45943a980e8c740d0a3d45661906de1416ca5891a538b0660481a3a828c27
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    jest-mock: "npm:^29.7.0"
  checksum: 10/97dbb9459135693ad3a422e65ca1c250f03d82b2a77f6207e7fa0edd2c9d2015fbe4346f3dc9ebff1678b9d8da74754d4d440b7837497f8927059c0642a22123
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": "npm:^0.2.3"
    "@jest/console": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    exit: "npm:^0.1.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    istanbul-lib-coverage: "npm:^3.0.0"
    istanbul-lib-instrument: "npm:^6.0.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^4.0.0"
    istanbul-reports: "npm:^3.1.3"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    string-length: "npm:^4.0.1"
    strip-ansi: "npm:^6.0.0"
    v8-to-istanbul: "npm:^9.0.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10/a17d1644b26dea14445cedd45567f4ba7834f980be2ef74447204e14238f121b50d8b858fde648083d2cd8f305f81ba434ba49e37a5f4237a6f2a61180cc73dc
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10/910040425f0fc93cd13e68c750b7885590b8839066dfa0cd78e7def07bbb708ad869381f725945d66f2284de5663bbecf63e8fdd856e2ae6e261ba30b1687e93
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    callsites: "npm:^3.0.0"
    graceful-fs: "npm:^4.2.9"
  checksum: 10/bcc5a8697d471396c0003b0bfa09722c3cd879ad697eb9c431e6164e2ea7008238a01a07193dfe3cbb48b1d258eb7251f6efcea36f64e1ebc464ea3c03ae2deb
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    collect-v8-coverage: "npm:^1.0.0"
  checksum: 10/c073ab7dfe3c562bff2b8fee6cc724ccc20aa96bcd8ab48ccb2aa309b4c0c1923a9e703cea386bd6ae9b71133e92810475bb9c7c22328fc63f797ad3324ed189
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10/4420c26a0baa7035c5419b0892ff8ffe9a41b1583ec54a10db3037cd46a7e29dd3d7202f8aa9d376e9e53be5f8b1bc0d16e1de6880a6d319b033b01dc4c8f639
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    babel-plugin-istanbul: "npm:^6.1.1"
    chalk: "npm:^4.0.0"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pirates: "npm:^4.0.4"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^4.0.2"
  checksum: 10/30f42293545ab037d5799c81d3e12515790bb58513d37f788ce32d53326d0d72ebf5b40f989e6896739aa50a5f77be44686e510966370d58511d5ad2637c68c1
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10/f74bf512fd09bbe2433a2ad460b04668b7075235eea9a0c77d6a42222c10a79b9747dc2b2a623f140ed40d6865a2ed8f538f3cbb75169120ea863f29a7ed76cd
  languageName: node
  linkType: hard

"@jolokia.js/simple@npm:^2.1.7, @jolokia.js/simple@npm:^2.1.8":
  version: 2.2.4
  resolution: "@jolokia.js/simple@npm:2.2.4"
  dependencies:
    jolokia.js: "npm:2.2.4"
  checksum: 10/b0f182db30ce0d8a71205e7de84ea74d6d5d4d44249949c6a7a515adfaba010ff0f9b41acb303dc394bc24aa415637b8533f8b4879985123446893310b27faf7
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/9d3a56ab3612ab9b85d38b2a93b87f3324f11c5130859957f6500e4ac8ce35f299d5ccc3ecd1ae87597601ecf83cee29e9afd04c18777c24011073992ff946df
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10/832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10/0a9aca9320dc9044014ba0ef989b3a8411b0d778895553e3b7ca2ac0a75a20af4a5ad3f202acfb1879fa40466036a4417e1d5b38305baed8b9c1ebe6e4b3e7f5
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@jsonjoy.com/base64@npm:^1.1.1":
  version: 1.1.2
  resolution: "@jsonjoy.com/base64@npm:1.1.2"
  peerDependencies:
    tslib: 2
  checksum: 10/d76bb58eff841c090d9bf69a073611ffa73c40a664ccbcea689f65961f57d7b24051269d06b437e4f6204285d6ba92f50f587c5e95c5f9e4f10b36a2ed4cd0c8
  languageName: node
  linkType: hard

"@jsonjoy.com/json-pack@npm:^1.0.3":
  version: 1.2.0
  resolution: "@jsonjoy.com/json-pack@npm:1.2.0"
  dependencies:
    "@jsonjoy.com/base64": "npm:^1.1.1"
    "@jsonjoy.com/util": "npm:^1.1.2"
    hyperdyperid: "npm:^1.2.0"
    thingies: "npm:^1.20.0"
  peerDependencies:
    tslib: 2
  checksum: 10/5b4f01bf195e314c19c5669a7bad968a814f0ed6b9e16a669b08081db0ed9f66fe3c3b2e3e0b67281b4f90910338f6beeae6b51bda9198590d29b39d1ea69755
  languageName: node
  linkType: hard

"@jsonjoy.com/util@npm:^1.1.2, @jsonjoy.com/util@npm:^1.3.0":
  version: 1.6.0
  resolution: "@jsonjoy.com/util@npm:1.6.0"
  peerDependencies:
    tslib: 2
  checksum: 10/6f2fd06aa9fb8b6bde1301e30aef0115bb728eff4dc73ab3402f11f0674a58f0a96411c0eeeb9ef2ed28e5aca3a9dc8138a5de784e62d1d53a3200731f7a0379
  languageName: node
  linkType: hard

"@leichtgewicht/ip-codec@npm:^2.0.1":
  version: 2.0.5
  resolution: "@leichtgewicht/ip-codec@npm:2.0.5"
  checksum: 10/cb98c608392abe59457a14e00134e7dfa57c0c9b459871730cd4e907bb12b834cbd03e08ad8663fea9e486f260da7f1293ccd9af0376bf5524dd8536192f248c
  languageName: node
  linkType: hard

"@module-federation/sdk@npm:0.15.0":
  version: 0.15.0
  resolution: "@module-federation/sdk@npm:0.15.0"
  checksum: 10/2d4bdc38d7fadca59f7e73c152e2614ef0228de1fd996c0143e81e68cf046cb20fb8b60df22931c5082b3ec891ce9448378fa38f647bb41d9222c877aa581152
  languageName: node
  linkType: hard

"@module-federation/utilities@npm:^3.1.16":
  version: 3.1.59
  resolution: "@module-federation/utilities@npm:3.1.59"
  dependencies:
    "@module-federation/sdk": "npm:0.15.0"
  peerDependencies:
    react: ^16 || ^17 || ^18
    react-dom: ^16 || ^17 || ^18
    webpack: ^5.40.0
  peerDependenciesMeta:
    next:
      optional: true
    react:
      optional: true
    react-dom:
      optional: true
  checksum: 10/31cf7041e2543054fa476e8362d716a583c1a9b96fc8b655aa9c4e5d0a1024cc888b1167345eace51dd35ce277f1535a8cbc8641526615cdcb1eaa6537972a24
  languageName: node
  linkType: hard

"@monaco-editor/loader@npm:^1.5.0":
  version: 1.5.0
  resolution: "@monaco-editor/loader@npm:1.5.0"
  dependencies:
    state-local: "npm:^1.0.6"
  checksum: 10/97d79916afa856809de4eaafaee1b1c6dc9443c0fdde81a7562f4ffa0c252496e97c4becf4c1f00c4119878c76d73390ce416a64305e59865a5830bded5afe55
  languageName: node
  linkType: hard

"@monaco-editor/react@npm:^4.6.0":
  version: 4.7.0
  resolution: "@monaco-editor/react@npm:4.7.0"
  dependencies:
    "@monaco-editor/loader": "npm:^1.5.0"
  peerDependencies:
    monaco-editor: ">= 0.25.0 < 1"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/d72392c4ed6faf8d830ba43421461e1b767b5978edba0739457d7781aa9533c66982be7f59bb156a77a2b578eddfb4711f50e0d84f0f0d25d28b5ab11140f5cc
  languageName: node
  linkType: hard

"@nicolo-ribaudo/chokidar-2@npm:2.1.8-no-fsevents.3":
  version: 2.1.8-no-fsevents.3
  resolution: "@nicolo-ribaudo/chokidar-2@npm:2.1.8-no-fsevents.3"
  checksum: 10/c6e83af3b5051a3f6562649ff8fe37de9934a4cc02138678ed1badbd13ed3334f7ae5f63f2bbc3432210f6b245f082ac97e9b2afe0c13730c9838b295658c185
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@patternfly/patternfly@npm:~5.4":
  version: 5.4.2
  resolution: "@patternfly/patternfly@npm:5.4.2"
  checksum: 10/1d1921a97d4883e761eaa1de08fea6cd684ee74e3838936d0fca9fc012ab729f7ba42fe0dcd3211e72e2e446d963a71ac3315a36717378f9e15217459bc3f829
  languageName: node
  linkType: hard

"@patternfly/react-charts@npm:~7.3.0":
  version: 7.3.1
  resolution: "@patternfly/react-charts@npm:7.3.1"
  dependencies:
    "@patternfly/react-styles": "npm:^5.3.1"
    "@patternfly/react-tokens": "npm:^5.3.1"
    hoist-non-react-statics: "npm:^3.3.0"
    lodash: "npm:^4.17.21"
    tslib: "npm:^2.5.0"
    victory-area: "npm:^36.9.1"
    victory-axis: "npm:^36.9.1"
    victory-bar: "npm:^36.9.1"
    victory-box-plot: "npm:^36.9.1"
    victory-chart: "npm:^36.9.1"
    victory-core: "npm:^36.9.1"
    victory-create-container: "npm:^36.9.1"
    victory-cursor-container: "npm:^36.9.1"
    victory-group: "npm:^36.9.1"
    victory-legend: "npm:^36.9.1"
    victory-line: "npm:^36.9.1"
    victory-pie: "npm:^36.9.1"
    victory-scatter: "npm:^36.9.1"
    victory-stack: "npm:^36.9.1"
    victory-tooltip: "npm:^36.9.1"
    victory-voronoi-container: "npm:^36.9.1"
    victory-zoom-container: "npm:^36.9.1"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/fe45ff19a44afa2da5fcf8cf7289f5af893c0719b9ccd1144948590d62d76abc69d4b32fd3968c416fdd518ef1f41bce8daadba31b59057b6cb455b9c25ac5a8
  languageName: node
  linkType: hard

"@patternfly/react-charts@npm:~7.4":
  version: 7.4.9
  resolution: "@patternfly/react-charts@npm:7.4.9"
  dependencies:
    "@patternfly/react-styles": "npm:^5.4.1"
    "@patternfly/react-tokens": "npm:^5.4.1"
    hoist-non-react-statics: "npm:^3.3.2"
    lodash: "npm:^4.17.21"
    tslib: "npm:^2.7.0"
    victory-area: "npm:^37.3.6"
    victory-axis: "npm:^37.3.6"
    victory-bar: "npm:^37.3.6"
    victory-box-plot: "npm:^37.3.6"
    victory-chart: "npm:^37.3.6"
    victory-core: "npm:^37.3.6"
    victory-create-container: "npm:^37.3.6"
    victory-cursor-container: "npm:^37.3.6"
    victory-group: "npm:^37.3.6"
    victory-legend: "npm:^37.3.6"
    victory-line: "npm:^37.3.6"
    victory-pie: "npm:^37.3.6"
    victory-scatter: "npm:^37.3.6"
    victory-stack: "npm:^37.3.6"
    victory-tooltip: "npm:^37.3.6"
    victory-voronoi-container: "npm:^37.3.6"
    victory-zoom-container: "npm:^37.3.6"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/fb60f8b2aa852ba1c176070fe1eb5947eaaf692441112912fb75a1369f24e1fa2a50d97546b891a89bf2b9dcff0d3de2ef7befb2e59268e7f578a7a42c431ab7
  languageName: node
  linkType: hard

"@patternfly/react-code-editor@npm:~5.3.3":
  version: 5.3.4
  resolution: "@patternfly/react-code-editor@npm:5.3.4"
  dependencies:
    "@monaco-editor/react": "npm:^4.6.0"
    "@patternfly/react-core": "npm:^5.3.4"
    "@patternfly/react-icons": "npm:^5.3.2"
    "@patternfly/react-styles": "npm:^5.3.1"
    react-dropzone: "npm:14.2.3"
    tslib: "npm:^2.5.0"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/9bb2f57f2b8a77fa46c4de3693f899274e9bc66924fe64c7b110866cd8268f310150be5f52dec1375c945df5b591060e62ecbc1596dca88eb4c4b8acbfc3f2ec
  languageName: node
  linkType: hard

"@patternfly/react-core@npm:^5.3.4, @patternfly/react-core@npm:^5.4.14, @patternfly/react-core@npm:~5.4":
  version: 5.4.14
  resolution: "@patternfly/react-core@npm:5.4.14"
  dependencies:
    "@patternfly/react-icons": "npm:^5.4.2"
    "@patternfly/react-styles": "npm:^5.4.1"
    "@patternfly/react-tokens": "npm:^5.4.1"
    focus-trap: "npm:7.6.2"
    react-dropzone: "npm:^14.2.3"
    tslib: "npm:^2.7.0"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/14d1bdcbfa941025b9368121c1b42dd88be7bb6e42abbd3284e788bc2a9524c4efde927076527640a2da3c308d01c8ea891aefe110db3a8a284a890357b0a9ab
  languageName: node
  linkType: hard

"@patternfly/react-core@npm:~5.3.3":
  version: 5.3.4
  resolution: "@patternfly/react-core@npm:5.3.4"
  dependencies:
    "@patternfly/react-icons": "npm:^5.3.2"
    "@patternfly/react-styles": "npm:^5.3.1"
    "@patternfly/react-tokens": "npm:^5.3.1"
    focus-trap: "npm:7.5.2"
    react-dropzone: "npm:^14.2.3"
    tslib: "npm:^2.5.0"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/39c44c7cc5542392ce4856a1cb2f5511099b2fd9e012364c37c9453aace3f2df92f0997e10db2b075d7a6c8d982b0d6d03b2df1481dc444ea774f861e97645be
  languageName: node
  linkType: hard

"@patternfly/react-icons@npm:^5.3.2, @patternfly/react-icons@npm:^5.4.2, @patternfly/react-icons@npm:~5.4":
  version: 5.4.2
  resolution: "@patternfly/react-icons@npm:5.4.2"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/4169ee0bf61a1b9deb9d501341d8031754c4e05ed0ea6811dd8a38a0c4ad5e15d231513413cb9ae5c77e404e9d2a7a2482160bf64dc462d045902a4a2907993f
  languageName: node
  linkType: hard

"@patternfly/react-styles@npm:^5.3.1, @patternfly/react-styles@npm:^5.4.1":
  version: 5.4.1
  resolution: "@patternfly/react-styles@npm:5.4.1"
  checksum: 10/414a55c31a90d4619f13085d043d90e93b3e563cde88ba72b485ed3e334e99f442cc43030b8de61712a5ace498e9e83acd5bd76ebe00098d0e0319fcc048fc8e
  languageName: node
  linkType: hard

"@patternfly/react-table@npm:~5.3.3":
  version: 5.3.4
  resolution: "@patternfly/react-table@npm:5.3.4"
  dependencies:
    "@patternfly/react-core": "npm:^5.3.4"
    "@patternfly/react-icons": "npm:^5.3.2"
    "@patternfly/react-styles": "npm:^5.3.1"
    "@patternfly/react-tokens": "npm:^5.3.1"
    lodash: "npm:^4.17.19"
    tslib: "npm:^2.5.0"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/ed2cce2848e54629555332bfb2f8d560b72f39b12ca5d3035493e120c186c679c8f7a9c44c03cf0eb19e97a224ce23fefe8266efd4589f30295a00aef47f075e
  languageName: node
  linkType: hard

"@patternfly/react-table@npm:~5.4":
  version: 5.4.16
  resolution: "@patternfly/react-table@npm:5.4.16"
  dependencies:
    "@patternfly/react-core": "npm:^5.4.14"
    "@patternfly/react-icons": "npm:^5.4.2"
    "@patternfly/react-styles": "npm:^5.4.1"
    "@patternfly/react-tokens": "npm:^5.4.1"
    lodash: "npm:^4.17.21"
    tslib: "npm:^2.7.0"
  peerDependencies:
    react: ^17 || ^18
    react-dom: ^17 || ^18
  checksum: 10/45c0ee73cfd7976e0e5eb8236bfd1c6afd2cdf84a2d685f9e8ba2c7dcf0023a75330b33fdc305c5ae4bd72b56ffc4e5c43607f8988884881f6d9b7e55e845b49
  languageName: node
  linkType: hard

"@patternfly/react-tokens@npm:^5.3.1, @patternfly/react-tokens@npm:^5.4.1":
  version: 5.4.1
  resolution: "@patternfly/react-tokens@npm:5.4.1"
  checksum: 10/042fc56f37b15c6f21b16eec6f6ffbff23299942324039d39ca6b5f346be6b13fe2952de816b72603fafd93afc640d383aad4d118d56a9bb678585d542076f3b
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@reactflow/background@npm:11.3.14":
  version: 11.3.14
  resolution: "@reactflow/background@npm:11.3.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/bf6c5c7eaabc72371ae882407299b130ed56c538a60e460a3aa305c8d0ba5a51b1f3cbf244e0c1768ee85324ce2c8fd23eeaf0ece45b570197dacb6348a1e843
  languageName: node
  linkType: hard

"@reactflow/controls@npm:11.2.14":
  version: 11.2.14
  resolution: "@reactflow/controls@npm:11.2.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/5ab2f4ee4daf43f22bae18b3d20e9804c8797a32f194b7fd0bf3d90c17f51db0fd09ba34c31408528ae809530dcdf2fde4d44bc044b1aa41ff0eabd972f52505
  languageName: node
  linkType: hard

"@reactflow/core@npm:11.11.4":
  version: 11.11.4
  resolution: "@reactflow/core@npm:11.11.4"
  dependencies:
    "@types/d3": "npm:^7.4.0"
    "@types/d3-drag": "npm:^3.0.1"
    "@types/d3-selection": "npm:^3.0.3"
    "@types/d3-zoom": "npm:^3.0.1"
    classcat: "npm:^5.0.3"
    d3-drag: "npm:^3.0.0"
    d3-selection: "npm:^3.0.0"
    d3-zoom: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/725a876356f44c0efccb43212beaf2e182481aabfb7b5a139926366c1b97e20cec5ad88cf392a7e0a6be3998422c7719cadfc2ab53ec6ff61fa792a36e899599
  languageName: node
  linkType: hard

"@reactflow/minimap@npm:11.7.14":
  version: 11.7.14
  resolution: "@reactflow/minimap@npm:11.7.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    "@types/d3-selection": "npm:^3.0.3"
    "@types/d3-zoom": "npm:^3.0.1"
    classcat: "npm:^5.0.3"
    d3-selection: "npm:^3.0.0"
    d3-zoom: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/3aa9bbfbe2fcbd874b8b41fe3ddb5d221f90f58d17d093c0d5edbfafb540d0dc4cd9785e8802eb18db5bdd730d5705234ab65bd31cfbb71eabf42e49bf2f7cac
  languageName: node
  linkType: hard

"@reactflow/node-resizer@npm:2.2.14":
  version: 2.2.14
  resolution: "@reactflow/node-resizer@npm:2.2.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.4"
    d3-drag: "npm:^3.0.0"
    d3-selection: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/0440dd32c6364304f92b12a2d8288260d66a825533b99b7705c112362858c676b0a0221adf983bfdbbacbd70388dfcaec69401129e60bfca5c25c9b8302b0aef
  languageName: node
  linkType: hard

"@reactflow/node-toolbar@npm:1.3.14":
  version: 1.3.14
  resolution: "@reactflow/node-toolbar@npm:1.3.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/159566da042f29c15e506d4c49cd938b28644ad7710b266e35293c268286c48488b92d7c621f25700bfbabff84470c181ec2683a44c04d1749370c09d2e4a12b
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10/297f95ff77c82c54de8c9907f186076e715ff2621c5222ba50b8d40a170661c0c5242c763cba2a4791f0f91cb1d8ffa53ea1d7294570cf8cd4694c0e383e484d
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 10/e7f36ed72abfcd5e0355f7423a72918b9748bb1ef370a59f3e5ad8d40b728b85d63b272f65f63eec1faf417cda89dcb0aeebe94015647b6054659c1442fe5ce0
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^2.1.0":
  version: 2.3.0
  resolution: "@sindresorhus/merge-streams@npm:2.3.0"
  checksum: 10/798bcb53cd1ace9df84fcdd1ba86afdc9e0cd84f5758d26ae9b1eefd8e8887e5fc30051132b9e74daf01bb41fa5a2faf1369361f83d76a3b3d7ee938058fd71c
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10/a0af217ba7044426c78df52c23cedede6daf377586f3ac58857c565769358ab1f44ebf95ba04bbe38814fba6e316ca6f02870a009328294fc2c555d0f85a7117
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10/78155c7bd866a85df85e22028e046b8d46cf3e840f72260954f5e3ed5bd97d66c595524305a6841ffb3f681a08f6e5cef572a2cce5442a8a232dc29fb409b83e
  languageName: node
  linkType: hard

"@swc/core-darwin-arm64@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-darwin-arm64@npm:1.7.42"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-darwin-x64@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-darwin-x64@npm:1.7.42"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@swc/core-linux-arm-gnueabihf@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-linux-arm-gnueabihf@npm:1.7.42"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-gnu@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-linux-arm64-gnu@npm:1.7.42"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-musl@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-linux-arm64-musl@npm:1.7.42"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-linux-x64-gnu@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-linux-x64-gnu@npm:1.7.42"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-x64-musl@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-linux-x64-musl@npm:1.7.42"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-win32-arm64-msvc@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-win32-arm64-msvc@npm:1.7.42"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-win32-ia32-msvc@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-win32-ia32-msvc@npm:1.7.42"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@swc/core-win32-x64-msvc@npm:1.7.42":
  version: 1.7.42
  resolution: "@swc/core-win32-x64-msvc@npm:1.7.42"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/core@npm:~1.7":
  version: 1.7.42
  resolution: "@swc/core@npm:1.7.42"
  dependencies:
    "@swc/core-darwin-arm64": "npm:1.7.42"
    "@swc/core-darwin-x64": "npm:1.7.42"
    "@swc/core-linux-arm-gnueabihf": "npm:1.7.42"
    "@swc/core-linux-arm64-gnu": "npm:1.7.42"
    "@swc/core-linux-arm64-musl": "npm:1.7.42"
    "@swc/core-linux-x64-gnu": "npm:1.7.42"
    "@swc/core-linux-x64-musl": "npm:1.7.42"
    "@swc/core-win32-arm64-msvc": "npm:1.7.42"
    "@swc/core-win32-ia32-msvc": "npm:1.7.42"
    "@swc/core-win32-x64-msvc": "npm:1.7.42"
    "@swc/counter": "npm:^0.1.3"
    "@swc/types": "npm:^0.1.13"
  peerDependencies:
    "@swc/helpers": "*"
  dependenciesMeta:
    "@swc/core-darwin-arm64":
      optional: true
    "@swc/core-darwin-x64":
      optional: true
    "@swc/core-linux-arm-gnueabihf":
      optional: true
    "@swc/core-linux-arm64-gnu":
      optional: true
    "@swc/core-linux-arm64-musl":
      optional: true
    "@swc/core-linux-x64-gnu":
      optional: true
    "@swc/core-linux-x64-musl":
      optional: true
    "@swc/core-win32-arm64-msvc":
      optional: true
    "@swc/core-win32-ia32-msvc":
      optional: true
    "@swc/core-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@swc/helpers":
      optional: true
  checksum: 10/d8b2782a18a602e33b38c236aaba66a4dd95e5b851ddf3713bfab896c1f5e9cc756ee680c6d25f04b558ebdbb6b87977a145fb788618e5ffc5b30a24f8abb03b
  languageName: node
  linkType: hard

"@swc/counter@npm:^0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: 10/df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/types@npm:^0.1.13":
  version: 0.1.23
  resolution: "@swc/types@npm:0.1.23"
  dependencies:
    "@swc/counter": "npm:^0.1.3"
  checksum: 10/8d9d73dd1fc9335105105da57595ab913bad6addd4fbcb2eb147300694630232225eb7dc74b733205af33352803e4fcefc18e3a36f8924cf821ef91384767670
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: "npm:^2.0.0"
  checksum: 10/c29df3bcec6fc3bdec2b17981d89d9c9fc9bd7d0c9bcfe92821dc533f4440bc890ccde79971838b4ceed1921d456973c4180d7175ee1d0023ad0562240a58d95
  languageName: node
  linkType: hard

"@testing-library/dom@npm:^10.4.0":
  version: 10.4.0
  resolution: "@testing-library/dom@npm:10.4.0"
  dependencies:
    "@babel/code-frame": "npm:^7.10.4"
    "@babel/runtime": "npm:^7.12.5"
    "@types/aria-query": "npm:^5.0.1"
    aria-query: "npm:5.3.0"
    chalk: "npm:^4.1.0"
    dom-accessibility-api: "npm:^0.5.9"
    lz-string: "npm:^1.5.0"
    pretty-format: "npm:^27.0.2"
  checksum: 10/05825ee9a15b88cbdae12c137db7111c34069ed3c7a1bd03b6696cb1b37b29f6f2d2de581ebf03033e7df1ab7ebf08399310293f440a4845d95c02c0a9ecc899
  languageName: node
  linkType: hard

"@testing-library/jest-dom@npm:^6.5.0":
  version: 6.6.3
  resolution: "@testing-library/jest-dom@npm:6.6.3"
  dependencies:
    "@adobe/css-tools": "npm:^4.4.0"
    aria-query: "npm:^5.0.0"
    chalk: "npm:^3.0.0"
    css.escape: "npm:^1.5.1"
    dom-accessibility-api: "npm:^0.6.3"
    lodash: "npm:^4.17.21"
    redent: "npm:^3.0.0"
  checksum: 10/1f3427e45870eab9dcc59d6504b780d4a595062fe1687762ae6e67d06a70bf439b40ab64cf58cbace6293a99e3764d4647fdc8300a633b721764f5ce39dade18
  languageName: node
  linkType: hard

"@testing-library/react@npm:^16.0.1":
  version: 16.3.0
  resolution: "@testing-library/react@npm:16.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
  peerDependencies:
    "@testing-library/dom": ^10.0.0
    "@types/react": ^18.0.0 || ^19.0.0
    "@types/react-dom": ^18.0.0 || ^19.0.0
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/0ee9e31dd0d2396a924682d0e61a4ecc6bfab8eaff23dbf8a72c3c2ce22c116fa578148baeb4de75b968ef99d22e6e6aa0a00dba40286f71184918bb6bb5b06a
  languageName: node
  linkType: hard

"@testing-library/user-event@npm:^14.5.2":
  version: 14.6.1
  resolution: "@testing-library/user-event@npm:14.6.1"
  peerDependencies:
    "@testing-library/dom": ">=7.21.4"
  checksum: 10/34b74fff56a0447731a94b40d4cf246deb8dbc1c1e3aec93acd1c3377a760bb062e979f1572bb34ec164ad28ee2a391744b42d0d6d6cc16c4ce527e5e09610e1
  languageName: node
  linkType: hard

"@thumbmarkjs/thumbmarkjs@npm:^0.16.0":
  version: 0.16.1
  resolution: "@thumbmarkjs/thumbmarkjs@npm:0.16.1"
  checksum: 10/15998a7d4eab699825adbfe5bdfeb6039dcf5cd7fa52b02cc4e4d4d82895d0c1d2f5248b600690791d523640775bba9651a2d0ef8447bc17d6dae16efc1ec7b9
  languageName: node
  linkType: hard

"@types/aria-query@npm:^5.0.1":
  version: 5.0.4
  resolution: "@types/aria-query@npm:5.0.4"
  checksum: 10/c0084c389dc030daeaf0115a92ce43a3f4d42fc8fef2d0e22112d87a42798d4a15aac413019d4a63f868327d52ad6740ab99609462b442fe6b9286b172d2e82e
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10/c32838d280b5ab59d62557f9e331d3831f8e547ee10b4f85cb78753d97d521270cebfc73ce501e9fb27fe71884d1ba75e18658692c2f4117543f0fc4e3e118b3
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10/f572e67a9a39397664350a4437d8a7fbd34acc83ff4887a8cf08349e39f8aeb5ad2f70fb78a0a0a23a280affe3a5f4c25f50966abdce292bcf31237af1c27b1a
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10/d7a02d2a9b67e822694d8e6a7ddb8f2b71a1d6962dfd266554d2513eefbb205b33ca71a0d163b1caea3981ccf849211f9964d8bd0727124d18ace45aa6c9ae29
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10/d005b58e1c26bdafc1ce564f60db0ee938393c7fc586b1197bdb71a02f7f33f72bc10ae4165776b6cafc77c4b6f2e1a164dd20bc36518c471b1131b153b4baa6
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.6
  resolution: "@types/body-parser@npm:1.19.6"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/33041e88eae00af2cfa0827e951e5f1751eafab2a8b6fce06cd89ef368a988907996436b1325180edaeddd1c0c7d0d0d4c20a6c9ff294a91e0039a9db9e9b658
  languageName: node
  linkType: hard

"@types/bonjour@npm:^3.5.13":
  version: 3.5.13
  resolution: "@types/bonjour@npm:3.5.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/e827570e097bd7d625a673c9c208af2d1a22fa3885c0a1646533cf24394c839c3e5f60ac1bc60c0ddcc69c0615078c9fb2c01b42596c7c582d895d974f2409ee
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "npm:*"
    "@types/keyv": "npm:^3.1.4"
    "@types/node": "npm:*"
    "@types/responselike": "npm:^1.0.0"
  checksum: 10/159f9fdb2a1b7175eef453ae2ced5ea04c0d2b9610cc9ccd9f9abb066d36dacb1f37acd879ace10ad7cbb649490723feb396fb7307004c9670be29636304b988
  languageName: node
  linkType: hard

"@types/connect-history-api-fallback@npm:^1.5.4":
  version: 1.5.4
  resolution: "@types/connect-history-api-fallback@npm:1.5.4"
  dependencies:
    "@types/express-serve-static-core": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/e1dee43b8570ffac02d2d47a2b4ba80d3ca0dd1840632dafb221da199e59dbe3778d3d7303c9e23c6b401f37c076935a5bc2aeae1c4e5feaefe1c371fe2073fd
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/d3-array@npm:*, @types/d3-array@npm:^3.0.3":
  version: 3.2.1
  resolution: "@types/d3-array@npm:3.2.1"
  checksum: 10/4a9ecacaa859cff79e10dcec0c79053f027a4749ce0a4badeaff7400d69a9c44eb8210b147916b6ff5309be049030e7d68a0e333294ff3fa11c44aa1af4ba458
  languageName: node
  linkType: hard

"@types/d3-axis@npm:*":
  version: 3.0.6
  resolution: "@types/d3-axis@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10/8af56b629a0597ac8ef5051b6ad5390818462d8e588e1b52fb181808b1c0525d12a658730fad757e1ae256d0db170a0e29076acdef21acc98b954608d1c37b84
  languageName: node
  linkType: hard

"@types/d3-brush@npm:*":
  version: 3.0.6
  resolution: "@types/d3-brush@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10/4095cee2512d965732147493c471a8dd97dfb5967479d9aef43397f8b0e074b03296302423b8379c4274f9249b52bd1d74cc021f98d4f64b5a8a4a7e6fe48335
  languageName: node
  linkType: hard

"@types/d3-chord@npm:*":
  version: 3.0.6
  resolution: "@types/d3-chord@npm:3.0.6"
  checksum: 10/ca9ba8b00debd24a2b51527b9c3db63eafa5541c08dc721d1c52ca19960c5cec93a7b1acfc0ec072dbca31d134924299755e20a4d1d4ee04b961fc0de841b418
  languageName: node
  linkType: hard

"@types/d3-color@npm:*":
  version: 3.1.3
  resolution: "@types/d3-color@npm:3.1.3"
  checksum: 10/1cf0f512c09357b25d644ab01b54200be7c9b15c808333b0ccacf767fff36f17520b2fcde9dad45e1bd7ce84befad39b43da42b4fded57680fa2127006ca3ece
  languageName: node
  linkType: hard

"@types/d3-contour@npm:*":
  version: 3.0.6
  resolution: "@types/d3-contour@npm:3.0.6"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/geojson": "npm:*"
  checksum: 10/e7b7e3972aa71003c21f2c864116ffb95a9175a62ec56ec656a855e5198a66a0830b2ad7fc26811214cfa8c98cdf4190d7d351913ca0913f799fbcf2a4c99b2d
  languageName: node
  linkType: hard

"@types/d3-delaunay@npm:*":
  version: 6.0.4
  resolution: "@types/d3-delaunay@npm:6.0.4"
  checksum: 10/cb8d2c9ed0b39ade3107b9792544a745b2de3811a6bd054813e9dc708b1132fbacd796e54c0602c11b3a14458d14487c5276c1affb7c2b9f25fe55fff88d6d25
  languageName: node
  linkType: hard

"@types/d3-dispatch@npm:*":
  version: 3.0.6
  resolution: "@types/d3-dispatch@npm:3.0.6"
  checksum: 10/f82076c7d205885480d363c92c19b8e0d6b9e529a3a78ce772f96a7cc4cce01f7941141f148828337035fac9676b13e7440565530491d560fdf12e562cb56573
  languageName: node
  linkType: hard

"@types/d3-drag@npm:*, @types/d3-drag@npm:^3.0.1":
  version: 3.0.7
  resolution: "@types/d3-drag@npm:3.0.7"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10/93aba299c3a8d41ee326c5304ab694ceea135ed115c3b2ccab727a5d9bfc935f7f36d3fc416c013010eb755ac536c52adfcb15c195f241dc61f62650cc95088e
  languageName: node
  linkType: hard

"@types/d3-dsv@npm:*":
  version: 3.0.7
  resolution: "@types/d3-dsv@npm:3.0.7"
  checksum: 10/8507f542135cae472781dff1c3b391eceedad0f2032d24ac4a0814e72e2f6877e4ddcb66f44627069977ee61029dc0a729edf659ed73cbf1040f55a7451f05ef
  languageName: node
  linkType: hard

"@types/d3-ease@npm:*, @types/d3-ease@npm:^3.0.0":
  version: 3.0.2
  resolution: "@types/d3-ease@npm:3.0.2"
  checksum: 10/d8f92a8a7a008da71f847a16227fdcb53a8938200ecdf8d831ab6b49aba91e8921769761d3bfa7e7191b28f62783bfd8b0937e66bae39d4dd7fb0b63b50d4a94
  languageName: node
  linkType: hard

"@types/d3-fetch@npm:*":
  version: 3.0.7
  resolution: "@types/d3-fetch@npm:3.0.7"
  dependencies:
    "@types/d3-dsv": "npm:*"
  checksum: 10/d496475cec7750f75740936e750a0150ca45e924a4f4697ad2c564f3a8f6c4ebc1b1edf8e081936e896532516731dbbaf2efd4890d53274a8eae13f51f821557
  languageName: node
  linkType: hard

"@types/d3-force@npm:*":
  version: 3.0.10
  resolution: "@types/d3-force@npm:3.0.10"
  checksum: 10/9c35abed2af91b94fc72d6b477188626e628ed89a01016437502c1deaf558da934b5d0cc808c2f2979ac853b6302b3d6ef763eddaff3a55552a55c0be710d5ca
  languageName: node
  linkType: hard

"@types/d3-format@npm:*":
  version: 3.0.4
  resolution: "@types/d3-format@npm:3.0.4"
  checksum: 10/b937ecd2712d4aa38d5b4f5daab9cc8a576383868be1809e046aec99eeb1f1798c139f2e862dc400a82494c763be46087d154891773417f8eb53c73762ba3eb8
  languageName: node
  linkType: hard

"@types/d3-geo@npm:*":
  version: 3.1.0
  resolution: "@types/d3-geo@npm:3.1.0"
  dependencies:
    "@types/geojson": "npm:*"
  checksum: 10/e759d98470fe605ff0088247af81c3197cefce72b16eafe8acae606216c3e0a9f908df4e7cd5005ecfe13b8ac8396a51aaa0d282f3ca7d1c3850313a13fac905
  languageName: node
  linkType: hard

"@types/d3-hierarchy@npm:*":
  version: 3.1.7
  resolution: "@types/d3-hierarchy@npm:3.1.7"
  checksum: 10/9ff6cdedf5557ef9e1e7a65ca3c6846c895c84c1184e11ec6fa48565e96ebf5482d8be5cc791a8bc7f7debbd0e62604ee3da3ddca4f9d58bf6c8b4030567c6c6
  languageName: node
  linkType: hard

"@types/d3-interpolate@npm:*, @types/d3-interpolate@npm:^3.0.1":
  version: 3.0.4
  resolution: "@types/d3-interpolate@npm:3.0.4"
  dependencies:
    "@types/d3-color": "npm:*"
  checksum: 10/72a883afd52c91132598b02a8cdfced9e783c54ca7e4459f9e29d5f45d11fb33f2cabc844e42fd65ba6e28f2a931dcce1add8607d2f02ef6fb8ea5b83ae84127
  languageName: node
  linkType: hard

"@types/d3-path@npm:*":
  version: 3.1.1
  resolution: "@types/d3-path@npm:3.1.1"
  checksum: 10/0437994d45d852ecbe9c4484e5abe504cd48751796d23798b6d829503a15563fdd348d93ac44489ba9c656992d16157f695eb889d9ce1198963f8e1dbabb1266
  languageName: node
  linkType: hard

"@types/d3-polygon@npm:*":
  version: 3.0.2
  resolution: "@types/d3-polygon@npm:3.0.2"
  checksum: 10/7cf1eadb54f02dd3617512b558f4c0f3811f8a6a8c887d9886981c3cc251db28b68329b2b0707d9f517231a72060adbb08855227f89bef6ef30caedc0a67cab2
  languageName: node
  linkType: hard

"@types/d3-quadtree@npm:*":
  version: 3.0.6
  resolution: "@types/d3-quadtree@npm:3.0.6"
  checksum: 10/4c260c9857d496b7f112cf57680c411c1912cc72538a5846c401429e3ed89a097c66410cfd38b394bfb4733ec2cb47d345b4eb5e202cbfb8e78ab044b535be02
  languageName: node
  linkType: hard

"@types/d3-random@npm:*":
  version: 3.0.3
  resolution: "@types/d3-random@npm:3.0.3"
  checksum: 10/2c126dda6846f6c7e02c9123a30b4cdf27f3655d19b78456bbb330fbac27acceeeb987318055d3964dba8e6450377ff737db91d81f27c81ca6f4522c9b994ef2
  languageName: node
  linkType: hard

"@types/d3-scale-chromatic@npm:*":
  version: 3.1.0
  resolution: "@types/d3-scale-chromatic@npm:3.1.0"
  checksum: 10/6b04af931b7cd4aa09f21519970cab44aaae181faf076013ab93ccb0d550ec16f4c8d444c1e9dee1493be4261a8a8bb6f8e6356e6f4c6ba0650011b1e8a38aef
  languageName: node
  linkType: hard

"@types/d3-scale@npm:*, @types/d3-scale@npm:^4.0.2":
  version: 4.0.9
  resolution: "@types/d3-scale@npm:4.0.9"
  dependencies:
    "@types/d3-time": "npm:*"
  checksum: 10/2cae90a5e39252ae51388f3909ffb7009178582990462838a4edd53dd7e2e08121b38f0d2e1ac0e28e41167e88dea5b99e064ca139ba917b900a8020cf85362f
  languageName: node
  linkType: hard

"@types/d3-selection@npm:*, @types/d3-selection@npm:^3.0.3":
  version: 3.0.11
  resolution: "@types/d3-selection@npm:3.0.11"
  checksum: 10/2d2d993b9e9553d066566cb22916c632e5911090db99e247bd8c32855a344e6b7c25b674f3c27956c367a6b3b1214b09931ce854788c3be2072003e01f2c75d7
  languageName: node
  linkType: hard

"@types/d3-shape@npm:*, @types/d3-shape@npm:^3.1.0":
  version: 3.1.7
  resolution: "@types/d3-shape@npm:3.1.7"
  dependencies:
    "@types/d3-path": "npm:*"
  checksum: 10/b7ddda2a9c916ba438308bfa6e53fa2bb11c2ce13537ba2a7816c16f9432287b57901921c7231d2924f2d7d360535c3795f017865ab05abe5057c6ca06ca81df
  languageName: node
  linkType: hard

"@types/d3-time-format@npm:*":
  version: 4.0.3
  resolution: "@types/d3-time-format@npm:4.0.3"
  checksum: 10/9dfc1516502ac1c657d6024bdb88b6dc7e21dd7bff88f6187616cf9a0108250f63507a2004901ece4f97cc46602005a2ca2d05c6dbe53e8a0f6899bd60d4ff7a
  languageName: node
  linkType: hard

"@types/d3-time@npm:*, @types/d3-time@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/d3-time@npm:3.0.4"
  checksum: 10/b1eb4255066da56023ad243fd4ae5a20462d73bd087a0297c7d49ece42b2304a4a04297568c604a38541019885b2bc35a9e0fd704fad218e9bc9c5f07dc685ce
  languageName: node
  linkType: hard

"@types/d3-timer@npm:*, @types/d3-timer@npm:^3.0.0":
  version: 3.0.2
  resolution: "@types/d3-timer@npm:3.0.2"
  checksum: 10/1643eebfa5f4ae3eb00b556bbc509444d88078208ec2589ddd8e4a24f230dd4cf2301e9365947e70b1bee33f63aaefab84cd907822aae812b9bc4871b98ab0e1
  languageName: node
  linkType: hard

"@types/d3-transition@npm:*":
  version: 3.0.9
  resolution: "@types/d3-transition@npm:3.0.9"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10/dad647c485440f176117e8a45f31aee9427d8d4dfa174eaa2f01e702641db53ad0f752a144b20987c7189723c4f0afe0bf0f16d95b2a91aa28937eee4339c161
  languageName: node
  linkType: hard

"@types/d3-zoom@npm:*, @types/d3-zoom@npm:^3.0.1":
  version: 3.0.8
  resolution: "@types/d3-zoom@npm:3.0.8"
  dependencies:
    "@types/d3-interpolate": "npm:*"
    "@types/d3-selection": "npm:*"
  checksum: 10/cc6ba975cf4f55f94933413954d81b87feb1ee8b8cee8f2202cf526f218dcb3ba240cbeb04ed80522416201c4a7394b37de3eb695d840a36d190dfb2d3e62cb5
  languageName: node
  linkType: hard

"@types/d3@npm:^7.4.0":
  version: 7.4.3
  resolution: "@types/d3@npm:7.4.3"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/d3-axis": "npm:*"
    "@types/d3-brush": "npm:*"
    "@types/d3-chord": "npm:*"
    "@types/d3-color": "npm:*"
    "@types/d3-contour": "npm:*"
    "@types/d3-delaunay": "npm:*"
    "@types/d3-dispatch": "npm:*"
    "@types/d3-drag": "npm:*"
    "@types/d3-dsv": "npm:*"
    "@types/d3-ease": "npm:*"
    "@types/d3-fetch": "npm:*"
    "@types/d3-force": "npm:*"
    "@types/d3-format": "npm:*"
    "@types/d3-geo": "npm:*"
    "@types/d3-hierarchy": "npm:*"
    "@types/d3-interpolate": "npm:*"
    "@types/d3-path": "npm:*"
    "@types/d3-polygon": "npm:*"
    "@types/d3-quadtree": "npm:*"
    "@types/d3-random": "npm:*"
    "@types/d3-scale": "npm:*"
    "@types/d3-scale-chromatic": "npm:*"
    "@types/d3-selection": "npm:*"
    "@types/d3-shape": "npm:*"
    "@types/d3-time": "npm:*"
    "@types/d3-time-format": "npm:*"
    "@types/d3-timer": "npm:*"
    "@types/d3-transition": "npm:*"
    "@types/d3-zoom": "npm:*"
  checksum: 10/12234aa093c8661546168becdd8956e892b276f525d96f65a7b32fed886fc6a569fe5a1171bff26fef2a5663960635f460c9504a6f2d242ba281a2b6c8c6465c
  languageName: node
  linkType: hard

"@types/dagre-layout@npm:^0.8.5":
  version: 0.8.5
  resolution: "@types/dagre-layout@npm:0.8.5"
  checksum: 10/edc267d13beedaf738d03809d07bc7c6fbab2d9ea2bacee002aaeee33174069691d4b46c32ab3a0b15ef0a2d40a15c0d75b1badc6194559276f5a3fee2ac38b4
  languageName: node
  linkType: hard

"@types/dagre@npm:^0.7.52":
  version: 0.7.52
  resolution: "@types/dagre@npm:0.7.52"
  checksum: 10/89e046e73f9f83855fcecc0f79838e0e3e0e42d88b6cc42bb573249364a606909ff7ded5b6a0377246eb0648047b330b5003c8dd975358de3135635ddae0f589
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10/47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/emscripten@npm:^1.39.6":
  version: 1.40.1
  resolution: "@types/emscripten@npm:1.40.1"
  checksum: 10/d1aa8b91416c0d8ea7d2f3b99c8c40327350dff632ad98cb67f9df9afa13efd4d755c273b49a37797390a94180c7cc98397bbbe1d8def4ea1d5d3bff9c816ac9
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.7":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "npm:*"
    "@types/estree": "npm:*"
  checksum: 10/e2889a124aaab0b89af1bab5959847c5bec09809209255de0e63b9f54c629a94781daa04adb66bffcdd742f5e25a17614fb933965093c0eea64aacda4309380e
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "npm:*"
    "@types/json-schema": "npm:*"
  checksum: 10/719fcd255760168a43d0e306ef87548e1e15bffe361d5f4022b0f266575637acc0ecb85604ac97879ee8ae83c6a6d0613b0ed31d0209ddf22a0fe6d608fc56fe
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10/25a4c16a6752538ffde2826c2cc0c6491d90e69cd6187bef4a006dd2c3c45469f049e643d7e516c515f21484dc3d48fd5c870be158a5beb72f5baf3dc43e4099
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:*, @types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.6
  resolution: "@types/express-serve-static-core@npm:5.0.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/9dc51bdee7da9ad4792e97dd1be5b3071b5128f26d3b87a753070221bb36c8f9d16074b95a8b972acc965641e987b1e279a44675e7312ac8f3e18ec9abe93940
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.21, @types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/a2e00b6c5993f0dd63ada2239be81076fe0220314b9e9fde586e8946c9c09ce60f9a2dd0d74410ee2b5fd10af8c3e755a32bb3abf134533e2158142488995455
  languageName: node
  linkType: hard

"@types/express@npm:*, @types/express@npm:^5.0.0":
  version: 5.0.3
  resolution: "@types/express@npm:5.0.3"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^5.0.0"
    "@types/serve-static": "npm:*"
  checksum: 10/bb6f10c14c8e3cce07f79ee172688aa9592852abd7577b663cd0c2054307f172c2b2b36468c918fed0d4ac359b99695807b384b3da6157dfa79acbac2226b59b
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.21":
  version: 4.17.23
  resolution: "@types/express@npm:4.17.23"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10/cf4d540bbd90801cdc79a46107b8873404698a7fd0c3e8dd42989d52d3bd7f5b8768672e54c20835e41e27349c319bb47a404ad14c0f8db0e9d055ba1cb8a05b
  languageName: node
  linkType: hard

"@types/geojson@npm:*":
  version: 7946.0.16
  resolution: "@types/geojson@npm:7946.0.16"
  checksum: 10/34d07421bdd60e7b99fa265441d17ac6e9aef48e3ce22d04324127d0de1daf7fbaa0bd3be1cece2092eb6995f21da84afa5231e24621a2910ff7340bc98f496f
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/79d746a8f053954bba36bd3d94a90c78de995d126289d656fb3271dd9f1229d33f678da04d10bce6be440494a5a73438e2e363e92802d16b8315b051036c5256
  languageName: node
  linkType: hard

"@types/hast@npm:^2.0.0":
  version: 2.3.10
  resolution: "@types/hast@npm:2.3.10"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10/41531b7fbf590b02452996fc63272479c20a07269e370bd6514982cbcd1819b4b84d3ea620f2410d1b9541a23d08ce2eeb0a592145d05e00e249c3d56700d460
  languageName: node
  linkType: hard

"@types/history@npm:^4.7.11":
  version: 4.7.11
  resolution: "@types/history@npm:4.7.11"
  checksum: 10/1da529a3485f3015daf794effa3185493bf7dd2551c26932389c614f5a0aab76ab97645897d1eef9c74ead216a3848fcaa019f165bbd6e4b71da6eff164b4c68
  languageName: node
  linkType: hard

"@types/html-minifier-terser@npm:^6.0.0":
  version: 6.1.0
  resolution: "@types/html-minifier-terser@npm:6.1.0"
  checksum: 10/06bb3e1e8ebff43602c826d67f53f1fd3a6b9c751bfbc67d7ea4e85679446a639e20e60adad8c9d44ab4baf1337b3861b91e7e5e2be798575caf0cc1a5712552
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: 10/a59566cff646025a5de396d6b3f44a39ab6a74f2ed8150692e0f31cc52f3661a68b04afe3166ebe0d566bd3259cb18522f46e949576d5204781cd6452b7fe0c5
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.5
  resolution: "@types/http-errors@npm:2.0.5"
  checksum: 10/a88da669366bc483e8f3b3eb3d34ada5f8d13eeeef851b1204d77e2ba6fc42aba4566d877cca5c095204a3f4349b87fe397e3e21288837bdd945dd514120755b
  languageName: node
  linkType: hard

"@types/http-proxy@npm:^1.17.8":
  version: 1.17.16
  resolution: "@types/http-proxy@npm:1.17.16"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/a054ac8f5301acfcfdcec3a775f52dc371180bbe60037906534312f10cceb3799b4a16e46c56c22f9925d078e11dcda1723c38f1ddd124be8169a4cccca69c8c
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10/3feac423fd3e5449485afac999dcfcb3d44a37c830af898b689fadc65d26526460bedb889db278e0d4d815a670331796494d073a10ee6e3a6526301fe7415778
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10/b91e9b60f865ff08cb35667a427b70f6c2c63e88105eadd29a112582942af47ed99c60610180aa8dcc22382fa405033f141c119c69b95db78c4c709fbadfeeb4
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10/93eb18835770b3431f68ae9ac1ca91741ab85f7606f310a34b3586b5a34450ec038c3eed7ab19266635499594de52ff73723a54a72a75b9f7d6a956f01edee95
  languageName: node
  linkType: hard

"@types/jest@npm:^29.5.13, @types/jest@npm:^29.5.14":
  version: 29.5.14
  resolution: "@types/jest@npm:29.5.14"
  dependencies:
    expect: "npm:^29.0.0"
    pretty-format: "npm:^29.0.0"
  checksum: 10/59ec7a9c4688aae8ee529316c43853468b6034f453d08a2e1064b281af9c81234cec986be796288f1bbb29efe943bc950e70c8fa8faae1e460d50e3cf9760f9b
  languageName: node
  linkType: hard

"@types/jquery@npm:^3.5.31":
  version: 3.5.32
  resolution: "@types/jquery@npm:3.5.32"
  dependencies:
    "@types/sizzle": "npm:*"
  checksum: 10/2c67cac338828870ead5c5e608f5fa5ab8101598ed4572cf49b58c342adffe8918d2e2fc94d7954e6b98a889cef8c3f4e6f44b8fecb75e80854b0f9cf9dd18a1
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/mdast@npm:^3.0.0":
  version: 3.0.15
  resolution: "@types/mdast@npm:3.0.15"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10/050a5c1383928b2688dd145382a22535e2af87dc3fd592c843abb7851bcc99893a1ee0f63be19fc4e89779387ec26a57486cfb425b016c0b2a98a17fc4a1e8b3
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10/e29a5f9c4776f5229d84e525b7cd7dd960b51c30a0fb9a028c0821790b82fca9f672dab56561e2acd9e8eed51d431bde52eafdfef30f643586c4162f1aecfc78
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10/532d2ebb91937ccc4a89389715e5b47d4c66e708d15942fe6cc25add6dc37b2be058230a327dd50f43f89b8b6d5d52b74685a9e8f70516edfc9bdd6be910eff4
  languageName: node
  linkType: hard

"@types/node-forge@npm:^1.3.0":
  version: 1.3.11
  resolution: "@types/node-forge@npm:1.3.11"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/670c9b377c48189186ec415e3c8ed371f141ecc1a79ab71b213b20816adeffecba44dae4f8406cc0d09e6349a4db14eb8c5893f643d8e00fa19fc035cf49dee0
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 24.0.3
  resolution: "@types/node@npm:24.0.3"
  dependencies:
    undici-types: "npm:~7.8.0"
  checksum: 10/6cce0afa9b0ff7f8eab7cb0339909c1e4ef480b824b8de5adc9cee05dac63ee3d8c7a46e1f95f13ecc94e84608118741f9949527a92fbf3f0e1f7714b37a7b61
  languageName: node
  linkType: hard

"@types/node@npm:^18.17.15":
  version: 18.19.112
  resolution: "@types/node@npm:18.19.112"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10/1d0150b4afbfa76ddcdbdcfaaa695dd1dc7485047d0c7e0b22207a0ffb61dab5bc44d536e4d2c3cb85c91ebb519479bfcd7033e76054fbc96fa6d13a86d9b26d
  languageName: node
  linkType: hard

"@types/node@npm:^22.7.5, @types/node@npm:^22.9.0":
  version: 22.15.32
  resolution: "@types/node@npm:22.15.32"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10/10b4c106d0c512a1d35ec08142bd7fb5cf2e1df93fc5627b3c69dd843dec4be07a47f1fa7ede232ad84762d75a372ea35028b79ee1e753b6f2adecd0b2cb2f71
  languageName: node
  linkType: hard

"@types/prop-types@npm:*, @types/prop-types@npm:^15.0.0":
  version: 15.7.15
  resolution: "@types/prop-types@npm:15.7.15"
  checksum: 10/31aa2f59b28f24da6fb4f1d70807dae2aedfce090ec63eaf9ea01727a9533ef6eaf017de5bff99fbccad7d1c9e644f52c6c2ba30869465dd22b1a7221c29f356
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10/1909205514d22b3cbc7c2314e2bd8056d5f05dfb21cf4377f0730ee5e338ea19957c41735d5e4806c746176563f50005bbab602d8358432e25d900bdf4970826
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10/95640233b689dfbd85b8c6ee268812a732cf36d5affead89e806fe30da9a430767af8ef2cd661024fd97e19d61f3dec75af2df5e80ec3bea000019ab7028629a
  languageName: node
  linkType: hard

"@types/react-dom@npm:^18.3.1, @types/react-dom@npm:~18.3":
  version: 18.3.7
  resolution: "@types/react-dom@npm:18.3.7"
  peerDependencies:
    "@types/react": ^18.0.0
  checksum: 10/317569219366d487a3103ba1e5e47154e95a002915fdcf73a44162c48fe49c3a57fcf7f57fc6979e70d447112681e6b13c6c3c1df289db8b544df4aab2d318f3
  languageName: node
  linkType: hard

"@types/react-router-dom@npm:^5.3.3":
  version: 5.3.3
  resolution: "@types/react-router-dom@npm:5.3.3"
  dependencies:
    "@types/history": "npm:^4.7.11"
    "@types/react": "npm:*"
    "@types/react-router": "npm:*"
  checksum: 10/28c4ea48909803c414bf5a08502acbb8ba414669b4b43bb51297c05fe5addc4df0b8fd00e0a9d1e3535ec4073ef38aaafac2c4a2b95b787167d113bc059beff3
  languageName: node
  linkType: hard

"@types/react-router@npm:*":
  version: 5.1.20
  resolution: "@types/react-router@npm:5.1.20"
  dependencies:
    "@types/history": "npm:^4.7.11"
    "@types/react": "npm:*"
  checksum: 10/72d78d2f4a4752ec40940066b73d7758a0824c4d0cbeb380ae24c8b1cdacc21a6fc835a99d6849b5b295517a3df5466fc28be038f1040bd870f8e39e5ded43a4
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10/a3e6fe0f60f22828ef887f30993aa147b71532d7b1219dd00d246277eb7a9ca01ec533096237fa21ca1bccb3653373b4e8e59e5ae59f9c793058384bbc1f4d5c
  languageName: node
  linkType: hard

"@types/react@npm:^18.3.11, @types/react@npm:~18.3":
  version: 18.3.23
  resolution: "@types/react@npm:18.3.23"
  dependencies:
    "@types/prop-types": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 10/4b965dffe34a1f8aac8e2d7e976f113373f38134f9e37239f7e75d7ac6b3c2e1333a8df21febf1fe7749640f8de5708f7668cdfc70bffebda1cc4d3346724fd5
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/6ac4b35723429b11b117e813c7acc42c3af8b5554caaf1fc750404c1ae59f9b7376bc69b9e9e194a5a97357a597c2228b7173d317320f0360d617b6425212f58
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.2":
  version: 0.12.2
  resolution: "@types/retry@npm:0.12.2"
  checksum: 10/e5675035717b39ce4f42f339657cae9637cf0c0051cf54314a6a2c44d38d91f6544be9ddc0280587789b6afd056be5d99dbe3e9f4df68c286c36321579b1bf4a
  languageName: node
  linkType: hard

"@types/semver@npm:^7.1.0":
  version: 7.7.0
  resolution: "@types/semver@npm:7.7.0"
  checksum: 10/ee4514c6c852b1c38f951239db02f9edeea39f5310fad9396a00b51efa2a2d96b3dfca1ae84c88181ea5b7157c57d32d7ef94edacee36fbf975546396b85ba5b
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.5
  resolution: "@types/send@npm:0.17.5"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10/b68ae8f9ba9328a4f276cd010914ed43b96371fbf34c7aa08a9111bff36661810bb14b96647e4a92e319dbd2689dc107fb0f9194ec3fa9335c162dc134026240
  languageName: node
  linkType: hard

"@types/serve-index@npm:^1.9.4":
  version: 1.9.4
  resolution: "@types/serve-index@npm:1.9.4"
  dependencies:
    "@types/express": "npm:*"
  checksum: 10/72727c88d54da5b13275ebfb75dcdc4aa12417bbe9da1939e017c4c5f0c906fae843aa4e0fbfe360e7ee9df2f3d388c21abfc488f77ce58693fb57809f8ded92
  languageName: node
  linkType: hard

"@types/serve-static@npm:*, @types/serve-static@npm:^1.15.5":
  version: 1.15.8
  resolution: "@types/serve-static@npm:1.15.8"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/c031f870df6056a4c0a5a0ae94c5584006ab55400c74ae44de4d68d89338fbe982422861bad478b89a073f671efca454689fd28b6147358d6adc8edbc599caea
  languageName: node
  linkType: hard

"@types/sizzle@npm:*":
  version: 2.3.9
  resolution: "@types/sizzle@npm:2.3.9"
  checksum: 10/413811a79e7e9f1d8f47e6047ae0aea1530449d612304cdda1c30018e3d053b8544861ec2c70bdeca75a0a010192e6bb78efc6fb4caaafdd65c4eee90066686a
  languageName: node
  linkType: hard

"@types/sockjs@npm:^0.3.36":
  version: 0.3.36
  resolution: "@types/sockjs@npm:0.3.36"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/b4b5381122465d80ea8b158537c00bc82317222d3fb31fd7229ff25b31fa89134abfbab969118da55622236bf3d8fee75759f3959908b5688991f492008f29bc
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10/72576cc1522090fe497337c2b99d9838e320659ac57fa5560fcbdcbafcf5d0216c6b3a0a8a4ee4fdb3b1f5e3420aa4f6223ab57b82fef3578bec3206425c6cf5
  languageName: node
  linkType: hard

"@types/treeify@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/treeify@npm:1.0.3"
  checksum: 10/777e579b30a916a781e7cbad2b7a76bc5473ff7bfe7167dd6de47f80f4386df5bf3d0dc34170afb75d52e75f6ed61cc109abf2324e093c1f9ecd4e79fec58d0c
  languageName: node
  linkType: hard

"@types/unist@npm:^2, @types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 10/6d436e832bc35c6dde9f056ac515ebf2b3384a1d7f63679d12358766f9b313368077402e9c1126a14d827f10370a5485e628bf61aa91117cf4fc882423191a4e
  languageName: node
  linkType: hard

"@types/ws@npm:^8.5.10":
  version: 8.18.1
  resolution: "@types/ws@npm:8.18.1"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/1ce05e3174dcacf28dae0e9b854ef1c9a12da44c7ed73617ab6897c5cbe4fccbb155a20be5508ae9a7dde2f83bd80f5cf3baa386b934fc4b40889ec963e94f3a
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10/a794eb750e8ebc6273a51b12a0002de41343ffe46befef460bdbb57262d187fdf608bc6615b7b11c462c63c3ceb70abe2564c8dd8ee0f7628f38a314f74a9b9b
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10/16f6681bf4d99fb671bf56029141ed01db2862e3db9df7fc92d8bea494359ac96a1b4b1c35a836d1e95e665fb18ad753ab2015fc0db663454e8fd4e5d5e2ef91
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
  checksum: 10/f83e6abe38057f5d87c1fb356513a371a8b43c9b87657f2790741a66b1ef8ecf958d1391bc42f27c5fb33f58ab8286a38ea849fdd21f433cd4df1307424bab45
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: 10/e866ec8433f4a70baa511df5e8f2ebcd6c24f4e2cc6274c7c5aabe2bcce3459ea4680e0f35d450e1f3602acf3913b6b8e4f15069c8cfd34ae8609fb9a7d01795
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 10/48b5df7fd3095bb252f59a139fe2cbd999a62ac9b488123e9a0da3906ad8a2f2da7b2eb21d328c01a90da987380928706395c2897d1f3ed9e2125b6d75a920d0
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: 10/9690afeafa5e765a34620aa6216e9d40f9126d4e37e9726a2594bf60cab6b211ef20ab6670fd3c4449dd4a3497e69e49b2b725c8da0fb213208c7f45f15f5d5b
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": "npm:1.13.2"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/e4c7d0b09811e1cda8eec644a022b560b28f4e974f50195375ccd007df5ee48a922a6dcff5ac40b6a8ec850d56d0ea6419318eee49fec7819ede14e90417a6a4
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 10/3edd191fff7296df1ef3b023bdbe6cb5ea668f6386fd197ccfce46015c6f2a8cc9763cfb86503a0b94973ad27996645afff2252ee39a236513833259a47af6ed
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
  checksum: 10/6b73874f906532512371181d7088460f767966f26309e836060c5a8e4e4bfe6d523fb5f4c034b34aa22ebb1192815f95f0e264298769485c1f0980fdd63ae0ce
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": "npm:^1.2.0"
  checksum: 10/d7e3520baa37a7309fa7db4d73d69fb869878853b1ebd4b168821bd03fcc4c0e1669c06231315b0039035d9a7a462e53de3ad982da4a426a4b0743b5888e8673
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/3a10542c86807061ec3230bac8ee732289c852b6bceb4b88ebd521a12fbcecec7c432848284b298154f28619e2746efbed19d6904aef06c49ef20a0b85f650cf
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 10/27885e5d19f339501feb210867d69613f281eda695ac508f04d69fa3398133d05b6870969c0242b054dc05420ed1cc49a64dea4fe0588c18d211cddb0117cc54
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-section": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-opt": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
    "@webassemblyjs/wast-printer": "npm:1.14.1"
  checksum: 10/c62c50eadcf80876713f8c9f24106b18cf208160ab842fcb92060fd78c37bf37e7fcf0b7cbf1afc05d230277c2ce0f3f728432082c472dd1293e184a95f9dbdd
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10/6085166b0987d3031355fe17a4f9ef0f412e08098d95454059aced2bd72a4c3df2bc099fa4d32d640551fc3eca1ac1a997b44432e46dc9d84642688e42c17ed4
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
  checksum: 10/fa5d1ef8d2156e7390927f938f513b7fb4440dd6804b3d6c8622b7b1cf25a3abf1a5809f615896d4918e04b27b52bc3cbcf18faf2d563cb563ae0a9204a492db
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10/07d9805fda88a893c984ed93d5a772d20d671e9731358ab61c6c1af8e0e58d1c42fc230c18974dfddebc9d2dd7775d514ba4d445e70080b16478b4b16c39c7d9
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/cef09aad2fcd291bfcf9efdae2ea1e961a1ba0f925d1d9dcdd8c746d32fbaf431b6d26a0241699c0e39f82139018aa720b4ceb84ac6f4c78f13072747480db69
  languageName: node
  linkType: hard

"@webpack-cli/configtest@npm:^3.0.1":
  version: 3.0.1
  resolution: "@webpack-cli/configtest@npm:3.0.1"
  peerDependencies:
    webpack: ^5.82.0
    webpack-cli: 6.x.x
  checksum: 10/a83301ff360de6c36fe98766f1f391db6149f0806450ce31484c49df3902584f73385453da23f3324a605d5afad4d2889654ada679afd49e35c59a2c4769ee97
  languageName: node
  linkType: hard

"@webpack-cli/info@npm:^3.0.1":
  version: 3.0.1
  resolution: "@webpack-cli/info@npm:3.0.1"
  peerDependencies:
    webpack: ^5.82.0
    webpack-cli: 6.x.x
  checksum: 10/0ddcfd8b370d924f71cc085b17b31a77b362d8046fedb38ac601042733568cda05b0c8c7b1e0e1e050dc926ee76f754cd9c4f351e2b361a0d157465f8b03b689
  languageName: node
  linkType: hard

"@webpack-cli/serve@npm:^3.0.1":
  version: 3.0.1
  resolution: "@webpack-cli/serve@npm:3.0.1"
  peerDependencies:
    webpack: ^5.82.0
    webpack-cli: 6.x.x
  peerDependenciesMeta:
    webpack-dev-server:
      optional: true
  checksum: 10/688138f7b2f96ed7a5aae2798bd647e4db0fdf8e86850a493c987049eec6faf63ba78d8f08b4f0a9e41dc459cba80abfb621ae1a45890bb0fa2c09baef4db75b
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: 10/ab033b032927d77e2f9fa67accdf31b1ca7440974c21c9cfabc8349e10ca2817646171c4f23be98d0e31896d6c2c3462a074fe37752e523abc3e45c79254259c
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 10/7217bae9fe240e0d804969e7b2af11cb04ec608837c78b56ca88831991b287e232a0b7fce8d548beaff42aaf0197ffa471d81be6ac4c4e53b0148025a2c076ec
  languageName: node
  linkType: hard

"@yarnpkg/core@npm:^4.4.2":
  version: 4.4.2
  resolution: "@yarnpkg/core@npm:4.4.2"
  dependencies:
    "@arcanis/slice-ansi": "npm:^1.1.1"
    "@types/semver": "npm:^7.1.0"
    "@types/treeify": "npm:^1.0.0"
    "@yarnpkg/fslib": "npm:^3.1.2"
    "@yarnpkg/libzip": "npm:^3.2.1"
    "@yarnpkg/parsers": "npm:^3.0.3"
    "@yarnpkg/shell": "npm:^4.1.3"
    camelcase: "npm:^5.3.1"
    chalk: "npm:^4.1.2"
    ci-info: "npm:^4.0.0"
    clipanion: "npm:^4.0.0-rc.2"
    cross-spawn: "npm:^7.0.3"
    diff: "npm:^5.1.0"
    dotenv: "npm:^16.3.1"
    fast-glob: "npm:^3.2.2"
    got: "npm:^11.7.0"
    hpagent: "npm:^1.2.0"
    lodash: "npm:^4.17.15"
    micromatch: "npm:^4.0.2"
    p-limit: "npm:^2.2.0"
    semver: "npm:^7.1.2"
    strip-ansi: "npm:^6.0.0"
    tar: "npm:^6.0.5"
    tinylogic: "npm:^2.0.0"
    treeify: "npm:^1.1.0"
    tslib: "npm:^2.4.0"
  checksum: 10/3b4dbd753a4f2e66e003e6632980a86457a0647b6d131ddfce63808d3b009892f9464d61c5303de9fe0a01097ac69da01c148b8c09646a9eb2be16e82b210a86
  languageName: node
  linkType: hard

"@yarnpkg/fslib@npm:^3.1.2":
  version: 3.1.2
  resolution: "@yarnpkg/fslib@npm:3.1.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/be7ed5d418c1adadd24bfa68abb0a95aceb39aafb6dd22644fcae3a8b0185d30868904f4df59de94536ea3eb36bfb4ee1304cc058afeec8d9ee6837d28f6bcd4
  languageName: node
  linkType: hard

"@yarnpkg/libzip@npm:^3.2.1":
  version: 3.2.1
  resolution: "@yarnpkg/libzip@npm:3.2.1"
  dependencies:
    "@types/emscripten": "npm:^1.39.6"
    "@yarnpkg/fslib": "npm:^3.1.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    "@yarnpkg/fslib": ^3.1.2
  checksum: 10/c8c3bc43777e2ef3c812c991266b9bbccef4d01cea5de44a6b8c359c953dab5325e26fda5640ff611567b61f9dd62ac3a0c3ba79343eb53224d733915a7eb664
  languageName: node
  linkType: hard

"@yarnpkg/nm@npm:^4.0.7":
  version: 4.0.7
  resolution: "@yarnpkg/nm@npm:4.0.7"
  dependencies:
    "@yarnpkg/core": "npm:^4.4.2"
    "@yarnpkg/fslib": "npm:^3.1.2"
    "@yarnpkg/pnp": "npm:^4.1.1"
  checksum: 10/006afb782d464305972e993754359bba25376896b51168ef7469ef296679ba3529f2154603f468fc575498fb4a91f31ffb63f63fb5802cd9295be7d11a67e0f6
  languageName: node
  linkType: hard

"@yarnpkg/parsers@npm:^3.0.3":
  version: 3.0.3
  resolution: "@yarnpkg/parsers@npm:3.0.3"
  dependencies:
    js-yaml: "npm:^3.10.0"
    tslib: "npm:^2.4.0"
  checksum: 10/379f7ff8fc1b37d3818dfeba4e18a72f8e9817bb41aab9332b50bbc843e45c9bf135563a7a06882ffb50e4cdd29c8da33c8e4f3739201de2fbcd38ecb59e3a8e
  languageName: node
  linkType: hard

"@yarnpkg/pnp@npm:^4.1.1":
  version: 4.1.1
  resolution: "@yarnpkg/pnp@npm:4.1.1"
  dependencies:
    "@types/node": "npm:^18.17.15"
    "@yarnpkg/fslib": "npm:^3.1.2"
  checksum: 10/652f323b51187f58b0a5c574a2628d5f57adacb8281ebd394bf104bf81f9f8e10f4aec2520023e3b26e35cba4bf3d7f629b86393e99883265f5ef3f84421735f
  languageName: node
  linkType: hard

"@yarnpkg/pnpify@npm:~4.1":
  version: 4.1.5
  resolution: "@yarnpkg/pnpify@npm:4.1.5"
  dependencies:
    "@yarnpkg/core": "npm:^4.4.2"
    "@yarnpkg/fslib": "npm:^3.1.2"
    "@yarnpkg/nm": "npm:^4.0.7"
    clipanion: "npm:^4.0.0-rc.2"
    tslib: "npm:^2.4.0"
  bin:
    pnpify: ./lib/cli.js
  checksum: 10/4ff8e3c052e2deb28764c352311e53d3961c4053245c0d7daec6a243eabac9f1b3b91113b74ffdd582ae4d1b7e0d70ffe1a927c5723b6f94654e8de5c168ce53
  languageName: node
  linkType: hard

"@yarnpkg/sdks@npm:~3.2":
  version: 3.2.2
  resolution: "@yarnpkg/sdks@npm:3.2.2"
  dependencies:
    "@yarnpkg/core": "npm:^4.4.2"
    "@yarnpkg/fslib": "npm:^3.1.2"
    "@yarnpkg/parsers": "npm:^3.0.3"
    chalk: "npm:^4.1.2"
    clipanion: "npm:^4.0.0-rc.2"
    comment-json: "npm:^2.2.0"
    lodash: "npm:^4.17.15"
    tslib: "npm:^2.4.0"
  bin:
    sdks: ./lib/cli.js
  checksum: 10/7adcb0e394c26089f86ce1922e4329370aa6e5083a23bc863eda3093022e47c1b0f64ac35d1d45f8b9de5e9eea3086f789529ac65300567b5f37f1de68588678
  languageName: node
  linkType: hard

"@yarnpkg/shell@npm:^4.1.3":
  version: 4.1.3
  resolution: "@yarnpkg/shell@npm:4.1.3"
  dependencies:
    "@yarnpkg/fslib": "npm:^3.1.2"
    "@yarnpkg/parsers": "npm:^3.0.3"
    chalk: "npm:^4.1.2"
    clipanion: "npm:^4.0.0-rc.2"
    cross-spawn: "npm:^7.0.3"
    fast-glob: "npm:^3.2.2"
    micromatch: "npm:^4.0.2"
    tslib: "npm:^2.4.0"
  bin:
    shell: ./lib/cli.js
  checksum: 10/5994f92adf960071ac938653c5ad09746285d3fdc452fc6fdd30c3a832b612cc208e8d2274731e35957b457b168d6be524f5ce30ceb18542532d9326b422421b
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10/67eaaa90e2917c58418e7a9b89392002d2b1ccd69bcca4799135d0c632f3b082f23f4ae4ddeedbced5aa59bcc7bdf4699c69ebed4593696c922462b7bc5744d6
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10/77f2de5051a631cf1729c090e5759148459cdb76b5f5c70f890503d629cf5052357b0ce783c0f976dd8a93c5150f59f6d18df1def3f502396a20f81282482fa4
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10/70c263ded219bf277ffd9127f793b625f10a46113b2e901e150da41931fcfd7f5592da6d66862f4449bb157ffe65867c3294a7df1d661cc232c4163d5a1718ed
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10/5021f96ab7ddd03a4005326bd06f45f448ebfbb0fe7018b1b70b6c28142fa68372bda2057359814b83fd0b2d4c8726c297f0a7557b15377be7b56ce5344533d8
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10/ee3c62162c953e91986c838f004132b6a253d700f1e51253b99791e2dbfdb39161bc950ebdc2f156f8568035bb5ed8be7bd78289cd9ecbf3381fe8f5b82e3f33
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10/8661034456193ffeda0c15c8c564a9636b0c04094b7f78bd01517929c17c504090a60f7a75f949f5af91289c264d3e1001d91492c1bd58efc8e100500ce04de2
  languageName: node
  linkType: hard

"ansi-html-community@npm:^0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 10/08df3696720edacd001a8d53b197bb5728242c55484680117dab9f7633a6320e961a939bddd88ee5c71d4a64f3ddb49444d1c694bd0668adbb3f95ba114f2386
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10/d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10/d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10/3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10/c6a621343a553ff3779390bb5ee9c2263d6643ebcd7843227bdde6cc7adbed796eb5540ca98db19e3fd7b4714e1faa51551f8849b268bb62df27ddb15cbcd91e
  languageName: node
  linkType: hard

"aria-query@npm:5.3.0":
  version: 5.3.0
  resolution: "aria-query@npm:5.3.0"
  dependencies:
    dequal: "npm:^2.0.3"
  checksum: 10/c3e1ed127cc6886fea4732e97dd6d3c3938e64180803acfb9df8955517c4943760746ffaf4020ce8f7ffaa7556a3b5f85c3769a1f5ca74a1288e02d042f9ae4e
  languageName: node
  linkType: hard

"aria-query@npm:^5.0.0":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: 10/b2fe9bc98bd401bc322ccb99717c1ae2aaf53ea0d468d6e7aebdc02fac736e4a99b46971ee05b783b08ade23c675b2d8b60e4a1222a95f6e27bc4d2a0bfdcc03
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"attr-accept@npm:^2.2.2, attr-accept@npm:^2.2.4":
  version: 2.2.5
  resolution: "attr-accept@npm:2.2.5"
  checksum: 10/474b1c53e62c5b881c745d1f098196f190c8b493245e95d4b0fea9298d3acb56f551868fc12806885277e55e9d8ad3c5963e92d93456f4e4081dfc5190977bfd
  languageName: node
  linkType: hard

"axios@npm:1.8.4":
  version: 1.8.4
  resolution: "axios@npm:1.8.4"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10/a10f0dd836613924e48cf03dc2eff3fd21b14f764807aedaee4880a70c0f142aaebdb21da7ce27104d4c16ca00d0e452a20a20851f60e385a8d5bad1ae909d46
  languageName: node
  linkType: hard

"babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": "npm:^29.7.0"
    "@types/babel__core": "npm:^7.1.14"
    babel-plugin-istanbul: "npm:^6.1.1"
    babel-preset-jest: "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 10/8a0953bd813b3a8926008f7351611055548869e9a53dd36d6e7e96679001f71e65fd7dbfe253265c3ba6a4e630dc7c845cf3e78b17d758ef1880313ce8fba258
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-instrument: "npm:^5.0.4"
    test-exclude: "npm:^6.0.0"
  checksum: 10/ffd436bb2a77bbe1942a33245d770506ab2262d9c1b3c1f1da7f0592f78ee7445a95bc2efafe619dd9c1b6ee52c10033d6c7d29ddefe6f5383568e60f31dfe8d
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": "npm:^7.3.3"
    "@babel/types": "npm:^7.3.3"
    "@types/babel__core": "npm:^7.1.14"
    "@types/babel__traverse": "npm:^7.0.6"
  checksum: 10/9bfa86ec4170bd805ab8ca5001ae50d8afcb30554d236ba4a7ffc156c1a92452e220e4acbd98daefc12bf0216fccd092d0a2efed49e7e384ec59e0597a926d65
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/e238534f345edb26471438cdef8f9182892c4a857fc1cd74d8ecb3072d5126232e299d3850027cecbcb599e721cef835b9e63aba35c2db41733635d39b76c1d8
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/19a2978ee3462cc3b98e7d36e6537bf9fb1fb61f42fd96cb41e9313f2ac6f2c62380d94064366431eff537f342184720fe9bce73eb65fd57c5311d15e8648f62
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10/f4d4a803834ffa72713579d696586d8cc654c0025cbd5ec775fc5d37faa00381dcb80e5b97d4b16059443352653585596d87848b5590b1d8670c235408e73fb3
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-bigint": "npm:^7.8.3"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-import-attributes": "npm:^7.24.7"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/46331111ae72b7121172fd9e6a4a7830f651ad44bf26dbbf77b3c8a60a18009411a3eacb5e72274004290c110371230272109957d5224d155436b4794ead2f1b
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/aa4ff2a8a728d9d698ed521e3461a109a1e66202b13d3494e41eea30729a5e7cc03b3a2d56c594423a135429c37bf63a9fa8b0b9ce275298be3095a88c69f6fb
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: 10/aab4e8ccdc8d762bf3fdfce8e706601695620c0c2eda256dd85088dc0be3cfd7ff126f6e99c2bee1f24f5d418414aacf09d7f9702f16d6963df2fa488cda8824
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 10/61f9934c7378a51dce61b915586191078ef7f1c3eca707fdd58b96ff2ff56d9e0af2bdab66b1462301a73c73374239e6542d9821c0af787f3209a23365d07e7f
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10/bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3, body-parser@npm:^1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10/8723e3d7a672eb50854327453bed85ac48d045f4958e81e7d470c56bf111f835b97e5b73ae9f6393d0011cc9e252771f46fd281bbabc57d33d3986edf1e6aeca
  languageName: node
  linkType: hard

"bonjour-service@npm:^1.2.1":
  version: 1.3.0
  resolution: "bonjour-service@npm:1.3.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    multicast-dns: "npm:^7.2.5"
  checksum: 10/63d516d88f15fa4b89e247e6ff7d81c21a3ef5ed035b0b043c2b38e0c839f54f4ce58fbf9b7668027bf538ac86de366939dbb55cca63930f74eeea1e278c9585
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10/3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.25.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10/4a5442b1a0d09c4c64454f184b8fed17d8c3e202034bf39de28f74497d7bd28dddee121b2bab4e34825fe0ed4c166d84e32a39f576c76fce73c1f8f05e4b6ee6
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10/edba1b65bae682450be4117b695997972bd9a3c4dfee029cab5bcb72ae5393a79a8f909b8bc77957eb0deec1c7168670f18f4d5c556f46cdd3bca5f3b3a8d020
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10/0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"bundle-name@npm:^4.1.0":
  version: 4.1.0
  resolution: "bundle-name@npm:4.1.0"
  dependencies:
    run-applescript: "npm:^7.0.0"
  checksum: 10/1d966c8d2dbf4d9d394e53b724ac756c2414c45c01340b37743621f59cc565a435024b394ddcb62b9b335d1c9a31f4640eb648c3fec7f97ee74dc0694c9beb6c
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10/a10abf2ba70c784471d6b4f58778c0beeb2b5d405148e66affa91f23a9f13d07603d0a0354667310ae1d6dc141474ffd44e2a074be0f6e2254edb8fc21445388
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 10/618a8b3eea314060e74cb3285a6154e8343c244a34235acf91cfe626ee0705c24e3cd11e4b1a7b3900bd749ee203ae65afe13adf610c8ab173e99d4a208faf75
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.4
  resolution: "cacheable-request@npm:7.0.4"
  dependencies:
    clone-response: "npm:^1.0.2"
    get-stream: "npm:^5.1.0"
    http-cache-semantics: "npm:^4.0.0"
    keyv: "npm:^4.0.0"
    lowercase-keys: "npm:^2.0.0"
    normalize-url: "npm:^6.0.1"
    responselike: "npm:^2.0.0"
  checksum: 10/0f4f2001260ecca78b9f64fc8245e6b5a5dcde24ea53006daab71f5e0e1338095aa1512ec099c4f9895a9e5acfac9da423cb7c079e131485891e9214aca46c41
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: "npm:^3.1.2"
    tslib: "npm:^2.0.3"
  checksum: 10/bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0, camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10/e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10/8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001723
  resolution: "caniuse-lite@npm:1.0.30001723"
  checksum: 10/edab89e84a2b257cf640f0bac1f25f92c699ade86143b2affc73403468f894023416a9f4a99e5345c933956990b005a2facfb87ac4517c8ccb588819bb62453b
  languageName: node
  linkType: hard

"chalk@npm:2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10/3d1d103433166f6bfe82ac75724951b33769675252d8417317363ef9d54699b7c3b2d46671b772b893a8e50c3ece70c4b933c73c01e81bc60ea4df9b55afa303
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/37f90b31fd655fb49c2bd8e2a68aebefddd64522655d001ef417e6f955def0ed9110a867ffc878a533f2dafea5f2032433a37c8a7614969baa7f8a1cd424ddfc
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 10/1ec5c2906adb9f84e7f6732a40baef05d7c85401b82ffcbc44b85fbd0f7a2b0c2a96f2eb9cf55cae3235dc12d4023003b88f09bcae8be9ae894f52ed746f4d48
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: 10/c8dd1f4bf1a92fccf7d2fad9673660a88b37854557d30f6076c32fedfb92d1420208298829ff1d3b6b4fa1c7012e8326c45e7f5c3ed1e9a09ec177593c521b2f
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/c327fb07704443f8d15f7b4a7ce93b2f0bc0e6cea07ec28a7570aa22cd51fcf0379df589403976ea956c369f25aa82d84561947e227cd925902e1751371658df
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10/c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 10/1762bed739774903bf5915fe3045c3120fc3c7f7d929d88e566447ea38944937a6370ccb687278318c43c24f837ad22dac780bed67c066336815557b8cf558c6
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10/75bc67902b4d1c7b435497adeb91598f6d52a3389398e44294f6601b20cfef32cf2176f7be0eb961d9e085bb333a8a5cae121cb22f81cf238ae7f58eb80e9397
  languageName: node
  linkType: hard

"ci-info@npm:^4.0.0":
  version: 4.2.0
  resolution: "ci-info@npm:4.2.0"
  checksum: 10/928d8457f3476ffc4a66dec93b9cdf1944d5e60dba69fbd6a0fc95b652386f6ef64857f6e32372533210ef6d8954634af2c7693d7c07778ee015f3629a5e0dd9
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 10/d2b92f919a2dedbfd61d016964fce8da0035f827182ed6839c97cac56e8a8077cfa6a59388adfe2bc588a19cef9bbe830d683a76a6e93c51f65852062cfe2591
  languageName: node
  linkType: hard

"classcat@npm:^5.0.3, classcat@npm:^5.0.4":
  version: 5.0.5
  resolution: "classcat@npm:5.0.5"
  checksum: 10/19bdeb99b8923b47f9df978b6ef2c5a4cc3bcaa8fb6be16244e31fad619b291b366429747331903ac2ea27560ffd6066d14089a99c95535ce0f1e897525fa63d
  languageName: node
  linkType: hard

"clean-css@npm:^5.2.2":
  version: 5.3.3
  resolution: "clean-css@npm:5.3.3"
  dependencies:
    source-map: "npm:~0.6.0"
  checksum: 10/2db1ae37b384c8ff0a06a12bfa80f56cc02b4abcaaf340db98c0ae88a61dd67c856653fd8135ace6eb0ec13aeab3089c425d2e4238d2a2ad6b6917e6ccc74729
  languageName: node
  linkType: hard

"clipanion@npm:^4.0.0-rc.2":
  version: 4.0.0-rc.4
  resolution: "clipanion@npm:4.0.0-rc.4"
  dependencies:
    typanion: "npm:^3.8.0"
  peerDependencies:
    typanion: "*"
  checksum: 10/c3a94783318d91e6b35380a8aa4a6f166964082a51ff2df21a339266223aaab98f5986dd2c37ca7fd640ad1d233b3cd5b24aad64c51537b54ccc9c66ec070eeb
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10/44afbcc29df0899e87595590792a871cd8c4bc7d6ce92832d9ae268d141a77022adafca1aeaeccff618b62a613b8354e57fe22a275c199ec04baf00d381ef6ab
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/eaa5561aeb3135c2cddf7a3b3f562fc4238ff3b3fc666869ef2adf264be0f372136702f16add9299087fb1907c2e4ec5dbfe83bd24bce815c70a80c6c1a2e950
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10/770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: "npm:^1.0.0"
  checksum: 10/4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 10/a5d9f37091c70398a269e625cedff5622f200ed0aa0cff22ee7b55ed74a123834b58711776eb0f1dc58eb6ebbc1185aa7567b57bd5979a948c6e4f85073e2c05
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: 10/30ea7d5c9ee51f2fdba4901d4186c5b7114a088ef98fd53eda3979da77eed96758a2cae81cc6d97e239aaea6065868cf908b24980663f7b7e96aa291b3e12fa4
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10/ffa319025045f2973919d155f25e7c00d08836b6b33ea2d205418c59bd63a665d713c52d9737a9e0fe467fb194b40fbef1d849bae80d674568ee220a31ef3d10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10/09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colorette@npm:^2.0.10, colorette@npm:^2.0.14":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10/0b8de48bfa5d10afc160b8eaa2b9938f34a892530b2f7d7897e0458d9535a066e3998b49da9d21161c78225b272df19ae3a64d6df28b4c9734c0e55bbd02406f
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10/e3bf9e0332a5c45f49b90e79bcdb4a7a85f28d6a6f0876a94f1bb9b2bfbdbbb9292aac50e1e742d8c0db1e62a0229a106f57917e2d067fca951d81737651700d
  languageName: node
  linkType: hard

"commander@npm:^12.1.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10/cdaeb672d979816853a4eed7f1310a9319e8b976172485c2a6b437ed0db0a389a44cfb222bfbde772781efa9f215bdd1b936f80d6b249485b465c6cb906e1f93
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10/90c5b6898610cd075984c58c4f88418a4fb44af08c1b1415e9854c03171bec31b336b7f3e4cefe33de994b3f12b03c5e2d638da4316df83593b9e82554e7e95b
  languageName: node
  linkType: hard

"commander@npm:^6.2.0":
  version: 6.2.1
  resolution: "commander@npm:6.2.1"
  checksum: 10/25b88c2efd0380c84f7844b39cf18510da7bfc5013692d68cdc65f764a1c34e6c8a36ea6d72b6620e3710a930cf8fab2695bdec2bf7107a0f4fa30a3ef3b7d0e
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 10/6b7b5d334483ce24bd73c5dac2eab901a7dbb25fd983ea24a1eeac6e7166bb1967f641546e8abf1920afbde86a45fbfe5812fbc69d0dc451bb45ca416a12a3a3
  languageName: node
  linkType: hard

"comment-json@npm:^2.2.0":
  version: 2.4.2
  resolution: "comment-json@npm:2.4.2"
  dependencies:
    core-util-is: "npm:^1.0.2"
    esprima: "npm:^4.0.1"
    has-own-prop: "npm:^2.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10/6b0e0477c8fc4821b3fc2d79dcab6f1d9ebc5ee72c221307b47003ae769867561e02398fe8292495d9a4e327f9e8a5270bed1eeb60132e18657bb80672e1e7e5
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10/58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10/ca213b9bd03e56c7c3596399d846237b5f0b31ca4cdeaa76a9547cd3c1465fbcfcb0fe93a5d7ff64eff28383fc65b53f1ef8bb2720d11bb48ad8c0836c502506
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^2.0.0":
  version: 2.0.0
  resolution: "connect-history-api-fallback@npm:2.0.0"
  checksum: 10/3b26bf4041fdb33deacdcb3af9ae11e9a0b413fb14c95844d74a460b55e407625b364955dcf965c654605cde9d24ad5dad423c489aa430825aab2035859aba0c
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10/b7f4ce176e324f19324be69b05bf6f6e411160ac94bc523b782248129eb1ef3be006f6cff431aaea5e337fe5d176ce8830b8c2a1b721626ead8933f0cbe78720
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10/585847d98dc7fb8035c02ae2cb76c7a9bd7b25f84c447e5ed55c45c2175e83617c8813871b4ee22f368126af6b2b167df655829007b21aa10302873ea9c62662
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10/f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10/aec6a6aa0781761bf55d60447d6be08861d381136a0fe94aa084fddd4f0300faa2b064df490c6798adfa1ebaef9e0af9b08a189c823e0811b8b313b3d9a03380
  languageName: node
  linkType: hard

"cookie@npm:^1.0.1":
  version: 1.0.2
  resolution: "cookie@npm:1.0.2"
  checksum: 10/f5817cdc84d8977761b12549eba29435e675e65c7fef172bc31737788cd8adc83796bf8abe6d950554e7987325ad2d9ac2971c5bd8ff0c4f81c145f82e4ab1be
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:~12.0":
  version: 12.0.2
  resolution: "copy-webpack-plugin@npm:12.0.2"
  dependencies:
    fast-glob: "npm:^3.3.2"
    glob-parent: "npm:^6.0.1"
    globby: "npm:^14.0.0"
    normalize-path: "npm:^3.0.0"
    schema-utils: "npm:^4.2.0"
    serialize-javascript: "npm:^6.0.2"
  peerDependencies:
    webpack: ^5.1.0
  checksum: 10/674725d4d9556b7b9a32bb85393532ef2bb75ffce785d942681b3575a86d900751f67cebbb089ddd050757f58c84edc18732e17880f12c45c9775ca94328526c
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: "npm:^4.25.0"
  checksum: 10/fa57a75e0e0798889f0a8d4dbc66bd276c799f265442eb0f6baa4113efaf0c4213e457c70f8f0f9d78f98b22c5c16dfd7e68d88e6f2484ae2120888a4bd08b68
  languageName: node
  linkType: hard

"core-util-is@npm:^1.0.2, core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10/9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    prompts: "npm:^2.0.1"
  bin:
    create-jest: bin/create-jest.js
  checksum: 10/847b4764451672b4174be4d5c6d7d63442ec3aa5f3de52af924e4d996d87d7801c18e125504f25232fc75840f6625b3ac85860fac6ce799b5efae7bdcaf4a2b7
  languageName: node
  linkType: hard

"cross-spawn@npm:7.0.5":
  version: 7.0.5
  resolution: "cross-spawn@npm:7.0.5"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/c95062469d4bdbc1f099454d01c0e77177a3733012d41bf907a71eb8d22d2add43b5adf6a0a14ef4e7feaf804082714d6c262ef4557a1c480b86786c120d18e2
  languageName: node
  linkType: hard

"css-loader@npm:~7.1":
  version: 7.1.2
  resolution: "css-loader@npm:7.1.2"
  dependencies:
    icss-utils: "npm:^5.1.0"
    postcss: "npm:^8.4.33"
    postcss-modules-extract-imports: "npm:^3.1.0"
    postcss-modules-local-by-default: "npm:^4.0.5"
    postcss-modules-scope: "npm:^3.2.0"
    postcss-modules-values: "npm:^4.0.0"
    postcss-value-parser: "npm:^4.2.0"
    semver: "npm:^7.5.4"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    webpack: ^5.27.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: 10/ddde22fb103888320f60a1414a6a04638d2e9760a532a52d03c45e6e2830b32dd76c734aeef426f78dd95b2d15f77eeec3854ac53061aff02569732dc6e6801c
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.0.1"
    domhandler: "npm:^4.3.1"
    domutils: "npm:^2.8.0"
    nth-check: "npm:^2.0.1"
  checksum: 10/8f7310c9af30ccaba8f72cb4a54d32232c53bf9ba05d019b693e16bfd7ba5df0affc1f4d74b1ee55923643d23b80a837eedcf60938c53356e479b04049ff9994
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10/c67a3a2d0d81843af87f8bf0a4d0845b0f952377714abbb2884e48942409d57a2110eabee003609d02ee487b054614bdfcfc59ee265728ff105bd5aa221c1d0e
  languageName: node
  linkType: hard

"css.escape@npm:^1.5.1":
  version: 1.5.1
  resolution: "css.escape@npm:1.5.1"
  checksum: 10/f6d38088d870a961794a2580b2b2af1027731bb43261cfdce14f19238a88664b351cc8978abc20f06cc6bbde725699dec8deb6fe9816b139fc3f2af28719e774
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10/0e161912c1306861d8f46e1883be1cbc8b1b2879f0f509287c0db71796e4ddfb97ac96bdfca38f77f452e2c10554e1bb5678c99b07a5cf947a12778f73e47e12
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:^3.1.6":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: "npm:1 - 2"
  checksum: 10/5800c467f89634776a5977f6dae3f4e127d91be80f1d07e3e6e35303f9de93e6636d014b234838eea620f7469688d191b3f41207a30040aab750a63c97ec1d7c
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 10/536ba05bfd9f4fcd6fa289b5974f5c846b21d186875684637e22bf6855e6aba93e24a2eb3712985c6af3f502fbbfa03708edb72f58142f626241a8a17258e545
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 3":
  version: 3.0.1
  resolution: "d3-dispatch@npm:3.0.1"
  checksum: 10/2b82f41bf4ef88c2f9033dfe32815b67e2ef1c5754a74137a74c7d44d6f0d6ecfa934ac56ed8afe358f6c1f06462e8aa42ca0a388397b5b77a42721570e80487
  languageName: node
  linkType: hard

"d3-drag@npm:2 - 3, d3-drag@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-drag@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-selection: "npm:3"
  checksum: 10/80bc689935e5a46ee92b2d7f71e1c792279382affed9fbcf46034bff3ff7d3f50cf61a874da4bdf331037292b9e7dca5c6401a605d4bb699fdcb4e0c87e176ec
  languageName: node
  linkType: hard

"d3-ease@npm:1 - 3, d3-ease@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 10/985d46e868494e9e6806fedd20bad712a50dcf98f357bf604a843a9f6bc17714a657c83dd762f183173dcde983a3570fa679b2bc40017d40b24163cdc4167796
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: 10/a0fe23d2575f738027a3db0ce57160e5a473ccf24808c1ed46d45ef4f3211076b34a18b585547d34e365e78dcc26dd4ab15c069731fc4b1c07a26bfced09ea31
  languageName: node
  linkType: hard

"d3-interpolate@npm:1 - 3, d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
  checksum: 10/988d66497ef5c190cf64f8c80cd66e1e9a58c4d1f8932d776a8e3ae59330291795d5a342f5a97602782ccbef21a5df73bc7faf1f0dc46a5145ba6243a82a0f0e
  languageName: node
  linkType: hard

"d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 10/8e97a9ab4930a05b18adda64cf4929219bac913a5506cf8585631020253b39309549632a5cbeac778c0077994442ddaaee8316ee3f380e7baf7566321b84e76a
  languageName: node
  linkType: hard

"d3-scale@npm:^4.0.2":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: "npm:2.10.0 - 3"
    d3-format: "npm:1 - 3"
    d3-interpolate: "npm:1.2.0 - 3"
    d3-time: "npm:2.1.1 - 3"
    d3-time-format: "npm:2 - 4"
  checksum: 10/e2dc4243586eae2a0fdf91de1df1a90d51dfacb295933f0ca7e9184c31203b01436bef69906ad40f1100173a5e6197ae753cb7b8a1a8fcfda43194ea9cad6493
  languageName: node
  linkType: hard

"d3-selection@npm:2 - 3, d3-selection@npm:3, d3-selection@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-selection@npm:3.0.0"
  checksum: 10/0e5acfd305b31628b7be5009ba7303d84bb34817a88ed4dde9c8bd9c23528573fc5272f89fc04e5be03d2cbf5441a248d7274aaf55a8ef3dad46e16333d72298
  languageName: node
  linkType: hard

"d3-shape@npm:^3.1.0":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: "npm:^3.1.0"
  checksum: 10/2e861f4d4781ee8abd85d2b435f848d667479dcf01a4e0db3a06600a5bdeddedb240f88229ec7b3bf7fa300c2b3526faeaf7e75f9a24dbf4396d3cc5358ff39d
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: "npm:1 - 3"
  checksum: 10/ffc0959258fbb90e3890bfb31b43b764f51502b575e87d0af2c85b85ac379120d246914d07fca9f533d1bcedc27b2841d308a00fd64848c3e2cad9eff5c9a0aa
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:^3.0.0":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: "npm:2 - 3"
  checksum: 10/c110bed295ce63e8180e45b82a9b0ba114d5f33ff315871878f209c1a6d821caa505739a2b07f38d1396637155b8e7372632dacc018e11fbe8ceef58f6af806d
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 3, d3-timer@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 10/004128602bb187948d72c7dc153f0f063f38ac7a584171de0b45e3a841ad2e17f1e40ad396a4af9cce5551b6ab4a838d5246d23492553843d9da4a4050a911e2
  languageName: node
  linkType: hard

"d3-transition@npm:2 - 3":
  version: 3.0.1
  resolution: "d3-transition@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-dispatch: "npm:1 - 3"
    d3-ease: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  peerDependencies:
    d3-selection: 2 - 3
  checksum: 10/02571636acb82f5532117928a87fe25de68f088c38ab4a8b16e495f0f2d08a3fd2937eaebdefdfcf7f1461545524927d2632d795839b88d2e4c71e387aaaffac
  languageName: node
  linkType: hard

"d3-zoom@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-zoom@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:2 - 3"
    d3-transition: "npm:2 - 3"
  checksum: 10/0e6e5c14e33c4ecdff311a900dd037dea407734f2dd2818988ed6eae342c1799e8605824523678bd404f81e37824cc588f62dbde46912444c89acc7888036c6b
  languageName: node
  linkType: hard

"dagre@npm:^0.8.5":
  version: 0.8.5
  resolution: "dagre@npm:0.8.5"
  dependencies:
    graphlib: "npm:^2.1.8"
    lodash: "npm:^4.17.15"
  checksum: 10/f39899e29e9090581d67177ef6e2dd3ca5d7f764fbf3de81758d879bba66fee6fd8802d41d0c5d3d9a0563b334e99e1454a8d6ab4ce17e8e4f50836a3a403fdd
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10/e07005f2b40e04f1bd14a3dd20520e9c4f25f60224cb006ce9d6781732c917964e9ec029fc7f1a151083cd929025ad5133814d4dc624a9aaf020effe4914ed14
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10/ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.2.0
  resolution: "decode-named-character-reference@npm:1.2.0"
  dependencies:
    character-entities: "npm:^2.0.0"
  checksum: 10/f26b23046c1a137c0b41fa51e3ce07ba8364640322c742a31570999784abc8572fc24cb108a76b14ff72ddb75d35aad3d14b10d7743639112145a2664b9d1864
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10/d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.6.0
  resolution: "dedent@npm:1.6.0"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 10/f100cb11001309f2185c4334c6f29e5323c1e73b7b75e3b1893bc71ef53cd13fb80534efc8fa7163a891ede633e310a9c600ba38c363cc9d14a72f238fe47078
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10/058d9e1b0ff1a154468bf3837aea436abcfea1ba1d165ddaaf48ca93765fdd01a30d33c36173da8fbbed951dd0a267602bc782fe288b0fc4b7e1e7091afc4529
  languageName: node
  linkType: hard

"default-browser-id@npm:^5.0.0":
  version: 5.0.0
  resolution: "default-browser-id@npm:5.0.0"
  checksum: 10/185bfaecec2c75fa423544af722a3469b20704c8d1942794a86e4364fe7d9e8e9f63241a5b769d61c8151993bc65833a5b959026fa1ccea343b3db0a33aa6deb
  languageName: node
  linkType: hard

"default-browser@npm:^5.2.1":
  version: 5.2.1
  resolution: "default-browser@npm:5.2.1"
  dependencies:
    bundle-name: "npm:^4.1.0"
    default-browser-id: "npm:^5.0.0"
  checksum: 10/afab7eff7b7f5f7a94d9114d1ec67273d3fbc539edf8c0f80019879d53aa71e867303c6f6d7cffeb10a6f3cfb59d4f963dba3f9c96830b4540cc7339a1bf9840
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 10/8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 10/f28421cf9ee86eecaf5f3b8fe875f13d7009c2625e97645bfff7a2a49aca678270b86c39f9c32939e5ca7ab96b551377ed4139558c795e076774287ad3af1aa4
  languageName: node
  linkType: hard

"delaunator@npm:^4.0.0":
  version: 4.0.1
  resolution: "delaunator@npm:4.0.1"
  checksum: 10/b4c1f65a4f0c4e58428725a34edee9089cd72cc3ee998c8b2fa1bc9e8f654ca1afe2fad42607085b15a7dd4dbe71ba2ce75a8c3c867a49fab6e4769b0854189d
  languageName: node
  linkType: hard

"delaunay-find@npm:0.0.6":
  version: 0.0.6
  resolution: "delaunay-find@npm:0.0.6"
  dependencies:
    delaunator: "npm:^4.0.0"
  checksum: 10/8f2b994986076700b7f18898494562b9ba6825091b5172d69d141434945c4db26c1494f59f5a6036782ce79e98416b94250d28635fdb83cac96d9262251aea52
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10/c0c8ff36079ce5ada64f46cc9d6fd47ebcf38241105b6e0c98f412e8ad91f084bcf906ff644cc3a4bd876ca27a62accb8b0fff72ea6ed1a414b89d8506f4a5ca
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 10/2ed6966fc14463a9e85451db330ab8ba041efed0b9a1a472dbfc6fbf2f82bab66491915f996b25d8517dddc36c8c74e24c30879b34877f3c4410733444a51d1d
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0, dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10/6ff05a7561f33603df87c45e389c9ac0a95e3c056be3da1a0c4702149e3a7f6fe5ffbb294478687ba51a9e95f3a60e8b6b9005993acd79c292c7d15f71964b6b
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10/0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: 10/ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 10/832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: 10/179daf9d2f9af5c57ad66d97cb902a538bcf8ed64963fa7aa0c329b3de3665ce2eb6ffdc2f69f29d445fa4af2517e5e55e5b6e00c00a9ae4f43645f97f7078cb
  languageName: node
  linkType: hard

"diff@npm:^5.0.0, diff@npm:^5.1.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 10/01b7b440f83a997350a988e9d2f558366c0f90f15be19f4aa7f1bb3109a4e153dfc3b9fbf78e14ea725717017407eeaa2271e3896374a0181e8f52445740846d
  languageName: node
  linkType: hard

"dns-packet@npm:^5.2.2":
  version: 5.6.1
  resolution: "dns-packet@npm:5.6.1"
  dependencies:
    "@leichtgewicht/ip-codec": "npm:^2.0.1"
  checksum: 10/ef5496dd5a906e22ed262cbe1a6f5d532c0893c4f1884a7aa37d4d0d8b8376a2b43f749aab087c8bb1354d67b40444f7fca8de4017b161a4cea468543061aed3
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.5.9":
  version: 0.5.16
  resolution: "dom-accessibility-api@npm:0.5.16"
  checksum: 10/377b4a7f9eae0a5d72e1068c369c99e0e4ca17fdfd5219f3abd32a73a590749a267475a59d7b03a891f9b673c27429133a818c44b2e47e32fec024b34274e2ca
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.6.3":
  version: 0.6.3
  resolution: "dom-accessibility-api@npm:0.6.3"
  checksum: 10/83d3371f8226487fbad36e160d44f1d9017fb26d46faba6a06fcad15f34633fc827b8c3e99d49f71d5f3253d866e2131826866fd0a3c86626f8eccfc361881ff
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: "npm:~0.4"
  checksum: 10/71b22f56bce6255a963694a72860a99f08763cf500f02ff38ce4c7489f95b07e7a0069f10b04c7d200e21375474abe01232833ca1600f104bdee7173e493a5b9
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.2.0"
    entities: "npm:^2.0.0"
  checksum: 10/53b217bcfed4a0f90dd47f34f239b1c81fff53ffa39d164d722325817fdb554903b145c2d12c8421ce0df7d31c1b180caf7eacd3c86391dd925f803df8027dcc
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10/ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: "npm:^2.2.0"
  checksum: 10/e0d2af7403997a3ca040a9ace4a233b75ebe321e0ef628b417e46d619d65d47781b2f2038b6c2ef6e56e73e66aec99caf6a12c7e687ecff18ef74af6dfbde5de
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: "npm:^1.0.1"
    domelementtype: "npm:^2.2.0"
    domhandler: "npm:^4.2.0"
  checksum: 10/1f316a03f00b09a8893d4a25d297d5cbffd02c564509dede28ef72d5ce38d93f6d61f1de88d439f31b14a1d9b42f587ed711b9e8b1b4d3bf6001399832bfc4e0
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv@npm:^16.3.1":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10/e68a16834f1a41cc2dfb01563bc150668ad675e6cd09191211467b5c0806b6ecd6ec438e021aa8e01cd0e72d2b70ef4302bec7cc0fe15b6955f85230b62dc8a9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10/1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.168
  resolution: "electron-to-chromium@npm:1.5.168"
  checksum: 10/a98d4905dbbed1a801be5c43f6f77a5d44ea9bc317a79cf61e6c963b4179b67232dbdb2b9e708ab9f4d8f5fbc17c303192270c91d0e092470dcfc6d0706d5eb8
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10/fbe214171d878b924eedf1757badf58a5dce071cd1fa7f620fa841a0901a80d6da47ff05929d53163105e621ce11a71b9d8acb1148ffe1745e045145f6e69521
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10/e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10/abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10/1e0cfa6e7f49887544e03314f9dfc56a8cb6dde910cbb445983ecc2ff426fc05946df9d75d8a21a3a64f2cecfe1bf88f773952029f46756b2ed64a24e95b1fb8
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10/50e81c7fe2239fba5670ebce78a34709906ed3a79274aa416434f7307b252e0b7824d76a7dd403eca795571dc6afd9a44183fc45a68475e8f2fdfbae6e92fcc3
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 10/2c765221ee324dbe25e1b8ca5d1bf2a4d39e750548f2e85cbf7ca1d167d709689ddf1796623e66666ae747364c11ed512c03b48c5bbe70968d30f2a4009509b7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"envinfo@npm:^7.14.0":
  version: 7.14.0
  resolution: "envinfo@npm:7.14.0"
  bin:
    envinfo: dist/cli.js
  checksum: 10/0d9d711f2b6ae02dec89dd768a3390acbcb99ac50d07f20e635a8d2db68447703476db535483592d1ed4656c3d36eee4883032d71a5118c917b4973e2d4fa027
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10/d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10/b6f3e576a3fed4d82b0d0ad4bbf6b3a5ad694d2e7ce8c4a069560da3db6399381eaba703616a182b16dde50ce998af64e07dcf49f2ae48153b9e07be3f107087
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10/6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10/6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10/9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10/c541ef384c92eb5c999b7d3443d80195fcafb3da335500946f6db76539b87d5826c8f2e1d23bf6afc3154ba8cd7c8e566f8dc00f1eea25fdf3afc8fb9c87b238
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/f1d3c622ad992421362294f7acf866aa9409fbad4eb2e8fa230bd33944ce371d32279667b242d8b8907ec2b6ad7353a717f3c0e60e748873a34a7905174bc0eb
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10/3f67ad02b6dbfaddd9ea459cf2b6ef4ecff9a6082a7af9d22e445b9abc082ad9ca47e1825557b293fcdae477f4714e561123e30bb6a5b2f184fb2bad4a9497eb
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10/571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10/8030029382404942c01d0037079f1b1bc8fed524b5849c237b80549b01e2fc49709e1d0c557fa65ca4498fc9e24cff1475ef7b855121fcc15f9d61f93e282346
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10/ac6423ec31124629c84c7077eed1e6987f6d66c31cf43c6fcbf6c87791d56317ce808d9ead483652436df171b526fc7220eccdc9f3225df334e81582c3cf7dd5
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10/a3d47e285e28d324d7180f1e493961a2bbb4cad6412090e4dec114f4db1f5b560c7696ee8e758f55e23913ede856e3689cd3aa9ae13c56b5d8314cd3b3ddd1be
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10/8ada91f2d70f7dff702c861c2c64f21dfdc1525628f3c0454fd6f02fce65f7b958616cbd2b99ca7fa4d474e461a3d363824e91b3eb881705231abbf387470597
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: 10/387555050c5b3c10e7a9e8df5f43194e95d7737c74532c409910e585d5554eaff34960c166643f5e23d042196529daad059c292dcf1fb61b8ca878d3677f4b87
  languageName: node
  linkType: hard

"expect@npm:^29.0.0, expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10/63f97bc51f56a491950fb525f9ad94f1916e8a014947f8d8445d3847a665b5471b768522d659f5e865db20b6c2033d2ac10f35fcbd881a4d26407a4f6f18451a
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"express@npm:^4.21.1, express@npm:^4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10/34571c442fc8c9f2c4b442d2faa10ea1175cf8559237fc6a278f5ce6254a8ffdbeb9a15d99f77c1a9f2926ab183e3b7ba560e3261f1ad4149799e3412ab66bd1
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10/59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.2, fast-glob@npm:^3.3.2, fast-glob@npm:^3.3.3":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10/43c87cd03926b072a241590e49eca0e2dfe1d347ddffd4b15307613b42b8eacce00a315cf3c7374736b5f343f27e27ec88726260eb03a758336d507d6fbaba0a
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.12":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: 10/ee85d33b5cef592033f70e1c13ae8624055950b4eb832435099cd56aa313d7f251b873bedbc06a517adfaff7b31756d139535991e2406967438e03a1bf1b008e
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/75679dc226316341c4f2a6b618571f51eac96779906faecd8921b984e844d6ae42fabb2df69b1071327d398d5716693ea9c9c8941f64ac9e89ec2032ce59d730
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: "npm:>=0.5.1"
  checksum: 10/22433c14c60925e424332d2794463a8da1c04848539b5f8db5fced62a7a7c71a25335a4a8b37334e3a32318835e2b87b1733d008561964121c4a0bd55f0878c3
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10/4f95d336fb805786759e383fd7fff342ceb7680f53efcc0ef82f502eb479ce35b98e8b207b6dfdfeea0eba845862107dc73813775fc6b56b3098c6e90a2dad77
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/c186ba387e7b75ccf874a098d9bc5fe0af0e9c52fc56f8eac8e80aa4edb65532684bf2bf769894ff90f53bf221d6136692052d31f07a9952807acae6cbe7ee50
  languageName: node
  linkType: hard

"file-selector@npm:^0.6.0":
  version: 0.6.0
  resolution: "file-selector@npm:0.6.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/6add4098ae07fd1e9050b1e8d3fd9f128680c1d6648c0676af54ace4586e6e5bfcb8fdfa45b69e9131ffd8175bf630d54a445a5facf9be244f85b99ce309183e
  languageName: node
  linkType: hard

"file-selector@npm:^2.1.0":
  version: 2.1.2
  resolution: "file-selector@npm:2.1.2"
  dependencies:
    tslib: "npm:^2.7.0"
  checksum: 10/2a6be0e1904df85f8705a5171fd3b93c1b1ff2ad0143556adb78ac4de899bfc0ba1a20083b4febd4f7000759ec9119a31af76a057e29dd9215907da69ac95e50
  languageName: node
  linkType: hard

"file-selector@npm:~1.2":
  version: 1.2.0
  resolution: "file-selector@npm:1.2.0"
  dependencies:
    tslib: "npm:^2.7.0"
  checksum: 10/4d5c8a655d0365113ad5b2a22cf9538e1012f5f480e657086607010eb32ca5afd6b84cc62afa8cdd401135bdae9b263940cbec40105191ddf8a811665f0868ee
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10/4babe72969b7373b5842bc9f75c3a641a4d0f8eb53af6b89fa714d4460ce03fb92b28de751d12ba415e96e7e02870c436d67412120555e2b382640535697305b
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"focus-trap@npm:7.5.2":
  version: 7.5.2
  resolution: "focus-trap@npm:7.5.2"
  dependencies:
    tabbable: "npm:^6.2.0"
  checksum: 10/9f51e15e1bcd9c92dd8172fd967977832c8d80738c5cf195d98ee8226498665152cd449a62ee62b0f90bbaa4509b648fb64389714ba52f2502d53e1272dfc3e9
  languageName: node
  linkType: hard

"focus-trap@npm:7.6.2":
  version: 7.6.2
  resolution: "focus-trap@npm:7.6.2"
  dependencies:
    tabbable: "npm:^6.2.0"
  checksum: 10/7024d3c994bb1018857fdf0ae24e370b134f20e788eb5aa3315ad4a7de2407b69d56bcf0f05a5a6d86b9360a8609923b9ea80397c2710aca75b1fc88292d75d0
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0, follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10/e3ab42d1097e90d28b913903841e6779eb969b62a64706a3eb983e894a5db000fbd89296f45f08885a0e54cd558ef62e81be1165da9be25a6c44920da10f424c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10/22f6e55e6f32a5797a500ed7ca5aa9d690c4de6e1b3308f25f0d83a27d08d91a265ab59a190db2305b15144f8f07df08e8117bad6a93fc93de1baa838bfcc0b5
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10/29ba9fd347117144e97cbb8852baae5e8b2acb7d1b591ef85695ed96f5b933b1804a7fac4a15dd09ca7ac7d0cdc104410e8102aae2dd3faa570a797ba07adb81
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10/64c88e489b5d08e2f29664eb3c79c705ff9a8eb15d3e597198ef76546d4ade295897a44abb0abd2700e7ef784b2e3cbf1161e4fbf16f59129193fd1030d16da1
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/03191781e94bc9a54bd376d3146f90fe8e082627c502185dbf7b9b3032f66b0b142c1115f3b2cc5936575fc1b44845ce903dd4c21bec2a8d69f3bd56f9cee9ec
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs-readdir-recursive@npm:^1.1.0":
  version: 1.1.0
  resolution: "fs-readdir-recursive@npm:1.1.0"
  checksum: 10/d5e3fd8456b8e5d57a43f169a9eaf65c70fa82c4a22f1d4361cdba4ea5e61c60c5c2b4ac481ea137a4d43b2b99b3ea2fae95ac2730255c4206d61af645866c3a
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10/e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10/17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10/bba0811116d11e56d702682ddef7c73ba3481f114590e705fc549f4d868972263896af313c57a25c076e3c0d567e11d919a64ba1b30c879be985fc9d44f96148
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 10/13a73148dca795e41421013da6e3ebff8ccb7fba4d2f023fd0c6da2c166ec4e789bec9774a73a7b49c08daf2cae552f8a3e914042ac23b5f59dd278cc8f9cbfb
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10/781266d29725f35c59f1d214aedc92b0ae855800a980800e2923b3fbc4e56b3cb6e462c42e09a1cf1a00c64e056a78fa407cbe06c7c92b7e5cd49b4b85c2a497
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.1":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 10/9009529195a955c40d7b9690794aeff5ba665cc38f1519e111c58bb54366fd0c106bde80acf97ba4e533208eb53422c83b136611a54c5fefb1edd8dc267cb62e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.2.0":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10/9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globby@npm:^14.0.0":
  version: 14.1.0
  resolution: "globby@npm:14.1.0"
  dependencies:
    "@sindresorhus/merge-streams": "npm:^2.1.0"
    fast-glob: "npm:^3.3.3"
    ignore: "npm:^7.0.3"
    path-type: "npm:^6.0.0"
    slash: "npm:^5.1.0"
    unicorn-magic: "npm:^0.3.0"
  checksum: 10/e527ff54f0dddf60abfabd0d9e799768619d957feecd8b13ef60481f270bfdce0d28f6b09267c60f8064798fb3003b8ec991375f7fe0233fbce5304e1741368c
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"got@npm:^11.7.0":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": "npm:^4.0.0"
    "@szmarczak/http-timer": "npm:^4.0.5"
    "@types/cacheable-request": "npm:^6.0.1"
    "@types/responselike": "npm:^1.0.0"
    cacheable-lookup: "npm:^5.0.3"
    cacheable-request: "npm:^7.0.2"
    decompress-response: "npm:^6.0.0"
    http2-wrapper: "npm:^1.0.0-beta.5.2"
    lowercase-keys: "npm:^2.0.0"
    p-cancelable: "npm:^2.0.0"
    responselike: "npm:^2.0.0"
  checksum: 10/a30c74029d81bd5fe50dea1a0c970595d792c568e188ff8be254b5bc11e6158d1b014570772d4a30d0a97723e7dd34e7c8cc1a2f23018f60aece3070a7a5c2a5
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"grapheme-splitter@npm:^1.0.4":
  version: 1.0.4
  resolution: "grapheme-splitter@npm:1.0.4"
  checksum: 10/fdb2f51fd430ce881e18e44c4934ad30e59736e46213f7ad35ea5970a9ebdf7d0fe56150d15cc98230d55d2fd48c73dc6781494c38d8cf2405718366c36adb88
  languageName: node
  linkType: hard

"graphlib@npm:^2.1.8":
  version: 2.1.8
  resolution: "graphlib@npm:2.1.8"
  dependencies:
    lodash: "npm:^4.17.15"
  checksum: 10/37cbd851d3c1fb99f3174750ccaa22305d23d11746e5df81a38ba3bf25c0ba29cd9658ba69a0159ea81d56c28e8e875033eeaaa7167d838419fae08d9cd2c62c
  languageName: node
  linkType: hard

"graphtalk-plugin@workspace:.":
  version: 0.0.0-use.local
  resolution: "graphtalk-plugin@workspace:."
  dependencies:
    "@babel/cli": "npm:^7.26.10"
    "@babel/core": "npm:^7.26.10"
    "@babel/helpers": "npm:^7.27.0"
    "@babel/preset-env": "npm:^7.26.9"
    "@babel/preset-typescript": "npm:^7.26.10"
    "@babel/runtime": "npm:^7.27.0"
    "@cyclonedx/yarn-plugin-cyclonedx": "npm:^3.0.2"
    "@hawtio/backend-middleware": "npm:^1.0.5"
    "@hawtio/react": "npm:~1.6"
    "@jolokia.js/simple": "npm:^2.1.7"
    "@patternfly/patternfly": "npm:~5.4"
    "@patternfly/react-charts": "npm:~7.4"
    "@patternfly/react-core": "npm:~5.4"
    "@patternfly/react-icons": "npm:~5.4"
    "@patternfly/react-table": "npm:~5.4"
    "@swc/core": "npm:~1.7"
    "@types/react": "npm:~18.3"
    "@types/react-dom": "npm:~18.3"
    "@yarnpkg/pnpify": "npm:~4.1"
    "@yarnpkg/sdks": "npm:~3.2"
    axios: "npm:^1.8.4"
    body-parser: "npm:^1.20.3"
    copy-webpack-plugin: "npm:~12.0"
    cross-spawn: "npm:7.0.5"
    css-loader: "npm:~7.1"
    file-selector: "npm:~1.2"
    html-webpack-plugin: "npm:~5.6"
    http-proxy-middleware: "npm:2.0.9"
    jest: "npm:~29.7"
    jolokia.js: "npm:^2.1.7"
    mini-css-extract-plugin: "npm:~2.9"
    nanoid: "npm:^3.3.11"
    path-browserify: "npm:~1.0"
    path-to-regexp: "npm:0.1.12"
    qs: "npm:^6.13.0"
    react: "npm:~18.3"
    react-dom: "npm:~18.3"
    react-router-dom: "npm:^7.5.2"
    replace: "npm:~1.2"
    style-loader: "npm:~4.0"
    swc-loader: "npm:~0.2"
    tslib: "npm:~2.8"
    typescript: "npm:~5.5"
    webpack: "npm:~5.99"
    webpack-cli: "npm:~6.0"
    webpack-dev-server: "npm:~5.2"
  languageName: unknown
  linkType: soft

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 10/441ec98b07f26819c70c702f6c874088eebeb551b242fe8fae4eab325746b82bf84ae7a1f6419547698accb3941fa26806c5f5f93c50e19f90e499065a711d61
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10/4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-own-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-own-prop@npm:2.0.0"
  checksum: 10/ca6336e85ead2295c9603880cbc199e2d3ff7eaea0e9035d68fbc79892e9cf681abc62c0909520f112c671dad9961be2173b21dff951358cc98425c560e789e0
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "hast-util-whitespace@npm:2.0.1"
  checksum: 10/ad5a61f4e81330413d4182247e158d77408a076994fbe7257574ea6489728bb4138c83e00482051c941973d4ed3049729afb35600debfc6d1d945c40453685f7
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10/1acbe85f33e5a39f90c822ad4d28b24daeb60f71c545279431dc98c312cd28a54f8d64788e477fe21dc502b0e3cf58589ebe5c1ad22af27245370391c2d24ea6
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: "npm:^2.0.1"
    obuf: "npm:^1.0.0"
    readable-stream: "npm:^2.0.1"
    wbuf: "npm:^1.1.0"
  checksum: 10/6910e4b9d943a78fd8e84ac42729fdab9bd406789d6204ad160af9dc5aa4750fc01f208249bf7116c11dc0678207a387b4ade24e4b628b95385b251ceeeb719c
  languageName: node
  linkType: hard

"hpagent@npm:^1.2.0":
  version: 1.2.0
  resolution: "hpagent@npm:1.2.0"
  checksum: 10/bad186449da7e3456788a8cbae459fc6c0a855d5872a7f460c48ce4a613020d8d914839dad10047297099299c4f9e6c65a0eec3f5886af196c0a516e4ad8a845
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10/034d74029dcca544a34fb6135e98d427acd73019796ffc17383eaa3ec2fe1c0471dcbbc8f8ed39e46e86d43ccd753a160631615e4048285e313569609b66d5b7
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^6.0.2":
  version: 6.1.0
  resolution: "html-minifier-terser@npm:6.1.0"
  dependencies:
    camel-case: "npm:^4.1.2"
    clean-css: "npm:^5.2.2"
    commander: "npm:^8.3.0"
    he: "npm:^1.2.0"
    param-case: "npm:^3.0.4"
    relateurl: "npm:^0.2.7"
    terser: "npm:^5.10.0"
  bin:
    html-minifier-terser: cli.js
  checksum: 10/a244fa944e002b57c66cc829a3f2dfdb9514b1833c2d838ada624964bf8c0afaf61d36c371758c7e44dedae95cea740a84d8d1067b916ed204f35175184d0e27
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:~5.6":
  version: 5.6.3
  resolution: "html-webpack-plugin@npm:5.6.3"
  dependencies:
    "@types/html-minifier-terser": "npm:^6.0.0"
    html-minifier-terser: "npm:^6.0.2"
    lodash: "npm:^4.17.21"
    pretty-error: "npm:^4.0.0"
    tapable: "npm:^2.0.0"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    webpack: ^5.20.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: 10/fd2bf1ac04823526c8b609555d027b38b9d61b4ba9f5c8116a37cc6b62d5b86cab1f478616e8c5344fee13663d2566f5c470c66265ecb1e9574dc38d0459889d
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.0.0"
    domutils: "npm:^2.5.2"
    entities: "npm:^2.0.0"
  checksum: 10/c9c34b0b722f5923c4ae05e59268aeb768582152969e3338a1cd3342b87f8dd2c0420f4745e46d2fd87f1b677ea2f314c3a93436ed8831905997e6347e081a5d
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 10/9ae293b0acbfad6ed45d52c1f85f58ab062465872fd9079c80d78c6527634002d73c2a9d8c0296cc12d178a0b689bb5291d9979aad3ce71ab17a7517588adbf7
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10/0e7f76ee8ff8a33e58a3281a469815b893c41357378f408be8f6d4aa7d1efafb0da064625518e7078381b6a92325949b119dc38fcb30bdbc4e3a35f78c44c439
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.3"
    setprototypeof: "npm:1.1.0"
    statuses: "npm:>= 1.4.0 < 2"
  checksum: 10/e48732657ea0b4a09853d2696a584fa59fa2a8c1ba692af7af3137b5491a997d7f9723f824e7e08eb6a87098532c09ce066966ddf0f9f3dd30905e52301acadb
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.10
  resolution: "http-parser-js@npm:0.5.10"
  checksum: 10/33c53b458cfdf7e43f1517f9bcb6bed1c614b1c7c5d65581a84304110eb9eb02a48f998c7504b8bee432ef4a8ec9318e7009406b506b28b5610fed516242b20a
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:2.0.9":
  version: 2.0.9
  resolution: "http-proxy-middleware@npm:2.0.9"
  dependencies:
    "@types/http-proxy": "npm:^1.17.8"
    http-proxy: "npm:^1.18.1"
    is-glob: "npm:^4.0.1"
    is-plain-obj: "npm:^3.0.0"
    micromatch: "npm:^4.0.2"
  peerDependencies:
    "@types/express": ^4.17.13
  peerDependenciesMeta:
    "@types/express":
      optional: true
  checksum: 10/4ece416a91d52e96f8136c5f4abfbf7ac2f39becbad21fa8b158a12d7e7d8f808287ff1ae342b903fd1f15f2249dee87fabc09e1f0e73106b83331c496d67660
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: "npm:^4.0.0"
    follow-redirects: "npm:^1.0.0"
    requires-port: "npm:^1.0.0"
  checksum: 10/2489e98aba70adbfd8b9d41ed1ff43528be4598c88616c558b109a09eaffe4bb35e551b6c75ac42ed7d948bb7530a22a2be6ef4f0cecacb5927be139f4274594
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.0.0"
  checksum: 10/8097ee2699440c2e64bda52124990cc5b0fb347401c7797b1a0c1efd5a0f79a4ebaa68e8a6ac3e2dde5f09460c1602764da6da2412bad628ed0a3b0ae35e72d4
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10/df59be9e0af479036798a881d1f136c4a29e0b518d4abb863afbd11bf30efa3eeb1d0425fc65942dcc05ab3bf40205ea436b0ff389f2cd20b75b8643d539bf86
  languageName: node
  linkType: hard

"hyperdyperid@npm:^1.2.0":
  version: 1.2.0
  resolution: "hyperdyperid@npm:1.2.0"
  checksum: 10/64abb5568ff17aa08ac0175ae55e46e22831c5552be98acdd1692081db0209f36fff58b31432017b4e1772c178962676a2cc3c54e4d5d7f020d7710cec7ad7a6
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10/6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"ignore@npm:^7.0.3":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10/f134b96a4de0af419196f52c529d5c6120c4456ff8a6b5a14ceaaa399f883e15d58d2ce651c9b69b9388491d4669dda47285d307e827de9304a53a1824801bc6
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: "npm:^4.2.0"
    resolve-cwd: "npm:^3.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10/0b0b0b412b2521739fbb85eeed834a3c34de9bc67e670b3d0b86248fc460d990a7b116ad056c084b87a693ef73d1f17268d6a5be626bb43c998a8b1c8a230004
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10/cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10/d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 10/8771303d66c51be433b564427c16011a8e3fbc3449f1f11ea50efb30a4369495f1d0e89f0fc12bdec0bd7e49102ced5d137e031d39ea09821cb3c717fcf21e69
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.1.1":
  version: 0.1.1
  resolution: "inline-style-parser@npm:0.1.1"
  checksum: 10/e661f4fb6824a41076c4d23358e8b581fd3410fbfb9baea4cb542a85448b487691c3b9bbb58ad73a95613041ca616f059595f19cadd0c22476a1fffa79842b48
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 10/873e0e7fcfe32f999aa0997a0b648b1244508e56e3ea6b8259b5245b50b5eeb3853fba221f96692bd6d1def501da76c32d64a5cb22a0b26cdd9b445664f805e0
  languageName: node
  linkType: hard

"interpret@npm:^3.1.1":
  version: 3.1.1
  resolution: "interpret@npm:3.1.1"
  checksum: 10/bc9e11126949c4e6ff49b0b819e923a9adc8e8bf3f9d4f2d782de6d5f592774f6fee4457c10bd08c6a2146b4baee460ccb242c99e5397defa9c846af0d00505a
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10/864d0cced0c0832700e9621913a6429ccdc67f37c1bd78fb8c6789fff35c9d167cb329134acad2290497a53336813ab4798d2794fd675d5eb33b5fdf0982b9ca
  languageName: node
  linkType: hard

"ipaddr.js@npm:^2.1.0":
  version: 2.2.0
  resolution: "ipaddr.js@npm:2.2.0"
  checksum: 10/9e1cdd9110b3bca5d910ab70d7fb1933e9c485d9b92cb14ef39f30c412ba3fe02a553921bf696efc7149cc653453c48ccf173adb996ec27d925f1f340f872986
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10/73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10/078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.0":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 10/3261a8b858edcc6c9566ba1694bf829e126faa88911d1c0a747ea658c5d81b14b6955e3a702d59dabadd58fdd440c01f321aa71d6547105fd21d03f94d0597e7
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/452b2c2fb7f889cbbf7e54609ef92cf6c24637c568acc7e63d166812a0fb365ae8a504c333a29add8bdb1686704068caa7f4e4b639b650dde4f00a038b8941fb
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: 10/b698118f04feb7eaf3338922bd79cba064ea54a1c3db6ec8c0c8d8ee7613e7e5854d802d3ef646812a8a3ace81182a085dfa0a71cc68b06f3fa794b9783b3c90
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 10/a6ad5492cf9d1746f73b6744e0c43c0020510b59d56ddcb78a91cbc173f09b5e6beff53d75c9c5a29feb618bfef2bf458e025ecf3a57ad2268e2fb2569f56215
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: 10/c50b75a2ab66ab3e8b92b3bc534e1ea72ca25766832c0623ac22d134116a98bcf012197d1caabe1d1c4bd5f84363d4aa5c36bb4b585fbcaf57be172cd10a1a03
  languageName: node
  linkType: hard

"is-network-error@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-network-error@npm:1.1.0"
  checksum: 10/b2fe6aac07f814a9de275efd05934c832c129e7ba292d27614e9e8eec9e043b7a0bbeaeca5d0916b0f462edbec2aa2eaee974ee0a12ac095040e9515c222c251
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-plain-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-plain-obj@npm:3.0.0"
  checksum: 10/a6ebdf8e12ab73f33530641972a72a4b8aed6df04f762070d823808303e4f76d87d5ea5bd76f96a7bbe83d93f04ac7764429c29413bd9049853a69cb630fb21c
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10/6dc45da70d04a81f35c9310971e78a6a3c7a63547ef782e3a07ee3674695081b6ca4e977fbb8efc48dae3375e0b34558d2bcd722aec9bddfa2d7db5b041be8ce
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10/2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10/b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-wsl@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-wsl@npm:3.1.0"
  dependencies:
    is-inside-container: "npm:^1.0.0"
  checksum: 10/f9734c81f2f9cf9877c5db8356bfe1ff61680f1f4c1011e91278a9c0564b395ae796addb4bf33956871041476ec82c3e5260ed57b22ac91794d4ae70a1d2f0a9
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10/f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10/db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10/40bbdd1e937dfd8c830fa286d0f665e81b7a78bdabcd4565f6d5667c99828bda3db7fb7ac6b96a3e2e8a2461ddbc5452d9f8bc7d00cb00075fa6a3e99f5b6a81
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10/bbc4496c2f304d799f8ec22202ab38c010ac265c441947f075c0f7d46bd440b45c00e46017cf9053453d42182d768b1d6ed0e70a142c95ab00df9843aa5ab80e
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10/aa5271c0008dfa71b6ecc9ba1e801bf77b49dc05524e8c30d58aaf5b9505e0cd12f25f93165464d4266a518c5c75284ecb598fbd89fec081ae77d2c9d3327695
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/86a83421ca1cf2109a9f6d193c06c31ef04a45e72a74579b11060b1e7bb9b6337a4e6f04abfb8857e2d569c271273c65e855ee429376a0d7c91ad91db42accd1
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10/5526983462799aced011d776af166e350191b816821ea7bcf71cab3e5272657b062c47dc30697a22a43656e3ced78893a42de677f9ccf276a28c913190953b82
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10/f1faaa4684efaf57d64087776018d7426312a59aa6eeb4e0e3a777347d23cd286ad18f427e98f0e3dee666103d7404c9d7abc5f240406a912fa16bd6695437fa
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: "npm:^5.0.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
  checksum: 10/3d93742e56b1a73a145d55b66e96711fbf87ef89b96c2fab7cfdfba8ec06612591a982111ca2b712bb853dbc16831ec8b43585a2a96b83862d6767de59cbf83d
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    co: "npm:^4.6.0"
    dedent: "npm:^1.0.0"
    is-generator-fn: "npm:^2.0.0"
    jest-each: "npm:^29.7.0"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
    pure-rand: "npm:^6.0.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10/716a8e3f40572fd0213bcfc1da90274bf30d856e5133af58089a6ce45089b63f4d679bd44e6be9d320e8390483ebc3ae9921981993986d21639d9019b523123d
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    create-jest: "npm:^29.7.0"
    exit: "npm:^0.1.2"
    import-local: "npm:^3.0.2"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    yargs: "npm:^17.3.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10/6cc62b34d002c034203065a31e5e9a19e7c76d9e8ef447a6f70f759c0714cb212c6245f75e270ba458620f9c7b26063cd8cf6cd1f7e3afd659a7cc08add17307
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/test-sequencer": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-jest: "npm:^29.7.0"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    deepmerge: "npm:^4.2.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-circus: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    parse-json: "npm:^5.2.0"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 10/6bdf570e9592e7d7dd5124fc0e21f5fe92bd15033513632431b211797e3ab57eaa312f83cc6481b3094b72324e369e876f163579d60016677c117ec4853cf02b
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.6.3"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10/6f3a7eb9cd9de5ea9e5aa94aed535631fa6f80221832952839b3cb59dd419b91c20b73887deb0b62230d06d02d6b6cf34ebb810b88d904bb4fe1e2e4f0905c98
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: "npm:^3.0.0"
  checksum: 10/8d48818055bc96c9e4ec2e217a5a375623c0d0bfae8d22c26e011074940c202aa2534a3362294c81d981046885c05d304376afba9f2874143025981148f3e96d
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10/bd1a077654bdaa013b590deb5f7e7ade68f2e3289180a8c8f53bc8a49f3b40740c0ec2d3a3c1aee906f682775be2bebbac37491d80b634d15276b0aa0f2e3fda
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10/9cf7045adf2307cc93aed2f8488942e39388bff47ec1df149a997c6f714bfc66b2056768973770d3f8b1bf47396c19aa564877eb10ec978b952c6018ed1bd637
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10/88ac9102d4679d768accae29f1e75f592b760b44277df288ad76ce5bf038c3f5ce3719dea8aa0f035dac30e9eb034b848ce716b9183ad7cc222d029f03e92205
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/graceful-fs": "npm:^4.1.3"
    "@types/node": "npm:*"
    anymatch: "npm:^3.0.3"
    fb-watchman: "npm:^2.0.0"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.9"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/8531b42003581cb18a69a2774e68c456fb5a5c3280b1b9b77475af9e346b6a457250f9d756bfeeae2fe6cbc9ef28434c205edab9390ee970a919baddfa08bb85
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10/e3950e3ddd71e1d0c22924c51a300a1c2db6cf69ec1e51f95ccf424bcc070f78664813bef7aed4b16b96dfbdeea53fe358f8aeaaea84346ae15c3735758f1605
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10/981904a494299cf1e3baed352f8a3bd8b50a8c13a662c509b6a53c31461f94ea3bfeffa9d5efcfeb248e384e318c87de7e3baa6af0f79674e987482aa189af40
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10/31d53c6ed22095d86bab9d14c0fa70c4a92c749ea6ceece82cf30c22c9c0e26407acdfbdb0231435dc85a98d6d65ca0d9cbcd25cd1abb377fe945e843fb770b9
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10/ae51d1b4f898724be5e0e52b2268a68fcd876d9b20633c864a6dd6b1994cbc48d62402b0f40f3a1b669b30ebd648821f086c26c08ffde192ced951ff4670d51c
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 10/db1a8ab2cb97ca19c01b1cfa9a9c8c69a143fde833c14df1fab0766f411b1148ff0df878adea09007ac6a2085ec116ba9a996a6ad104b1e58c20adbf88eed9b2
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 10/0518beeb9bf1228261695e54f0feaad3606df26a19764bc19541e0fc6e2a3737191904607fb72f3f2ce85d9c16b28df79b7b1ec9443aa08c3ef0e9efda6f8f2a
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: "npm:^29.6.3"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10/1e206f94a660d81e977bcfb1baae6450cb4a81c92e06fad376cc5ea16b8e8c6ea78c383f39e95591a9eb7f925b6a1021086c38941aa7c1b8a6a813c2f6e93675
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-pnp-resolver: "npm:^1.2.2"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    resolve: "npm:^1.20.0"
    resolve.exports: "npm:^2.0.0"
    slash: "npm:^3.0.0"
  checksum: 10/faa466fd9bc69ea6c37a545a7c6e808e073c66f46ab7d3d8a6ef084f8708f201b85d5fe1799789578b8b47fa1de47b9ee47b414d1863bc117a49e032ba77b7c7
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/environment": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    graceful-fs: "npm:^4.2.9"
    jest-docblock: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-leak-detector: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-resolve: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    source-map-support: "npm:0.5.13"
  checksum: 10/9d8748a494bd90f5c82acea99be9e99f21358263ce6feae44d3f1b0cd90991b5df5d18d607e73c07be95861ee86d1cbab2a3fc6ca4b21805f07ac29d47c1da1e
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/globals": "npm:^29.7.0"
    "@jest/source-map": "npm:^29.6.3"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    cjs-module-lexer: "npm:^1.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-bom: "npm:^4.0.0"
  checksum: 10/59eb58eb7e150e0834a2d0c0d94f2a0b963ae7182cfa6c63f2b49b9c6ef794e5193ef1634e01db41420c36a94cefc512cdd67a055cd3e6fa2f41eaf0f82f5a20
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@babel/generator": "npm:^7.7.2"
    "@babel/plugin-syntax-jsx": "npm:^7.7.2"
    "@babel/plugin-syntax-typescript": "npm:^7.7.2"
    "@babel/types": "npm:^7.3.3"
    "@jest/expect-utils": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
    chalk: "npm:^4.0.0"
    expect: "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    natural-compare: "npm:^1.4.0"
    pretty-format: "npm:^29.7.0"
    semver: "npm:^7.5.3"
  checksum: 10/cb19a3948256de5f922d52f251821f99657339969bf86843bd26cf3332eae94883e8260e3d2fba46129a27c3971c1aa522490e460e16c7fad516e82d10bbf9f8
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10/30d58af6967e7d42bd903ccc098f3b4d3859ed46238fbc88d4add6a3f10bea00c226b93660285f058bc7a65f6f9529cf4eb80f8d4707f79f9e3a23686b4ab8f3
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10/8ee1163666d8eaa16d90a989edba2b4a3c8ab0ffaa95ad91b08ca42b015bfb70e164b247a5b17f9de32d096987cada63ed8491ab82761bfb9a28bc34b27ae161
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    jest-util: "npm:^29.7.0"
    string-length: "npm:^4.0.1"
  checksum: 10/4f616e0345676631a7034b1d94971aaa719f0cd4a6041be2aa299be437ea047afd4fe05c48873b7963f5687a2f6c7cbf51244be8b14e313b97bfe32b1e127e55
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "npm:*"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10/06c6e2a84591d9ede704d5022fc13791e8876e83397c89d481b0063332abbb64c0f01ef4ca7de520b35c7a1058556078d6bdc3631376f4e9ffb42316c1a8488e
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10/364cbaef00d8a2729fc760227ad34b5e60829e0869bd84976bdfbd8c0d0f9c2f22677b3e6dd8afa76ed174765351cd12bae3d4530c62eefb3791055127ca9745
  languageName: node
  linkType: hard

"jest@npm:~29.7":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    import-local: "npm:^3.0.2"
    jest-cli: "npm:^29.7.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10/97023d78446098c586faaa467fbf2c6b07ff06e2c85a19e3926adb5b0effe9ac60c4913ae03e2719f9c01ae8ffd8d92f6b262cedb9555ceeb5d19263d8c6362a
  languageName: node
  linkType: hard

"jolokia.js@npm:2.2.4, jolokia.js@npm:^2.1.7, jolokia.js@npm:^2.1.8":
  version: 2.2.4
  resolution: "jolokia.js@npm:2.2.4"
  checksum: 10/c7c0f1fa9f4a4ded43edf0425978b84b6bff1fef1a9ac5f6b6a15ec1dd04f2b0e4b5314c1d6dc1996d91990e71944672215db9151150cab24108073a9b879e1f
  languageName: node
  linkType: hard

"jquery@npm:^3.7.1":
  version: 3.7.1
  resolution: "jquery@npm:3.7.1"
  checksum: 10/17be9929f5fa37697d9848284f0d108c543318ef79ec794e130cd0c49f6c050d60c803a69e8cfa16fa19f5ff7cdb814a6905cceab0831186560c65ed113cd579
  languageName: node
  linkType: hard

"js-logger@npm:^1.6.1":
  version: 1.6.1
  resolution: "js-logger@npm:1.6.1"
  checksum: 10/2d46604c64fa0564014349c2bb9cc4e95c50fd9a37e7e7b8b87176da6333b6f8bd17a2368a83ebdb4e336fa0ce252682d1adb08f2c7eb993272e1c99ced01d09
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:^3.10.0, js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/9e22d80b4d0105b9899135365f746d47466ed53ef4223c529b3c0f7a39907743fdbd3c4379f94f1106f02755b5e90b2faaf84801a891135544e1ea475d1a1379
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10/8e5a7de6b70a8bd71f9cb0b5a7ade6a73ae6ab55e697c74cc997cede97417a3a65ed86c36f7dd6125fe49766e8386c845023d9e213916ca92c9dfdd56e2babf3
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10/5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10/02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10/59169a081e4eeb6f9559ae1f938f656191c000e0512aa6df9f3c8b2437a4ab1823819c6b9fd1818a4e39593ccfd72e9a051fdd3e2d1e340ed913679e888ded8c
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jwt-decode@npm:^4.0.0":
  version: 4.0.0
  resolution: "jwt-decode@npm:4.0.0"
  checksum: 10/87b569e4a9a0067fb0d592bcf3b2ac3e638e49beee28620eeb07bef1b4470f4077dea68c15d191dd68e076846c3af8394be3bcaecffedc6e97433b221fdbbcf3
  languageName: node
  linkType: hard

"keycloak-js@npm:^26.0.6":
  version: 26.2.0
  resolution: "keycloak-js@npm:26.2.0"
  checksum: 10/1afde5975491e12e5b1be747d054475dd3d315fe884c980390b3b9dd4f04e9f0e7746f2c6f615ac1f2297c2b815dc2ec9a1054a7db3050968d2b2bee2f772969
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10/5873d303fb36aad875b7538798867da2ae5c9e328d67194b0162a3659a627d22f742fc9c4ae95cd1704132a24b00cae5041fc00c0f6ef937dc17080dc4dbb962
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10/0c0ecaf00a5c6173d25059c7db2113850b5457016dfa1d0e3ef26da4704fbb186b4938d7611246d86f0ddf1bccf26828daa5877b1f232a65e7373d0122a83e7f
  languageName: node
  linkType: hard

"kleur@npm:^4.0.3":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 10/44d84cc4eedd4311099402ef6d4acd9b2d16e08e499d6ef3bb92389bd4692d7ef09e35248c26e27f98acac532122acb12a1bfee645994ae3af4f0a37996da7df
  languageName: node
  linkType: hard

"launch-editor@npm:^2.6.1":
  version: 2.10.0
  resolution: "launch-editor@npm:2.10.0"
  dependencies:
    picocolors: "npm:^1.0.0"
    shell-quote: "npm:^1.8.1"
  checksum: 10/2ef26369d89ad22938c1f5c343a622ff2e8e2f7709901c739ef38319a103b7da400afc147005e765fc0c22fd467eeb5f63f98568b3882e21f7782a4061fdeb60
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10/638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10/0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: 10/555ae002869c1e8942a0efd29a99b50a0ce6c3296efea95caf48f00d7f6f7f659203ed6613688b6181aa81dc76de3e65ece43094c6dffef3127fe1a84d973cd3
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10/83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10/cd0b2819786e6e80cb9f5cda26b1a8fc073daaf04e48d4cb462fa4663ec9adb3a5387aa22d7129e48eed1afa05b482e2a6b79bfc99b86886364449500cbb00fd
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.19, lodash@npm:^4.17.20, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10/6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 10/1c233d2da35056e8c49fae8097ee061b8c799b2f02e33c2bf32f9913c7de8fb481ab04dab7df35e94156c800f5f34e99acbf32b21781d87c3aa43ef7b748b79e
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10/951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"lz-string@npm:^1.5.0":
  version: 1.5.0
  resolution: "lz-string@npm:1.5.0"
  bin:
    lz-string: bin/bin.js
  checksum: 10/e86f0280e99a8d8cd4eef24d8601ddae15ce54e43ac9990dfcb79e1e081c255ad24424a30d78d2ad8e51a8ce82a66a930047fed4b4aa38c6f0b392ff9300edfc
  languageName: node
  linkType: hard

"make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: "npm:^4.0.1"
    semver: "npm:^5.6.0"
  checksum: 10/043548886bfaf1820323c6a2997e6d2fa51ccc2586ac14e6f14634f7458b4db2daf15f8c310e2a0abd3e0cddc64df1890d8fc7263033602c47bb12cbfcf86aab
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10/bf0731a2dd3aab4db6f3de1585cea0b746bb73eb5a02e3d8d72757e376e64e6ada190b1eddcde5b2f24a81b688a9897efd5018737d05e02e2a671dda9cff8a8a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10/4c66ddfc654537333da952c084f507fa4c30c707b1635344eb35be894d797ba44c901a9cebe914aa29a7f61357543ba09b09dddbd7f65b4aee756b450f169f40
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"mdast-util-definitions@npm:^5.0.0":
  version: 5.1.2
  resolution: "mdast-util-definitions@npm:5.1.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10/4491b7c551ce1bdeb6c8fb1968cd461acb01ca1584f12c240755541a92d7f02bc5b9c9d6303d50deaed6d959ba58fe9a352a3e676e0f1d954e003de1277f57e4
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^1.0.0":
  version: 1.3.1
  resolution: "mdast-util-from-markdown@npm:1.3.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    mdast-util-to-string: "npm:^3.1.0"
    micromark: "npm:^3.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-decode-string: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10/1d334a54ddd6481ec4acf64c2c537b6463bc5113ba5a408f65c228dcc302d46837352814f11307af0f8b51dd7e4a0b887ce692e4d30ff31ff9d578b8ca82810b
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^12.1.0":
  version: 12.3.0
  resolution: "mdast-util-to-hast@npm:12.3.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-definitions: "npm:^5.0.0"
    micromark-util-sanitize-uri: "npm:^1.1.0"
    trim-lines: "npm:^3.0.0"
    unist-util-generated: "npm:^2.0.0"
    unist-util-position: "npm:^4.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10/82b72bf46863f0f5683dbf1c5917186ee2da2e06af1a5f5aaeca51b880f4cb2b3ae0463ebb4fa1a776f5d3c73f5fc6cd542920060cf5040f3d4431607ee73cce
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^3.1.0":
  version: 3.2.0
  resolution: "mdast-util-to-string@npm:3.2.0"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
  checksum: 10/fafe201c12a0d412a875fe8540bf70b4360f3775fb7f0d19403ba7b59e50f74f730e3b405c72ad940bc8a3ec1ba311f76dfca61c4ce585dce1ccda2168ec244f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10/38e0984db39139604756903a01397e29e17dcb04207bb3e081412ce725ab17338ecc47220c1b186b6bbe79a658aad1b0d41142884f5a481f36290cdefbe6aa46
  languageName: node
  linkType: hard

"memfs@npm:^4.6.0":
  version: 4.17.2
  resolution: "memfs@npm:4.17.2"
  dependencies:
    "@jsonjoy.com/json-pack": "npm:^1.0.3"
    "@jsonjoy.com/util": "npm:^1.3.0"
    tree-dump: "npm:^1.0.1"
    tslib: "npm:^2.0.0"
  checksum: 10/105175204e74e836460fbf18e431bc24def3f5ea7ecd94d644f35992dc28b5a4c5f425849dd5f342878ef0ba032508c05b2756e026491635a59fc7f631cbfcde
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10/52117adbe0313d5defa771c9993fe081e2d2df9b840597e966aadafde04ae8d0e3da46bac7ca4efc37d4d2b839436582659cd49c6a43eacb3fe3050896a105d1
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10/6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10/a385dd974faa34b5dd021b2bbf78c722881bf6f003bfe6d391d7da3ea1ed625d1ff10ddd13c57531f628b3e785be38d3eed10ad03cebd90b76932413df9a1820
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-core-commonmark@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-factory-destination: "npm:^1.0.0"
    micromark-factory-label: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-factory-title: "npm:^1.0.0"
    micromark-factory-whitespace: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-classify-character: "npm:^1.0.0"
    micromark-util-html-tag-name: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10/a73694d223ac8baad8ff00597a3c39d61f5b32bfd56fe4bcf295d75b2a4e8e67fb2edbfc7cc287b362b9d7f6d24fce08b6a7e8b5b155d79bcc1e4d9b2756ffb2
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-destination@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/9e2b5fb5fedbf622b687e20d51eb3d56ae90c0e7ecc19b37bd5285ec392c1e56f6e21aa7cfcb3c01eda88df88fe528f3acb91a5f57d7f4cba310bc3cd7f824fa
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-label@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10/fcda48f1287d9b148c562c627418a2ab759cdeae9c8e017910a0cba94bb759a96611e1fc6df33182e97d28fbf191475237298983bb89ef07d5b02464b1ad28d5
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-space@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/b58435076b998a7e244259a4694eb83c78915581206b6e7fc07b34c6abd36a1726ade63df8972fbf6c8fa38eecb9074f4e17be8d53f942e3b3d23d1a0ecaa941
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-title@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/4432d3dbc828c81f483c5901b0c6591a85d65a9e33f7d96ba7c3ae821617a0b3237ff5faf53a9152d00aaf9afb3a9f185b205590f40ed754f1d9232e0e9157b1
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-whitespace@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/ef0fa682c7d593d85a514ee329809dee27d10bc2a2b65217d8ef81173e33b8e83c549049764b1ad851adfe0a204dec5450d9d20a4ca8598f6c94533a73f73fcd
  languageName: node
  linkType: hard

"micromark-util-character@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-character@npm:1.2.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/88cf80f9b4c95266f24814ef587fb4180454668dcc3be4ac829e1227188cf349c8981bfca29e3eab1682f324c2c47544c0b0b799a26fbf9df5f156c6a84c970c
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-chunked@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10/c435bde9110cb595e3c61b7f54c2dc28ee03e6a57fa0fc1e67e498ad8bac61ee5a7457a2b6a73022ddc585676ede4b912d28dcf57eb3bd6951e54015e14dc20b
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-classify-character@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/8499cb0bb1f7fb946f5896285fcca65cd742f66cd3e79ba7744792bd413ec46834f932a286de650349914d02e822946df3b55d03e6a8e1d245d1ddbd5102e5b0
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-combine-extensions@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/ee78464f5d4b61ccb437850cd2d7da4d690b260bca4ca7a79c4bb70291b84f83988159e373b167181b6716cb197e309bc6e6c96a68cc3ba9d50c13652774aba9
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-numeric-character-reference@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10/4733fe75146e37611243f055fc6847137b66f0cde74d080e33bd26d0408c1d6f44cabc984063eee5968b133cb46855e729d555b9ff8d744652262b7b51feec73
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-string@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10/f1625155db452f15aa472918499689ba086b9c49d1322a08b22bfbcabe918c61b230a3002c8bc3ea9b1f52ca7a9bb1c3dd43ccb548c7f5f8b16c24a1ae77a813
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-encode@npm:1.1.0"
  checksum: 10/4ef29d02b12336918cea6782fa87c8c578c67463925221d4e42183a706bde07f4b8b5f9a5e1c7ce8c73bb5a98b261acd3238fecd152e6dd1cdfa2d1ae11b60a0
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-html-tag-name@npm:1.2.0"
  checksum: 10/ccf0fa99b5c58676dc5192c74665a3bfd1b536fafaf94723bd7f31f96979d589992df6fcf2862eba290ef18e6a8efb30ec8e1e910d9f3fc74f208871e9f84750
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-normalize-identifier@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10/8655bea41ffa4333e03fc22462cb42d631bbef9c3c07b625fd852b7eb442a110f9d2e5902a42e65188d85498279569502bf92f3434a1180fc06f7c37edfbaee2
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-resolve-all@npm:1.1.0"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10/1ce6c0237cd3ca061e76fae6602cf95014e764a91be1b9f10d36cb0f21ca88f9a07de8d49ab8101efd0b140a4fbfda6a1efb72027ab3f4d5b54c9543271dc52c
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^1.0.0, micromark-util-sanitize-uri@npm:^1.1.0":
  version: 1.2.0
  resolution: "micromark-util-sanitize-uri@npm:1.2.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10/0d024100d95ffb88bf75f3360e305b545c1eb745430959b8633f7aa93f37ec401fc7094c90c97298409a9e30d94d53b895bae224e1bb966bea114976cfa0fd48
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-subtokenize@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10/075a1db6ea586d65827d3eead33dbfc520c4e43659c93fcd8fd82f44a7b75cfe61dcde967a3dfcc2ffd999347440ba5aa6698e65a04f3fc627e13e9f12a1a910
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-symbol@npm:1.1.0"
  checksum: 10/a26b6b1efd77a715a4d9bbe0a5338eaf3d04ea5e85733e34fee56dfeabf64495c0afc5438fe5220316884cd3a5eae1f17768e0ff4e117827ea4a653897466f86
  languageName: node
  linkType: hard

"micromark-util-types@npm:^1.0.0, micromark-util-types@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-util-types@npm:1.1.0"
  checksum: 10/287ac5de4a3802bb6f6c3842197c294997a488db1c0486e03c7a8e674d9eb7720c17dda1bcb814814b8343b338c4826fcbc0555f3e75463712a60dcdb53a028e
  languageName: node
  linkType: hard

"micromark@npm:^3.0.0":
  version: 3.2.0
  resolution: "micromark@npm:3.2.0"
  dependencies:
    "@types/debug": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    micromark-core-commonmark: "npm:^1.0.1"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10/560a4a501efc3859d622461aaa9345fb95b99a2f34d3d3f2a775ab04de1dd857cb0f642083a6b28ab01bd817f5f0741a1be9857fd702f45e04a3752927a66719
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10/9e7834be3d66ae7f10eaa69215732c6d389692b194f876198dca79b2b90cbf96688d9d5d05ef7987b20f749b769b11c01766564264ea5f919c88b32a29011311
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:^2.1.31, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10/b7d98bb1e006c0e63e2c91b590fe1163b872abf8f7ef224d53dd31499c2197278a6d3d0864c45239b1a93d22feaf6f9477e9fc847eef945838150b8c02d03170
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10/d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 10/034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10/7e719047612411fe071332a7498cf0448bbe43c485c0d780046c76633a771b223ff49bd00267be122cedebb897037fdb527df72335d0d0f74724604ca70b37ad
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 10/bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:~2.9":
  version: 2.9.2
  resolution: "mini-css-extract-plugin@npm:2.9.2"
  dependencies:
    schema-utils: "npm:^4.0.0"
    tapable: "npm:^2.2.1"
  peerDependencies:
    webpack: ^5.0.0
  checksum: 10/db6ddb8ba56affa1a295b57857d66bad435d36e48e1f95c75d16fadd6c70e3ba33e8c4141c3fb0e22b4d875315b41c4f58550c6ac73b50bdbe429f768297e3ff
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10/cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimatch@npm:3.0.5":
  version: 3.0.5
  resolution: "minimatch@npm:3.0.5"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/8f9707491183a07a9542b8cf45aacb3745ba9fe6c611173fb225d7bf191e55416779aee31e17673a516a178af02d8d3d71ddd36ae3d5cc2495f627977ad1a012
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10/61682162d29f45d3152b78b08bab7fb32ca10899bc5991ffe98afc18c9e9543bd1e3be94f8b8373ba6262497db63607079dc242ea62e43e7b2270837b7347c93
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10/ae0f45436fb51344dcb87938446a32fbebb540d0e191d63b35e1c773d47512e17307bf54aa88326cc6d176594d00e4423563a091f7266c2f9a6872cdc1e234d1
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10/d71b8dcd4b5af2fe13ecf3bd24070263489404fe216488c5ba7e38ece1f54daf219e72a833a3a2dc404331e870e9f44963a33399589490956bff003a3404d3b2
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"monaco-editor@npm:^0.52.0":
  version: 0.52.2
  resolution: "monaco-editor@npm:0.52.2"
  checksum: 10/0d4962d69ffa0a8df040faa9c582cef1893fa3fb617feca8f1425c5e670e74c2856104b9a2b01cbda0103a5e5f92f58843206bc9a0e070471c0c1270d7f52a96
  languageName: node
  linkType: hard

"mri@npm:^1.1.0":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 10/6775a1d2228bb9d191ead4efc220bd6be64f943ad3afd4dcb3b3ac8fc7b87034443f666e38805df38e8d047b29f910c3cc7810da0109af83e42c82c73bd3f6bc
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10/0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multicast-dns@npm:^7.2.5":
  version: 7.2.5
  resolution: "multicast-dns@npm:7.2.5"
  dependencies:
    dns-packet: "npm:^5.2.2"
    thunky: "npm:^1.0.2"
  bin:
    multicast-dns: cli.js
  checksum: 10/e9add8035fb7049ccbc87b1b069f05bb3b31e04fe057bf7d0116739d81295165afc2568291a4a962bee01a5074e475996816eed0f50c8110d652af5abb74f95a
  languageName: node
  linkType: hard

"nanoid@npm:3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10/2723fb822a17ad55c93a588a4bc44d53b22855bf4be5499916ca0cab1e7165409d0b288ba2577d7b029f10ce18cf2ed8e703e5af31c984e1e2304277ef979837
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10/d98c04a136583afd055746168f1067d58ce4bfe6e4c73ca1d339567f81ea1f7e665b5bd1e81f4771c67b6c2ea89b21cb2adaea2b16058c7dc31317778f931dab
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10/1a7948fea86f2b33ec766bc899c88796a51ba76a4afc9026764aedc6e7cde692a09067031e4a1bf6db4f978ccd99e7f5b6c03fe47ad9865c3d4f99050d67e002
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10/0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-forge@npm:^1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 10/05bab6868633bf9ad4c3b1dd50ec501c22ffd69f556cdf169a00998ca1d03e8107a6032ba013852f202035372021b845603aeccd7dfcb58cdb7430013b3daa8d
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/806fd8e3adc9157e17bf0d4a2c899cf6b98a0bbe9f453f630094ce791866271f6cddcaf2133e6513715d934fcba2014d287c7053d5d7934937b3a34d5a3d84ad
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10/b7afc2b65e56f7035b1a2eec57ae0fbdee7d742b1cdcd0f4387562b6527a011ab1cbe9f64cc8b3cca61e3297c9637c8bf61cec2e6b8d3a711d4b5267dfafbe02
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10/88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 10/5ae699402c9d5ffa330adc348fcd6fc6e6a155ab7c811b96e30b7ecab60ceef821d8f86443869671dda71bbc47f4b9625739c82ad247e883e9aefe875bfb8659
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10/5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10/5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"oauth4webapi@npm:^2.17.0":
  version: 2.17.0
  resolution: "oauth4webapi@npm:2.17.0"
  checksum: 10/bfe4d3c0e5ec1a67ee00889b758cabccdb45b3cfbcfb62fee5ad935f1f0d7c78432c69718b79303956f006c0a950eccc0b1f19ef85aeb0067b85d06439704dc9
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 10/53ff4ab3a13cc33ba6c856cf281f2965c0aec9720967af450e8fd06cfd50aceeefc791986a16bcefa14e7898b3ca9acdfcf15b9d9a1b9c7e1366581a8ad6e65e
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1, on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10/8e81472c5028125c8c39044ac4ab8ba51a7cdc19a9fbd4710f5d524a74c6d8c9ded4dd0eed83f28d3d33ac1d7a6a439ba948ccb765ac6ce87f30450a26bfe2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10/870766c16345855e2012e9422ba1ab110c7e44ad5891a67790f84610bd70a72b67fdd71baf497295f1d1bf38dd4c92248f825d48729c53c0eae5262fb69fa171
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10/cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10/e9fd0695a01cf226652f0385bf16b7a24153dbbb2039f764c8ba6d2306a8506b0e4ce570de6ad99c7a6eb49520743afdb66edd95ee979c1a342554ed49a9aadd
  languageName: node
  linkType: hard

"open@npm:^10.0.3":
  version: 10.1.2
  resolution: "open@npm:10.1.2"
  dependencies:
    default-browser: "npm:^5.2.1"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^3.1.0"
  checksum: 10/dc0496486fd79289844d8cac678402384488696db60ae5c5a175748cd728c381689cd937527762685dc27530408da0f0dac7653769f9730e773aa439d6674b98
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 10/7f1b64db17fc54acf359167d62898115dcf2a64bf6b3b038e4faf36fc059e5ed762fb9624df8ed04b25bee8de3ab8d72dea9879a2a960cd12e23c420a4aca6ed
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10/84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10/513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-retry@npm:^6.2.0":
  version: 6.2.1
  resolution: "p-retry@npm:6.2.1"
  dependencies:
    "@types/retry": "npm:0.12.2"
    is-network-error: "npm:^1.0.0"
    retry: "npm:^0.13.1"
  checksum: 10/7104ef13703b155d70883b0d3654ecc03148407d2711a4516739cf93139e8bec383451e14925e25e3c1ae04dbace3ed53c26dc3853c1e9b9867fcbdde25f4cdc
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10/f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10/62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10/407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-browserify@npm:~1.0":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10/7e7368a5207e7c6b9051ef045711d0dc3c2b6203e96057e408e6e74d09f383061010d2be95cb8593fe6258a767c3e9fc6b2bfc7ce8d48ae8c3d9f6994cca9ad8
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10/060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10/49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10/2e30f6a0144679c1f95c98e166b96e6acd1e72be9417830fefc8de7ac1992147eb9a4c7acaa59119fb1b3c34eec393b2129ef27e24b2054a3906fc4fb0d1398e
  languageName: node
  linkType: hard

"path-type@npm:^6.0.0":
  version: 6.0.0
  resolution: "path-type@npm:6.0.0"
  checksum: 10/b9f6eaf7795c48d5c9bc4c6bc3ac61315b8d36975a73497ab2e02b764c0836b71fb267ea541863153f633a069a1c2ed3c247cb781633842fc571c655ac57c00e
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10/8b97cbf9dc6d4c1320cc238a2db0fc67547f9dc77011729ff353faf34f1936ea1a4d7f3c63b2f4980b253be77bcc72ea1e9e76ee3fd53cce2aafb6a8854d07ec
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 10/2427f371366081ae42feb58214f04805d6b41d6b84d74480ebcc9e0ddbd7105a139f7c653daeaf83ad8a1a77214cf07f64178e76de048128fec501eab3305a96
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10/9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.1.0":
  version: 3.1.0
  resolution: "postcss-modules-extract-imports@npm:3.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/00bfd3aff045fc13ded8e3bbfd8dfc73eff9a9708db1b2a132266aef6544c8d2aee7a5d7e021885f6f9bbd5565a9a9ab52990316e21ad9468a2534f87df8e849
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.5":
  version: 4.2.0
  resolution: "postcss-modules-local-by-default@npm:4.2.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
    postcss-selector-parser: "npm:^7.0.0"
    postcss-value-parser: "npm:^4.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/552329aa39fbf229b8ac5a04f8aed0b1553e7a3c10b165ee700d1deb020c071875b3df7ab5e3591f6af33d461df66d330ec9c1256229e45fc618a47c60f41536
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.2.0":
  version: 3.2.1
  resolution: "postcss-modules-scope@npm:3.2.1"
  dependencies:
    postcss-selector-parser: "npm:^7.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/51c747fa15cedf1b2856da472985ea7a7bb510a63daf30f95f250f34fce9e28ef69b802e6cc03f9c01f69043d171bc33279109a9235847c2d3a75c44eac67334
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/18021961a494e69e65da9e42b4436144c9ecee65845c9bfeff2b7a26ea73d60762f69e288be8bb645447965b8fd6b26a264771136810dc0172bd31b940aee4f2
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^7.0.0":
  version: 7.1.0
  resolution: "postcss-selector-parser@npm:7.1.0"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/2caf09e66e2be81d45538f8afdc5439298c89bea71e9943b364e69dce9443d9c5ab33f4dd8b237f1ed7d2f38530338dcc189c1219d888159e6afb5b0afe58b19
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10/e4e4486f33b3163a606a6ed94f9c196ab49a37a7a7163abfcd469e5f113210120d70b8dd5e33d64636f41ad52316a3725655421eb9a1094f1bcab1db2f555c62
  languageName: node
  linkType: hard

"postcss@npm:^8.4.33":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/9e4fbe97574091e9736d0e82a591e29aa100a0bf60276a926308f8c57249698935f35c5d2f4e80de778d0cbb8dcffab4f383d85fd50c5649aca421c3df729b86
  languageName: node
  linkType: hard

"pretty-error@npm:^4.0.0":
  version: 4.0.0
  resolution: "pretty-error@npm:4.0.0"
  dependencies:
    lodash: "npm:^4.17.20"
    renderkid: "npm:^3.0.0"
  checksum: 10/0212ad8742f8bb6f412f95b07d7f6874c55514ac4384f4f7de0defe77e767cca99f667c2316529f62a041fa654194a99c1ee7e321e1b7f794b5cc700777634d6
  languageName: node
  linkType: hard

"pretty-format@npm:^27.0.2":
  version: 27.5.1
  resolution: "pretty-format@npm:27.5.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^17.0.1"
  checksum: 10/248990cbef9e96fb36a3e1ae6b903c551ca4ddd733f8d0912b9cc5141d3d0b3f9f8dfb4d799fb1c6723382c9c2083ffbfa4ad43ff9a0e7535d32d41fd5f01da6
  languageName: node
  linkType: hard

"pretty-format@npm:^29.0.0, pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10/dea96bc83c83cd91b2bfc55757b6b2747edcaac45b568e46de29deee80742f17bc76fe8898135a70d904f4928eafd8bb693cd1da4896e8bdd3c5e82cadf1d2bb
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10/1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10/c52536521a4d21eff4f2f2aa4572446cad227464066365a7167e52ccf8d9839c099f9afec1aba0eed3d5a2514b3e79e0b3e7a1dc326b9acde6b75d27ed74b1a9
  languageName: node
  linkType: hard

"prop-types@npm:^15.0.0, prop-types@npm:^15.5.7, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10/7d959caec002bc964c86cdc461ec93108b27337dabe6192fb97d69e16a0c799a03462713868b40749bfc1caf5f57ef80ac3e4ffad3effa636ee667582a75e2c0
  languageName: node
  linkType: hard

"property-information@npm:^6.0.0":
  version: 6.5.0
  resolution: "property-information@npm:6.5.0"
  checksum: 10/fced94f3a09bf651ad1824d1bdc8980428e3e480e6d01e98df6babe2cc9d45a1c52eee9a7736d2006958f9b394eb5964dedd37e23038086ddc143fc2fd5e426c
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10/f24a0c80af0e75d31e3451398670d73406ec642914da11a2965b80b1898ca6f66a0e3e091a11a4327079b2b268795f6fa06691923fef91887215c3d0e8ea3f68
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10/f0bb4a87cfd18f77bc2fba23ae49c3b378fb35143af16cc478171c623eebe181678f09439707ad80081d340d1593cd54a33a0113f3ccb3f4bc9451488780ee23
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10/52843fc933b838c0330f588388115a1b28ef2a5ffa7774709b142e35431e8ab0c2edec90de3fa34ebb72d59fef854f151eea7dfc211b6dcf586b384556bd2f39
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 10/256aa4bcaf9297256f552914e03cbdb0039c8fe1db11fa1e6d3f80790e16e563eb0a859a1e61082a95e224fc0c608661839439f8ecc6a3db4e48d46d99216ee4
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10/f548b376e685553d12e461409f0d6e5c59ec7c7d76f308e2a888fd9db3e0c5e89902bedd0754db3a9038eda5f27da2331a6f019c8517dc5e0a16b3c9a6e9cef8
  languageName: node
  linkType: hard

"qs@npm:^6.13.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10/a60e49bbd51c935a8a4759e7505677b122e23bf392d6535b8fc31c1e447acba2c901235ecb192764013cd2781723dc1f61978b5fdd93cc31d7043d31cdc01974
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: 10/a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10/4efd1ad3d88db77c2d16588dc54c2b52fd2461e70fe5724611f38d283857094fe09040fa2c9776366803c3152cf133171b452ef717592b65631ce5dc3a2bdafc
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10/ce21ef2a2dd40506893157970dc76e835c78cf56437e26e19189c48d5291e7279314477b06ac38abd6a401b661a6840f7b03bd0b1249da9b691deeaa15872c26
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10/863b5171e140546a4d99f349b720abac4410338e23df5e409cfcc3752538c9caf947ce382c89129ba976f71894bd38b5806c774edac35ebf168d02aa1ac11a95
  languageName: node
  linkType: hard

"react-dom@npm:^18.3.1, react-dom@npm:~18.3":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.2"
  peerDependencies:
    react: ^18.3.1
  checksum: 10/3f4b73a3aa083091173b29812b10394dd06f4ac06aff410b74702cfb3aa29d7b0ced208aab92d5272919b612e5cda21aeb1d54191848cf6e46e9e354f3541f81
  languageName: node
  linkType: hard

"react-dropzone@npm:14.2.3":
  version: 14.2.3
  resolution: "react-dropzone@npm:14.2.3"
  dependencies:
    attr-accept: "npm:^2.2.2"
    file-selector: "npm:^0.6.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    react: ">= 16.8 || 18.0.0"
  checksum: 10/34cf1758a896795b579adab5f9cdc144330577ab1826a0b66ff9daa8c60a80ed6b31b8f989647664f2548cfe00b336e9c31a2f3dd8de43111c8318fcc89b279c
  languageName: node
  linkType: hard

"react-dropzone@npm:^14.2.3":
  version: 14.3.8
  resolution: "react-dropzone@npm:14.3.8"
  dependencies:
    attr-accept: "npm:^2.2.4"
    file-selector: "npm:^2.1.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    react: ">= 16.8 || 18.0.0"
  checksum: 10/31d5089c2acb4528a279761473a00a1c148b78ae41a6ac49ed09ee94f1d44595437188308ec838debfa0d08082893b170dc072e680aca00469793a05d61b07f2
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.2.0":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 10/a6826180ba75cefba1c8d3ac539735f9b627ca05d3d307fe155487f5d0228d376dac6c9708d04a283a7b9f9aee599b637446635b79c8c8753d0b4eece56c125c
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10/5aa564a1cde7d391ac980bedee21202fc90bdea3b399952117f54fb71a932af1e5902020144fb354b4690b2414a0c7aafe798eb617b76a3d441d956db7726fdf
  languageName: node
  linkType: hard

"react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 10/73b36281e58eeb27c9cc6031301b6ae19ecdc9f18ae2d518bdb39b0ac564e65c5779405d623f1df9abf378a13858b79442480244bd579968afc1faf9a2ce5e05
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10/d5f60c87d285af24b1e1e7eaeb123ec256c3c8bdea7061ab3932e3e14685708221bf234ec50b21e10dd07f008f1b966a2730a0ce4ff67905b3872ff2042aec22
  languageName: node
  linkType: hard

"react-markdown@npm:^8.0.7":
  version: 8.0.7
  resolution: "react-markdown@npm:8.0.7"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/prop-types": "npm:^15.0.0"
    "@types/unist": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-whitespace: "npm:^2.0.0"
    prop-types: "npm:^15.0.0"
    property-information: "npm:^6.0.0"
    react-is: "npm:^18.0.0"
    remark-parse: "npm:^10.0.0"
    remark-rehype: "npm:^10.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-object: "npm:^0.4.0"
    unified: "npm:^10.0.0"
    unist-util-visit: "npm:^4.0.0"
    vfile: "npm:^5.0.0"
  peerDependencies:
    "@types/react": ">=16"
    react: ">=16"
  checksum: 10/5702a2ef0b8a8cb0a085bb5101810d7446e818f7b76291238eff73cce5aaea65b95ffa28f9b4127d1fc785b6cfe0790bba261b11c5a69655ff901399d8ea6896
  languageName: node
  linkType: hard

"react-monaco-editor@npm:^0.56.2":
  version: 0.56.2
  resolution: "react-monaco-editor@npm:0.56.2"
  dependencies:
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ">=16 <= 18"
    monaco-editor: ^0.52.0
    react: ">=16 <= 18"
  checksum: 10/9e891803f766ab81349e1579ffc8cd7cba27eb34ac5113aaf34798f0998629da68415f67e2a0e76057ae632fe019c355eeab89baa607e612239315381777fff4
  languageName: node
  linkType: hard

"react-router-dom@npm:7.5.2":
  version: 7.5.2
  resolution: "react-router-dom@npm:7.5.2"
  dependencies:
    react-router: "npm:7.5.2"
  peerDependencies:
    react: ">=18"
    react-dom: ">=18"
  checksum: 10/4372f9510313a49080c5542f177671e3ac9d958b7fce0968baaa53fc35ab0a38a7db5fee44def16b569e89572ee0cae921462569782d07e00782618cf82169ac
  languageName: node
  linkType: hard

"react-router@npm:7.5.2":
  version: 7.5.2
  resolution: "react-router@npm:7.5.2"
  dependencies:
    cookie: "npm:^1.0.1"
    set-cookie-parser: "npm:^2.6.0"
    turbo-stream: "npm:2.4.0"
  peerDependencies:
    react: ">=18"
    react-dom: ">=18"
  peerDependenciesMeta:
    react-dom:
      optional: true
  checksum: 10/7586ecb7fc17eaac70bf5fe838c94f9f3a2e18e6802a4eddf1ad10b0a51ca83c969f5b36b8e2be47fe46453fea7199f99f422d8554fd159a827c25c287ba5741
  languageName: node
  linkType: hard

"react-split@npm:^2.0.14":
  version: 2.0.14
  resolution: "react-split@npm:2.0.14"
  dependencies:
    prop-types: "npm:^15.5.7"
    split.js: "npm:^1.6.0"
  peerDependencies:
    react: "*"
  checksum: 10/15efbc7ef1161a300daa80e23fc3f51bca1e3ad5deb34a49312d7351c4e0c898b3395d79a169d5fe94a1cd05f3e48bb6a42ee06beb5a41e43c22606a5f15eb59
  languageName: node
  linkType: hard

"react@npm:^18.3.1, react@npm:~18.3":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10/261137d3f3993eaa2368a83110466fc0e558bc2c7f7ae7ca52d94f03aac945f45146bd85e5f481044db1758a1dbb57879e2fcdd33924e2dde1bdc550ce73f7bf
  languageName: node
  linkType: hard

"reactflow@npm:^11.11.4":
  version: 11.11.4
  resolution: "reactflow@npm:11.11.4"
  dependencies:
    "@reactflow/background": "npm:11.3.14"
    "@reactflow/controls": "npm:11.2.14"
    "@reactflow/core": "npm:11.11.4"
    "@reactflow/minimap": "npm:11.7.14"
    "@reactflow/node-resizer": "npm:2.2.14"
    "@reactflow/node-toolbar": "npm:1.3.14"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10/d3322b9971d69762ee4afe5eab16c3d9a2cd362923ad2385d3751ba51f0081a91100c7018597ee0b5eee4b3037ae8ccbded0a27c1bac711252197d8eeaebbe40
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10/8500dd3a90e391d6c5d889256d50ec6026c059fadee98ae9aa9b86757d60ac46fff24fafb7a39fa41d54cb39d8be56cc77be202ebd4cd8ffcf4cb226cbaa40d4
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10/d9e3e53193adcdb79d8f10f2a1f6989bd4389f5936c6f8b870e77570853561c362bee69feca2bbb7b32368ce96a85504aa4cedf7cf80f36e6a9de30d64244048
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10/196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: "npm:^1.20.0"
  checksum: 10/ad3caed8afdefbc33fbc30e6d22b86c35b3d51c2005546f4e79bcc03c074df804b3640ad18945e6bef9ed12caedc035655ec1082f64a5e94c849ff939dc0a788
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: "npm:^4.0.0"
    strip-indent: "npm:^3.0.0"
  checksum: 10/fa1ef20404a2d399235e83cc80bd55a956642e37dd197b4b612ba7327bf87fa32745aeb4a1634b2bab25467164ab4ed9c15be2c307923dd08b0fe7c52431ae6b
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10/9150eae6fe04a8c4f2ff06077396a86a98e224c8afad8344b1b656448e89e84edcd527e4b03aa5476774129eb6ad328ed684f9c1459794a935ec0cc17ce14329
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10/dc6c95ae4b3ba6adbd7687cafac260eee4640318c7a95239d5ce847d9b9263979758389e862fe9c93d633b5792ea4ada5708df75885dc5aa05a309fa18140a87
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10/5db3161abb311eef8c45bcf6565f4f378f785900ed3945acf740a9888c792f75b98ecb77f0775f3bf95502ff423529d23e94f41d80c8256e8fa05ed4b07cf471
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10/4d054ffcd98ca4f6ca7bf0df6598ed5e4a124264602553308add41d4fa714a0c5bcfb5bc868ac91f7060a9c09889cc21d3180a3a14c5f9c5838442806129ced3
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10/b930f03347e4123c917d7b40436b4f87f625b8dd3e705b447ddd44804e4616c3addb7453f0902d6e914ab0446c30e816e445089bb641a4714237fe8141a0ef9d
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10/c2d6506b3308679de5223a8916984198e0493649a67b477c66bdb875357e3785abbf3bedf7c5c2cf8967d3b3a7bdf08b7cbd39e65a70f9e1ffad584aecf5f06a
  languageName: node
  linkType: hard

"relateurl@npm:^0.2.7":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 10/f5d6ba58f2a5d5076389090600c243a0ba7072bcf347490a09e4241e2427ccdb260b4e22cea7be4f1fcd3c2bf05908b1e0d0bc9605e3199d4ecf37af1d5681fa
  languageName: node
  linkType: hard

"remark-parse@npm:^10.0.0":
  version: 10.0.2
  resolution: "remark-parse@npm:10.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    unified: "npm:^10.0.0"
  checksum: 10/184f48956734a58a7e157d83233e532ea289697f5ecebd1fb082cce79e6d9f5b1d3da72462356b2b3b5843643cee890280ffe3d21c9d4ad2d7d5e20bb5de7f14
  languageName: node
  linkType: hard

"remark-rehype@npm:^10.0.0":
  version: 10.1.0
  resolution: "remark-rehype@npm:10.1.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-hast: "npm:^12.1.0"
    unified: "npm:^10.0.0"
  checksum: 10/cf765b639d16872404b50d5945df0ba825d14f1150397dde804e7d9e2e856a7b7343c4dc3796c85e7c18ca84f3c989bd40e476bd194fc00a5a870e8a64ec30d9
  languageName: node
  linkType: hard

"renderkid@npm:^3.0.0":
  version: 3.0.0
  resolution: "renderkid@npm:3.0.0"
  dependencies:
    css-select: "npm:^4.1.3"
    dom-converter: "npm:^0.2.0"
    htmlparser2: "npm:^6.1.0"
    lodash: "npm:^4.17.21"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/434bd56d9930dd344bcba3ef7683f3dd893396b6bc7e8caa551a4cacbe75a9466dc6cf3d75bc324a5979278a73ef968d7854f8f660dbf1a52c38a73f1fb59b20
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10/1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"replace@npm:~1.2":
  version: 1.2.2
  resolution: "replace@npm:1.2.2"
  dependencies:
    chalk: "npm:2.4.2"
    minimatch: "npm:3.0.5"
    yargs: "npm:^15.3.1"
  bin:
    replace: bin/replace.js
    search: bin/search.js
  checksum: 10/275ca1a2cfc12476426420fb9a3dda382450395176b4600fdd76ac7d2876b43af96b4ba82c40e3bb46696d3caa2babef13ea1bbb81371d6dd4416ce4f9f77ffd
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10/839a3a890102a658f4cb3e7b2aa13a1f80a3a976b512020c3d1efc418491c48a886b6e481ea56afc6c4cb5eef678f23b2a4e70575e7534eccadf5e30ed2e56eb
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10/8604a570c06a69c9d939275becc33a65676529e1c3e5a9f42d58471674df79357872b96d70bb93a0380a62d60dc9031c98b1a9dad98c946ffdd61b7ac0c8cedd
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: 10/878880ee78ccdce372784f62f52a272048e2d0827c29ae31e7f99da18b62a2b9463ea03a75f277352f4697c100183debb0532371ad515a2d49d4bfe596dd4c20
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: 10/744e87888f0b6fa0b256ab454ca0b9c0b80808715e2ef1f3672773665c92a941f6181194e30ccae4a8cd0adbe0d955d3f133102636d2ee0cca0119fec0bc9aec
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: "npm:^5.0.0"
  checksum: 10/546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10/be18a5e4d76dd711778664829841cde690971d02b6cbae277735a09c1c28f407b99ef6ef3cd585a1e6546d4097b28df40ed32c4a287b9699dcf6d7f208495e23
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10/536efee0f30a10fac8604e6cdc7844dbc3f4313568d09f06db4f7ed8a5b8aeb8585966fe975083d1f2dfbc87cf5f8bc7ab65a5c23385c14acbb535ca79f8398a
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.20.0":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/0a398b44da5c05e6e421d70108822c327675febb880eebe905587628de401854c61d5df02866ff34fc4cb1173a51c9f0e84a94702738df3611a62e2acdc68181
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/d4d878bfe3702d215ea23e75e0e9caf99468e3db76f5ca100d27ebdc527366fee3877e54bce7d47cc72ca8952fc2782a070d238bfa79a550eeb0082384c3b81a
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: "npm:^2.0.0"
  checksum: 10/b122535466e9c97b55e69c7f18e2be0ce3823c5d47ee8de0d9c0b114aa55741c6db8bfbfce3766a94d1272e61bfb1ebf0a15e9310ac5629fbb7446a861b4fd3a
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10/6125ec2e06d6e47e9201539c887defba4e47f63471db304c59e4b82fc63c8e89ca06a77e9d34939a9a42a76f00774b2f46c0d4a4cbb3e287268bd018ed69426d
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10/af47851b547e8a8dc89af144fceee17b80d5beaf5e6f57ed086432d79943434ff67ca526e92275be6f54b6189f6920a24eace75c2657eed32d02c400312b21ec
  languageName: node
  linkType: hard

"run-applescript@npm:^7.0.0":
  version: 7.0.0
  resolution: "run-applescript@npm:7.0.0"
  checksum: 10/b02462454d8b182ad4117e5d4626e9e6782eb2072925c9fac582170b0627ae3c1ea92ee9b2df7daf84b5e9ffe14eb1cf5fb70bc44b15c8a0bfcdb47987e2410c
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"sade@npm:^1.7.3":
  version: 1.8.1
  resolution: "sade@npm:1.8.1"
  dependencies:
    mri: "npm:^1.1.0"
  checksum: 10/1c67ba03c94083e0ae307ff5564ecb86c2104c0f558042fdaa40ea0054f91a63a9783f14069870f2f784336adabb70f90f22a84dc457b5a25e859aaadefe0910
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10/7eb5b48f2ed9a594a4795677d5a150faa7eb54483b2318b568dc0c4fc94092a6cce5be02c7288a0500a156282f5276d5688bce7259299568d1053b2150ef374a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10/e8d68b89d18d5b028223edf090092846868a765a591944760942b77ea1f69b17235f7e956696efbb62c8130ab90af7e0949bfb8eba7896335507317236966bc9
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0, schema-utils@npm:^4.2.0, schema-utils@npm:^4.3.0, schema-utils@npm:^4.3.2":
  version: 4.3.2
  resolution: "schema-utils@npm:4.3.2"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10/02c32c34aae762d48468f98465a96a167fede637772871c7c7d8923671ddb9f20b2cc6f6e8448ae6bef5363e3597493c655212c8b06a4ee73aa099d9452fbd8b
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: 10/08cdd629a394d20e9005e7956f0624307c702cf950cc0458953e9b87ea961d3b1b72ac02266bdb93ac1eec4fcf42b41db9cabe93aa2b7683d71513d133c44fb5
  languageName: node
  linkType: hard

"selfsigned@npm:^2.4.1":
  version: 2.4.1
  resolution: "selfsigned@npm:2.4.1"
  dependencies:
    "@types/node-forge": "npm:^1.3.0"
    node-forge: "npm:^1"
  checksum: 10/52536623f1cfdeb2f8b9198377f2ce7931c677ea69421238d1dc1ea2983bbe258e56c19e7d1af87035cad7270f19b7e996eaab1212e724d887722502f68e17f2
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10/fca14418a174d4b4ef1fecb32c5941e3412d52a4d3d85165924ce3a47fbc7073372c26faf7484ceb4bbc2bde25880c6b97e492473dc7e9708fdfb1c6a02d546e
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.1.2, semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.5.4":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10/1f6064dea0ae4cbe4878437aedc9270c33f2a6650a77b56a16b62d057527f2766d96ee282997dd53ec0339082f2aad935bc7d989b46b48c82fc610800dc3a1d0
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10/445a420a6fa2eaee4b70cbd884d538e259ab278200a2ededd73253ada17d5d48e91fb1f4cd224a236ab62ea7ba0a70c6af29fc93b4f3d3078bf7da1c031fde58
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: "npm:~1.3.4"
    batch: "npm:0.6.1"
    debug: "npm:2.6.9"
    escape-html: "npm:~1.0.3"
    http-errors: "npm:~1.6.2"
    mime-types: "npm:~2.1.17"
    parseurl: "npm:~1.3.2"
  checksum: 10/2adce2878d7e30f197e66f30e39f4a404d9ae39295c0c13849bb25e7cf976b93e883204739efd1510559588bed56f8101e32191cbe75f374c6e1e803852194cb
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10/7fa9d9c68090f6289976b34fc13c50ac8cd7f16ae6bce08d16459300f7fc61fbc2d7ebfa02884c073ec9d6ab9e7e704c89561882bbe338e99fcacb2912fde737
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10/8980ebf7ae9eb945bb036b6e283c547ee783a1ad557a82babf758a065e2fb6ea337fd82cac30dd565c1e606e423f30024a19fff7afbf4977d784720c4026a8ef
  languageName: node
  linkType: hard

"set-cookie-parser@npm:^2.6.0":
  version: 2.7.1
  resolution: "set-cookie-parser@npm:2.7.1"
  checksum: 10/c92b1130032693342bca13ea1b1bc93967ab37deec4387fcd8c2a843c0ef2fd9a9f3df25aea5bb3976cd05a91c2cf4632dd6164d6e1814208fb7d7e14edd42b4
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 10/02d2564e02a260551bab3ec95358dcfde775fe61272b1b7c488de3676a4bb79f280b5668a324aebe0ec73f0d8ba408bc2d816a609ee5d93b1a7936b9d4ba1208
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10/fde1630422502fbbc19e6844346778f99d449986b2f9cdcceb8326730d2f3d9964dbcb03c02aaadaefffecd0f2c063315ebea8b3ad895914bf1afc1747fc172e
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10/e066bd540cfec5e1b0f78134853e0d892d1c8945fb9a926a579946052e7cb0c70ca4fc34f875a8083aa7910d751805d36ae64af250a6de6f3d28f9fa7be6c21b
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.8.1":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 10/5473e354637c2bd698911224129c9a8961697486cff1fb221f234d71c153fc377674029b0223d1d3c953a68d451d79366abfe53d1a0b46ee1f28eb9ade928f4c
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10/a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10/aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 10/512d4350735375bd11647233cb0e2f93beca6f53441015eea241fe784d8068281c3987fbaa93e7ef1c38df68d9c60013045c92837423c69115297d6169aa85e6
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10/94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slash@npm:^5.1.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 10/2c41ec6fb1414cd9bba0fa6b1dd00e8be739e3fe85d079c69d4b09ca5f2f86eafd18d9ce611c0c0f686428638a36c272a6ac14799146a8295f259c10cc45cde4
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.24":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: "npm:^0.11.3"
    uuid: "npm:^8.3.2"
    websocket-driver: "npm:^0.7.4"
  checksum: 10/36312ec9772a0e536b69b72e9d1c76bd3d6ecf885c5d8fd6e59811485c916b8ce75f46ec57532f436975815ee14aa9a0e22ae3d9e5c0b18ea37b56d0aaaf439c
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/0109090ec2bcb8d12d3875a987e85539ed08697500ad971a603c3057e4c266b4bf6a603e07af6d19218c422dd9b72d923aaa6c1f20abae275510bba458e4ccc9
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10/d1514a922ac9c7e4786037eeff6c3322f461cd25da34bb9fefb15387b3490531774e6e31d95ab6d5b84a3e139af9c3a570ccaee6b47bd7ea262691ed3a8bc34e
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10/8317e12d84019b31e34b86d483dd41d6f832f389f7417faf8fc5c75a66a12d9686e47f589a0554a868b8482f037e23df9d040d29387eb16fa14cb85f091ba207
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10/59ef7462f1c29d502b3057e822cdbdae0b0e565302c4dd1a95e11e793d8d9d62006cdc10e0fd99163ca33ff2071360cf50ee13f90440806e7ed57d81cba2f7ff
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10/202e97d7ca1ba0758a0aa4fe226ff98142073bcceeff2da3aad037968878552c3bbce3b3231970025375bbba5aee00c5b8206eda408da837ab2dc9c0f26be990
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: "npm:^4.1.0"
    detect-node: "npm:^2.0.4"
    hpack.js: "npm:^2.1.6"
    obuf: "npm:^1.1.2"
    readable-stream: "npm:^3.0.6"
    wbuf: "npm:^1.7.3"
  checksum: 10/b93b606b209ca785456bd850b8925f21a76522ee5b46701235ecff3eba17686560c27575f91863842dc843a39772f6d2f5a8755df9eaff0924d20598df18828d
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: "npm:^4.1.0"
    handle-thing: "npm:^2.0.0"
    http-deceiver: "npm:^1.2.7"
    select-hose: "npm:^2.0.0"
    spdy-transport: "npm:^3.0.0"
  checksum: 10/d29b89e48e7d762e505a2f83b1bc2c92268bd518f1b411864ab42a9e032e387d10467bbce0d8dbf8647bf4914a063aa1d303dff85e248f7a57f81a7b18ac34ef
  languageName: node
  linkType: hard

"split.js@npm:^1.6.0":
  version: 1.6.5
  resolution: "split.js@npm:1.6.5"
  checksum: 10/9597f317e26bd040f20d5221e6b08dd5b5195b8014d5482a149a5f4a49033765257b1fa34e31eb5372fc738f1d8c43e8715a5c08bce1b14cbd9346cc31c20cbd
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10/c34828732ab8509c2741e5fd1af6b767c3daf2c642f267788f933a65b1614943c282e74c4284f4fa749c264b18ee016a0d37a3e5b73aee446da46277d3a85daa
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10/cdc988acbc99075b4b036ac6014e5f1e9afa7e564482b687da6384eee6a1909d7eaffde85b0a17ffbe186c5247faf6c2b7544e802109f63b72c7be69b13151bb
  languageName: node
  linkType: hard

"state-local@npm:^1.0.6":
  version: 1.0.7
  resolution: "state-local@npm:1.0.7"
  checksum: 10/1d956043e270861d40a639ff3457938cf61dbc7e25209d21b55060d8dfaf74742b8a1e525ed6fcb0c2d89b7d3e305bb8589bf27392012889456b3ad82a4b7d0a
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10/18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10/c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: "npm:^1.0.2"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/ce85533ef5113fcb7e522bcf9e62cb33871aa99b3729cec5595f4447f660b0cefd542ca6df4150c97a677d58b0cb727a3fe09ac1de94071d05526c73579bf505
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10/54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10/7c41c17ed4dea105231f6df208002ebddd732e8e9e2d619d133cecd8e0087ddfd9587d2feb3c8caf3213cbd841ada6d057f5142cae68a4e62d3540778d9819b4
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10/9dbcfbaf503c57c06af15fe2c8176fb1bf3af5ff65003851a102749f875a6dbe0ab3b30115eccf6e805e9d756830d3e40ec508b62b3f1ddf3761a20ebe29d3f3
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10/69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: "npm:^1.0.0"
  checksum: 10/18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"style-loader@npm:~4.0":
  version: 4.0.0
  resolution: "style-loader@npm:4.0.0"
  peerDependencies:
    webpack: ^5.27.0
  checksum: 10/93f25b7e70cfca9d1d8427170384262b59a5b0e84e7191a5a26636a77799caeed46d9a3e45ee7b9afa0f69176e3b98d5a6c5e81593ff1fd0946f1c5682fd2a68
  languageName: node
  linkType: hard

"style-to-object@npm:^0.4.0":
  version: 0.4.4
  resolution: "style-to-object@npm:0.4.4"
  dependencies:
    inline-style-parser: "npm:0.1.1"
  checksum: 10/3101c0de5325e8051c3665125468af73578eba4712b818458b9f7ed732d7800f3b34e088e5c16f60070644db25316fa5a5b8b69e7f3414c879401eb074a2211e
  languageName: node
  linkType: hard

"superstruct@npm:^2.0.2":
  version: 2.0.2
  resolution: "superstruct@npm:2.0.2"
  checksum: 10/10e1944a9da4baee187fbaa6c5d97d7af266b55786dfe50bce67f0f1e7d93f1a5a42dd51e245a2e16404f8336d07c21c67f1c1fbc4ad0a252d3d2601d6c926da
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10/5f505c6fa3c6e05873b43af096ddeb22159831597649881aeb8572d6fe3b81e798cc10840d0c9735e0026b250368851b7f77b65e84f4e4daa820a4f69947f55b
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/157b534df88e39c5518c5e78c35580c1eca848d7dbaf31bbe06cdfc048e22c7ff1a9d046ae17b25691128f631a51d9ec373c1b740c12ae4f0de6e292037e4282
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10/a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"swc-loader@npm:~0.2":
  version: 0.2.6
  resolution: "swc-loader@npm:0.2.6"
  dependencies:
    "@swc/counter": "npm:^0.1.3"
  peerDependencies:
    "@swc/core": ^1.2.147
    webpack: ">=2"
  checksum: 10/fe90948c02a51bb8ffcff1ce3590e01dc12860b0bb7c9e22052b14fa846ed437781ae265614a5e14344bea22001108780f00a6e350e28c0b3499bc4cd11335fb
  languageName: node
  linkType: hard

"tabbable@npm:^6.2.0":
  version: 6.2.0
  resolution: "tabbable@npm:6.2.0"
  checksum: 10/980fa73476026e99dcacfc0d6e000d41d42c8e670faf4682496d30c625495e412c4369694f2a15cf1e5252d22de3c396f2b62edbe8d60b5dadc40d09e3f2dde3
  languageName: node
  linkType: hard

"tapable@npm:^2.0.0, tapable@npm:^2.1.1, tapable@npm:^2.2.0, tapable@npm:^2.2.1":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 10/065a0dc44aba1b32020faa1c27c719e8f76e5345347515d8494bf158524f36e9f22ad9eaa5b5494f9d5d67bf0640afdd5698505948c46d720b6b7e69d19349a6
  languageName: node
  linkType: hard

"tar@npm:^6.0.5":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10/bfbfbb2861888077fc1130b84029cdc2721efb93d1d1fb80f22a7ac3a98ec6f8972f29e564103bbebf5e97be67ebc356d37fa48dbc4960600a1eb7230fbd1ea0
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.11":
  version: 5.3.14
  resolution: "terser-webpack-plugin@npm:5.3.14"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jest-worker: "npm:^27.4.5"
    schema-utils: "npm:^4.3.0"
    serialize-javascript: "npm:^6.0.2"
    terser: "npm:^5.31.1"
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 10/5b7290f7edb179b83cefb8827c12371ddddc088cf251cf58a1c738d82628331ae6604273b61fe991d77411d4bb6b7178c3826aa47edf01b4ee21f973d6c8b8fb
  languageName: node
  linkType: hard

"terser@npm:^5.10.0, terser@npm:^5.31.1":
  version: 5.42.0
  resolution: "terser@npm:5.42.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10/c2375f84c36dd908699c0c46ed38b47efc0eaaf824dbd579f55f776b7d65168a162c10adbb0638bd0d4517e05a578af038a5d3d25a5acd033c6652fa6d100be9
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^7.1.4"
    minimatch: "npm:^3.0.4"
  checksum: 10/8fccb2cb6c8fcb6bb4115394feb833f8b6cf4b9503ec2485c2c90febf435cac62abe882a0c5c51a37b9bbe70640cdd05acf5f45e486ac4583389f4b0855f69e5
  languageName: node
  linkType: hard

"thingies@npm:^1.20.0":
  version: 1.21.0
  resolution: "thingies@npm:1.21.0"
  peerDependencies:
    tslib: ^2
  checksum: 10/5c3954b67391d1432c252cb7089f29480e2164f06987a63d83c9747aa6999bfc313d6edfce71ed967316a3378dfcaf38f35ea77aaa5d423edaf776b8ff854f83
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 10/825e3bd07ab3c9fd6f753c457a60957c628cacba5dd0656fd93b037c445e2828b43cf0805a9f2b16b0c5f5a10fd561206271acddb568df4f867f0aea0eb2772f
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"tinylogic@npm:^2.0.0":
  version: 2.0.0
  resolution: "tinylogic@npm:2.0.0"
  checksum: 10/6467b1ed9b602dae035726ee3faf2682bddffb5389b42fdb4daf13878037420ed9981a572ca7db467bd26c4ab00fb4eefe654f24e35984ec017fb5e83081db97
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10/cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10/952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tree-dump@npm:^1.0.1":
  version: 1.0.3
  resolution: "tree-dump@npm:1.0.3"
  peerDependencies:
    tslib: 2
  checksum: 10/cf382e61cfb5e3ff8f03425b5bc1923e8f0e385b3a02f43d9d0a32d09da9984477e0f2a7698628662263d1d3f1af17e33486c77ff454978f0f9f07fb5d1fe9a2
  languageName: node
  linkType: hard

"treeify@npm:^1.1.0":
  version: 1.1.0
  resolution: "treeify@npm:1.1.0"
  checksum: 10/5241976a751168fb9894a12d031299f1f6337b7f2cbd3eff22ee86e6777620352a69a1cab0d4709251317ff307eeda0dc45918850974fc44f4c7fc50e623b990
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10/7a1325e4ce8ff7e9e52007600e9c9862a166d0db1f1cf0c9357e359e410acab1278fcd91cc279dfa5123fc37b69f080de02f471e91dbbc61b155b9ca92597929
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 10/999c1cb3db6ec63e1663f911146a90125065da37f66ba342b031d53edb22a62f56c1f934bbc61a55b2b29dd74207544cfd78875b414665c1ffadcd9a9a009eeb
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.0.3, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:^2.7.0, tslib@npm:~2.8":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"turbo-stream@npm:2.4.0":
  version: 2.4.0
  resolution: "turbo-stream@npm:2.4.0"
  checksum: 10/7079bbc82b58340f783144cd669cc7e598288523103a8d68bb8a4c6bb28c64eccb71d389b33aab07788d3a9030638b795709e15cb8486f722b1cdac59cb58afc
  languageName: node
  linkType: hard

"typanion@npm:^3.8.0":
  version: 3.14.0
  resolution: "typanion@npm:3.14.0"
  checksum: 10/5e88d9e6121ff0ec543f572152fdd1b70e9cca35406d79013ec8e08defa8ef96de5fec9e98da3afbd1eb4426b9e8e8fe423163d0b482e34a40103cab1ef29abd
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10/5179e3b8ebc51fce1b13efb75fdea4595484433f9683bbc2dca6d99789dba4e602ab7922d2656f2ce8383987467f7770131d4a7f06a26287db0615d2f4c4ce7d
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10/f4254070d9c3d83a6e573bcb95173008d73474ceadbbf620dd32d273940ca18734dff39c2b2480282df9afe5d1675ebed5499a00d791758748ea81f61a38961f
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10/0bd9eeae5efd27d98fd63519f999908c009e148039d8e7179a074f105362d4fcc214c38b24f6cda79c87e563cbd12083a4691381ed28559220d4a10c2047bed4
  languageName: node
  linkType: hard

"typescript@npm:~5.5":
  version: 5.5.4
  resolution: "typescript@npm:5.5.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/1689ccafef894825481fc3d856b4834ba3cc185a9c2878f3c76a9a1ef81af04194849840f3c69e7961e2312771471bb3b460ca92561e1d87599b26c37d0ffb6f
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~5.5#optional!builtin<compat/typescript>":
  version: 5.5.4
  resolution: "typescript@patch:typescript@npm%3A5.5.4#optional!builtin<compat/typescript>::version=5.5.4&hash=379a07"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/746fdd0865c5ce4f15e494c57ede03a9e12ede59cfdb40da3a281807853fe63b00ef1c912d7222143499aa82f18b8b472baa1830df8804746d09b55f6cf5b1cc
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10/0097779d94bc0fd26f0418b3a05472410408877279141ded2bd449167be1aed7ea5b76f756562cb3586a07f251b90799bab22d9019ceba49c037c76445f7cddd
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10/ec8f41aa4359d50f9b59fa61fe3efce3477cc681908c8f84354d8567bb3701fafdddf36ef6bff307024d3feb42c837cf6f670314ba37fc8145e219560e473d14
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 10/fcff3fbab234f067fbd69e374ee2c198ba74c364ceaf6d93db7ca267e784457b5518cd01d0d2329b075f412574205ea3172a9a675facb49b4c9efb7141cd80b7
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10/3c3dabdb1d22aef4904399f9e810d0b71c0b12b3815169d96fac97e56d5642840c6071cf709adcace2252bc6bb80242396c2ec74b37224eb015c5f7aca40bad7
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10/1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10/9fd53c657aefe5d3cb8208931b4c34fbdb30bb5aa9a6c6bf744e2f3036f00b8889eeaf30cb55a873b76b6ee8b5801ea770e1c49b3352141309f58f0ebb3011d8
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10/243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.3.0":
  version: 0.3.0
  resolution: "unicorn-magic@npm:0.3.0"
  checksum: 10/bdd7d7c522f9456f32a0b77af23f8854f9a7db846088c3868ec213f9550683ab6a2bdf3803577eacbafddb4e06900974385841ccb75338d17346ccef45f9cb01
  languageName: node
  linkType: hard

"unified@npm:^10.0.0":
  version: 10.1.2
  resolution: "unified@npm:10.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    bail: "npm:^2.0.0"
    extend: "npm:^3.0.0"
    is-buffer: "npm:^2.0.0"
    is-plain-obj: "npm:^4.0.0"
    trough: "npm:^2.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10/6cffebcefc3290be26d25a58ba714cda943142782baf320fddf374ca3a319bdaabb006f96df4be17b8b367f5e6f6e113b1027c52ef66154846a7a110550f6688
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"unist-util-generated@npm:^2.0.0":
  version: 2.0.1
  resolution: "unist-util-generated@npm:2.0.1"
  checksum: 10/0528642918683f1518ab7a50cf8c900df10d8717b58bd2fb05aab29393b1c4050fd2740792f18d477b52f942bfb0e6e00023e985c0a7bd63859d3d836b56e4ce
  languageName: node
  linkType: hard

"unist-util-is@npm:^5.0.0":
  version: 5.2.1
  resolution: "unist-util-is@npm:5.2.1"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10/c10f6c07aad4f4830ffa8ea82b42a2c8d5cd36c7555e27889e5fee953040af321e4e6f4e52c4edb606604de75d7230a5f4bc7b71b8ac3e874a26ab595c2057e4
  languageName: node
  linkType: hard

"unist-util-position@npm:^4.0.0":
  version: 4.0.4
  resolution: "unist-util-position@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10/aedbc5d112cdab85b752a7dacd8f04233655f00e08948a42f6e49682467c6fc0c531c91acc71188da5ac8acfea9e67d72bc054127d1c4b76b31792cfb5132423
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^3.0.0":
  version: 3.0.3
  resolution: "unist-util-stringify-position@npm:3.0.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10/07913e4fd77fe57d95f8b2f771354f97a29082229c1ad14ceedce6bbc77b2d784ca8296563335471cdca97915e548204bd6f098ea5b808b822b4b54087662cfb
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^5.1.1":
  version: 5.1.3
  resolution: "unist-util-visit-parents@npm:5.1.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10/5381fc57a129d478d983b988d86b72a1266d6f91fc608562b00bfa76596128d6e4d1c2b26ced64d96e55eb5d27d620081b4ee9703979bab63e1210789e781372
  languageName: node
  linkType: hard

"unist-util-visit@npm:^4.0.0":
  version: 4.1.2
  resolution: "unist-util-visit@npm:4.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^5.1.1"
  checksum: 10/e3b20c6b1f5ae1b7b40bbf9be49103a342d98fad98bdf958110c20d72e5923bd3f12966b6702459bc61ab832facb5af418a79af87cefa7a8a41b892369678b13
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10/4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/87af2776054ffb9194cf95e0201547d041f72ee44ce54b144da110e65ea7ca01379367407ba21de5c9edd52c74d95395366790de67f3eb4cc4afa0fe4424e76f
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.2.2":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/ddae7c4572511f7f641d6977bd0725340aa7dbeda8250418b54c1a57ec285083d96cf50d1a1acbd6cf729f7a87071b2302c6fbd29310432bf1b21a961a313279
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 10/b068d8cb140588da0d0c80ee3c14c6b75d3f68760d8a1c6c3908d0270e9e4056454ff16189586481b7382926c44674f6929d08e06eaf9ec8f62736cd900169c5
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10/5d6949693d58cb2e636a84f3ee1c6e7b2f9c16cb1d42d0ecb386d8c025c69e327205aa1c69e2868cc06a01e5e20681fbba55a4e0ed0cce913d60334024eae798
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10/9a5f7aa1d6f56dd1e8d5f2478f855f25c645e64e26e347a98e98d95781d5ed20062d6cca2eecb58ba7c84bc3910be95c0451ef4161906abaab44f9cb68ffbdd1
  languageName: node
  linkType: hard

"uvu@npm:^0.5.0":
  version: 0.5.6
  resolution: "uvu@npm:0.5.6"
  dependencies:
    dequal: "npm:^2.0.0"
    diff: "npm:^5.0.0"
    kleur: "npm:^4.0.3"
    sade: "npm:^1.7.3"
  bin:
    uvu: bin.js
  checksum: 10/66ba25afc6732249877f9f4f8b6146f3aaa97538c51cf498f55825d602c33dbb903e02c7e1547cbca6bdfbb609e07eb7ea758b5156002ac2dd5072f00606f8d9
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10/fb1d70f1176cb9dc46cabbb3fd5c52c8f3e8738b61877b6e7266029aed0870b04140e3f9f4550ac32aebcfe1d0f38b0bac57e1e8fb97d68fec82f2b416148166
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10/31389debef15a480849b8331b220782230b9815a8e0dbb7b9a8369559aed2e9a7800cd904d4371ea74f4c3527db456dc8e7ac5befce5f0d289014dbdf47b2242
  languageName: node
  linkType: hard

"vfile-message@npm:^3.0.0":
  version: 3.1.4
  resolution: "vfile-message@npm:3.1.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
  checksum: 10/423ca87f4427a403e4688d7ec663a2e6add694eefac47c945746463377428c7553bc613058841f1da83e18b68af886d3dd11cb96d582b5cc3c98e11efb7e55e9
  languageName: node
  linkType: hard

"vfile@npm:^5.0.0":
  version: 5.3.7
  resolution: "vfile@npm:5.3.7"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    is-buffer: "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10/d8f59b419d4c83b3ed24f500cf02393149b728f8803f88519c18fe0733f62544fa9ab0d8425a8bc7835181d848b9ce29c014168dc45af72f416074bbe475f643
  languageName: node
  linkType: hard

"victory-area@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-area@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/ee15fe7c88647ea277c1752cedb8b736cc82cef4a66f4173307686cdc0c221b14924f5c9ac362eab62239781b6eae6c6c96100719c7f320b4db7b9fb79f0daa3
  languageName: node
  linkType: hard

"victory-area@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-area@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/7cb1c7af37a499e339de8fcc4a9f51571e3e433b9ae1d6f9d4f56a3452c6a1d9314f53ad12555833fc1d6dd497a68d10c8b1e5dce134ec1cd41ad032eb4c8fc5
  languageName: node
  linkType: hard

"victory-axis@npm:37.3.6, victory-axis@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-axis@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/0ea69ba4f1a7b2d073c2b6862f439fffc64a813c84288946225ff322a1b4f7f65a3fde6665b4987ec4b6b534902d23e53cb0f78abd6935e4422c1ec66ab87a91
  languageName: node
  linkType: hard

"victory-axis@npm:^36.9.1, victory-axis@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-axis@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/59db2f927e058525646a01ba2d780818a5c3d2960338a638675f43d63e514832363de971a22a8cc1419f448852ea6a4344206dc7395fbf12b5ba407c185130a2
  languageName: node
  linkType: hard

"victory-bar@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-bar@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/e1b5b03056ab42854c6efbbdf62b57c7bb826385fd5bbf4e31cf3320eb5d0c1c0d6d1a476f2aba7816d5ec0c1d67e1b4621d43a2627d8a3c510ddb01bfc2929f
  languageName: node
  linkType: hard

"victory-bar@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-bar@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/c90098465d8ff24d0c27b2454b016233a85ffbd8f5725e9a1c38659a0b32a22e4e49ccf95953dd2065c8f4ecd914c5a547b5013dea3b87ec9fd9b2bea671f329
  languageName: node
  linkType: hard

"victory-box-plot@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-box-plot@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/f4251dbb39acc0d428ecf0ab49f4bb600e19f5c7843bcbe75f053878e6411f2f5d8b3e3bfea681eb835a0572a2b39f5f87a982c422d29a89475bd9f06137ed76
  languageName: node
  linkType: hard

"victory-box-plot@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-box-plot@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/f228619994e0dc42a3e1c5628a6e07942ee236bd8fd9398c2095dc076f90c6e3a61754cc0c49f4ac341e3886ed140132d0afe6c74a84aa46fdb4c7fbff6feac4
  languageName: node
  linkType: hard

"victory-brush-container@npm:37.3.6":
  version: 37.3.6
  resolution: "victory-brush-container@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/e8caf58ab796ca001f2e409301172a2c09fc77177313afd698030229af160d3e99f50f54e891361ed3f322209512a7651ed23d2600adf81ba001e8c852e61a6b
  languageName: node
  linkType: hard

"victory-brush-container@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-brush-container@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/5b7e650bc71e9148902d0e1d7a60c28a286349099dfecbb470d196105675d9f77fc059906b8e459a293daaa6c1bb79c27485e4c9641646e78f178f37c8301ca3
  languageName: node
  linkType: hard

"victory-chart@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-chart@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-axis: "npm:^36.9.2"
    victory-core: "npm:^36.9.2"
    victory-polar-axis: "npm:^36.9.2"
    victory-shared-events: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/6384113befed410ec3c7f5f538abc111333e2829262601da4ac537087f4281d920c783afc9bc294eab600ecbd34387e5b31c13f0f53315374999c4023a10d4f4
  languageName: node
  linkType: hard

"victory-chart@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-chart@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-axis: "npm:37.3.6"
    victory-core: "npm:37.3.6"
    victory-polar-axis: "npm:37.3.6"
    victory-shared-events: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/218d6a5b0c83da0994b2812d7527bbcc36f455ea7712692c1aa12aedef201b1697467c86b0c0c723e6ae5cf2a4be06a1648bf9eb43bf466c419e69d435336a12
  languageName: node
  linkType: hard

"victory-core@npm:37.3.6, victory-core@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-core@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.21"
    react-fast-compare: "npm:^3.2.0"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/0e88454b10b6536dbade6cf90d21f66c31b8caa732d88eaebe41a4c54c96821f1914159613544c0bebe66af03d0e1c3a0d134f4eb157d6310282a109fdb8e4c7
  languageName: node
  linkType: hard

"victory-core@npm:^36.9.1, victory-core@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-core@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.21"
    react-fast-compare: "npm:^3.2.0"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/fa3be4c1411da16236e0ecc17ad33cdfca299f7d6d879b7c3f8ce667cd937a3098d38c0ae12008b2b4d753363977ee859b79a73b081f8478c3bdcde4fd11ac28
  languageName: node
  linkType: hard

"victory-create-container@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-create-container@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-brush-container: "npm:^36.9.2"
    victory-core: "npm:^36.9.2"
    victory-cursor-container: "npm:^36.9.2"
    victory-selection-container: "npm:^36.9.2"
    victory-voronoi-container: "npm:^36.9.2"
    victory-zoom-container: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/1137fbba85b46ef2a5b693fbad4655e1ac7f4ffd37848508d75100b129d9e45b9f7cbde877d38652d0b8609046a5c2ba11fda678b2d8ce77c673683cab6a115d
  languageName: node
  linkType: hard

"victory-create-container@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-create-container@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-brush-container: "npm:37.3.6"
    victory-core: "npm:37.3.6"
    victory-cursor-container: "npm:37.3.6"
    victory-selection-container: "npm:37.3.6"
    victory-voronoi-container: "npm:37.3.6"
    victory-zoom-container: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/513dbd7ab06ec207594c4621bffdaf7b6c3eb4cc35fe119be7ff4a0070033894e84cf85a1d2d27af56c79f8644c08666bfc0c89b2f211db9f3be4f5b6efb2853
  languageName: node
  linkType: hard

"victory-cursor-container@npm:37.3.6, victory-cursor-container@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-cursor-container@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/55ecc0224ae6e4c3beab578d3e294068b447649226bf21b0fb616b3f887dca9d7729ac3ca8cebf2c55faae3648303047b3c05c747075c89721f65e2d4a29c0da
  languageName: node
  linkType: hard

"victory-cursor-container@npm:^36.9.1, victory-cursor-container@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-cursor-container@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/d8b3078096c57aeb517bf14a367b74b5bed95b0f91c60f655604e1f1f4be9b2ec691fd44fadfaa10e49195ac18e5655d131a4891890c37ac006d44c1aa863315
  languageName: node
  linkType: hard

"victory-group@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-group@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:^36.9.2"
    victory-shared-events: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/f0e003b035562a9c5a02dc34506ec644bce3f53176a020ea47bcf925edab394d041cef864b8f1ec407b01fede3030ec184b04701c82733b424a6ca3d036be895
  languageName: node
  linkType: hard

"victory-group@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-group@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:37.3.6"
    victory-shared-events: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/5977f2aba43a908ab9039a5f90c7ba2a95829c1c6dc4b82213ee13a88a7c4ac7cdfdc055204b6697fd0ac8c07cf0dfd57b999538c9cf9f2c94a8912085b48fae
  languageName: node
  linkType: hard

"victory-legend@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-legend@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/6e198a0328e68ea1b49b918211e5c0a81852a6f104286ee6237e16cf6ce1d7c84422e965bfd947b3edd53576248a8075541c83e11dd7ef0a5f57cf7e8b35eee2
  languageName: node
  linkType: hard

"victory-legend@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-legend@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/afbcb4933e4e25fc6bc1988ea8ecb3481f6206c48ffc9b47bf9d173e301ce0738a70cc3b2de97e81c9b41772888f0a5363bfd726cf3f24bc3006e05570ea8495
  languageName: node
  linkType: hard

"victory-line@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-line@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/4f0491c1a715ec4bbaa3e2a28098c300fa4550d6cfd038b8890b67f2b42c143aa84278cb88a7afd6aeb033ac4a1a12af70c7cfd0bd30e2be61d07980b5c50bd8
  languageName: node
  linkType: hard

"victory-line@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-line@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/e4905bfddf31b6c30b439de67dfb716806986017445632e4be0e6bd5a5ac2b6dab15defe4a876b407b10be022e58a7cc8cc542e36f61f60dba818cab68dc1303
  languageName: node
  linkType: hard

"victory-pie@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-pie@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
    victory-vendor: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/052d1fa4e112c5626d5390a817a74ecdc56b9135acf894d12f37ad66812d7e6bd4c13b278a1df1b5418912e2f7c0c93d38ecf3553d9a4f25e3fdb75ea1ec94e6
  languageName: node
  linkType: hard

"victory-pie@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-pie@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
    victory-vendor: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/ce53349a0fec5e4b81e317cd3e1c89f72f3e5be2cad69285eb31dc9685d4a8b46c73772d1d3a007709dd06d63cbf2d3e44111febd55a42fe40b192a138fa4fd4
  languageName: node
  linkType: hard

"victory-polar-axis@npm:37.3.6":
  version: 37.3.6
  resolution: "victory-polar-axis@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/debb4231e5f0fa221b65fb49ae5cf3534a0feffdac88b02ccba07d2068282247aec4e6aa1780066f9b8d5c18f77b85e05d7e5a5335d5ad7fd0cdb2ce4b7e1137
  languageName: node
  linkType: hard

"victory-polar-axis@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-polar-axis@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/6974fb09d035ffa1c2ce222f1092f7c69e54bd5cb947150e27525f5b246bdd32075b4d105843ab6ade4a15be484252c72d6f3c62254ff8cd466cac9a2219f7a5
  languageName: node
  linkType: hard

"victory-scatter@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-scatter@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/135cbb8d8310f4a286d11fea8e36913ad39048269395d691dd8286a9c3a2c8f84377913ceeb17d23d5ce22656fe40e57287f20f36e270e971a41c88d208e7a07
  languageName: node
  linkType: hard

"victory-scatter@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-scatter@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/318813992841fa56506ce826871e59afb2a47c5214253c41ff8ef263ab9e1aefee9fd4c1871b8d1ab60add769f94baab65c7cde0591f27c27c22e231b914619b
  languageName: node
  linkType: hard

"victory-selection-container@npm:37.3.6":
  version: 37.3.6
  resolution: "victory-selection-container@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/e90c36fff9368d6593f3c62cc8fe71fbcdd641f77782b76b860f415b685c4dd384769d81f3d1fe83016d015c3c41d167d1af6e438c9cd2055e4b2e55b95f657e
  languageName: node
  linkType: hard

"victory-selection-container@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-selection-container@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/131943a40522b12f548fc02c30d9bd76cb24d6a9b9a3b179d959cb0beda311096f7e1ca47d4a4f8dd17455270cd20d0161002c9ff9fd6d227570473111693d35
  languageName: node
  linkType: hard

"victory-shared-events@npm:37.3.6":
  version: 37.3.6
  resolution: "victory-shared-events@npm:37.3.6"
  dependencies:
    json-stringify-safe: "npm:^5.0.1"
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/1d18284e8ce0f0724a0c4b0b5c3cb2f3a937b6a06587f3e140426c0690a25414666d7e160b3cadea808d26f4e2d099d62ada24eafa81f2fb6db38e495d82ef56
  languageName: node
  linkType: hard

"victory-shared-events@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-shared-events@npm:36.9.2"
  dependencies:
    json-stringify-safe: "npm:^5.0.1"
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/b478bcc6a9468e17d72400548bc7c8e20dafa19aef317cdf6ef001d4030e57eb4a608d308c396d4d0686c276151b1762b51261da73526be3f286a63e710d5af2
  languageName: node
  linkType: hard

"victory-stack@npm:^36.9.1":
  version: 36.9.2
  resolution: "victory-stack@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:^36.9.2"
    victory-shared-events: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/9405623f94582eef3305f5e5b438546c6e76f40af349221e85cd184541cc0ee3b59a5d76d1a2375a89714ed3dcc708bd8f8ee5bf798a5c574c2e024c99060129
  languageName: node
  linkType: hard

"victory-stack@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-stack@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:37.3.6"
    victory-shared-events: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/1e6920a99965b7c8ee17113c3292318c2d68468cff69b2f5b89ddde48d2a9c0de08659934324b857721290a5bcfe115016aab8dd053b25d4bc004270c9b3b695
  languageName: node
  linkType: hard

"victory-tooltip@npm:37.3.6, victory-tooltip@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-tooltip@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/cccf3228eee710d6eeb0525b72f060a18dbe696c3830bd686672bfaae7207211884b8b21b7fb279be05b36377d793be7d858754b3595a272c5a80a3f69c80089
  languageName: node
  linkType: hard

"victory-tooltip@npm:^36.9.1, victory-tooltip@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-tooltip@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/bb767825defbb89effefa652fcf9dbca4bbbe9d97aeb409b2d2a8914879db09fb1fac43e873746a300531dd5f58429ce3f9d29b9791d7b0be60e4bcdfff1a521
  languageName: node
  linkType: hard

"victory-vendor@npm:37.3.6":
  version: 37.3.6
  resolution: "victory-vendor@npm:37.3.6"
  dependencies:
    "@types/d3-array": "npm:^3.0.3"
    "@types/d3-ease": "npm:^3.0.0"
    "@types/d3-interpolate": "npm:^3.0.1"
    "@types/d3-scale": "npm:^4.0.2"
    "@types/d3-shape": "npm:^3.1.0"
    "@types/d3-time": "npm:^3.0.0"
    "@types/d3-timer": "npm:^3.0.0"
    d3-array: "npm:^3.1.6"
    d3-ease: "npm:^3.0.1"
    d3-interpolate: "npm:^3.0.1"
    d3-scale: "npm:^4.0.2"
    d3-shape: "npm:^3.1.0"
    d3-time: "npm:^3.0.0"
    d3-timer: "npm:^3.0.1"
  checksum: 10/1babc3ada56f182056af6edc10e592534b887116e37cf07b42fa909efd5edf41b7693bfaca3dbdf1a991e43c684549cd15e15a1b1b51a16fa2ee1e828d508bc7
  languageName: node
  linkType: hard

"victory-vendor@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-vendor@npm:36.9.2"
  dependencies:
    "@types/d3-array": "npm:^3.0.3"
    "@types/d3-ease": "npm:^3.0.0"
    "@types/d3-interpolate": "npm:^3.0.1"
    "@types/d3-scale": "npm:^4.0.2"
    "@types/d3-shape": "npm:^3.1.0"
    "@types/d3-time": "npm:^3.0.0"
    "@types/d3-timer": "npm:^3.0.0"
    d3-array: "npm:^3.1.6"
    d3-ease: "npm:^3.0.1"
    d3-interpolate: "npm:^3.0.1"
    d3-scale: "npm:^4.0.2"
    d3-shape: "npm:^3.1.0"
    d3-time: "npm:^3.0.0"
    d3-timer: "npm:^3.0.1"
  checksum: 10/db67b3d9b8070d4eae4122edc72be7067b4e32363340cdd4d5b628e7dd65bea0c7c5b4116016658d223adaa575bcc6b7b3a71507aa4f34b2609ed61dbfbba1ea
  languageName: node
  linkType: hard

"victory-voronoi-container@npm:37.3.6, victory-voronoi-container@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-voronoi-container@npm:37.3.6"
  dependencies:
    delaunay-find: "npm:0.0.6"
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:37.3.6"
    victory-tooltip: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/589a563175cafc16ff574cfd77c1610a0730209641fab276af4a7441db91b50b7ceba3a6f58a8313865bc6d32fe9adb6c7813e38191cc20b7bbc53c3ba6c1f45
  languageName: node
  linkType: hard

"victory-voronoi-container@npm:^36.9.1, victory-voronoi-container@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-voronoi-container@npm:36.9.2"
  dependencies:
    delaunay-find: "npm:0.0.6"
    lodash: "npm:^4.17.19"
    react-fast-compare: "npm:^3.2.0"
    victory-core: "npm:^36.9.2"
    victory-tooltip: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/6091c8ac862941f6f2acaba209a4d24b060e32bf7efa315678e31722edcef948f4974dd2b285ffbfb822e6e57521501563636157b4df2834477862ca99f5139e
  languageName: node
  linkType: hard

"victory-zoom-container@npm:37.3.6, victory-zoom-container@npm:^37.3.6":
  version: 37.3.6
  resolution: "victory-zoom-container@npm:37.3.6"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:37.3.6"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/082d49bdface8bf632a263631137ceec3e105847bcdfafb94af349e68e326061364f71713859424f656cadd71b6b7661f745b4b7eb54c521af3472f40c4657f5
  languageName: node
  linkType: hard

"victory-zoom-container@npm:^36.9.1, victory-zoom-container@npm:^36.9.2":
  version: 36.9.2
  resolution: "victory-zoom-container@npm:36.9.2"
  dependencies:
    lodash: "npm:^4.17.19"
    victory-core: "npm:^36.9.2"
  peerDependencies:
    react: ">=16.6.0"
  checksum: 10/e05a1bd6987014daef479c4c212ecbb923632d48f7e07aa13897721337db460edfb8aee52c70a72614d5871cd76f682c41affe712156e6f2d175ce8928c29cac
  languageName: node
  linkType: hard

"walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10/ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.1":
  version: 2.4.4
  resolution: "watchpack@npm:2.4.4"
  dependencies:
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.1.2"
  checksum: 10/cfa3473fc12a1a1b88123056941e90c462a67aedc10b242229eeeccdd45ed0b763c3b591caaffb0f7d77295b539b5518bb1ad3bcd891ae6505dfeae4cf51fd15
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: "npm:^1.0.0"
  checksum: 10/c18b51c4e1fb19705c94b93c0cf093ba014606abceee949399d56074ef1863bf4897a8d884be24e8d224d18c9ce411cf6924006d0a5430492729af51256e067a
  languageName: node
  linkType: hard

"webpack-cli@npm:~6.0":
  version: 6.0.1
  resolution: "webpack-cli@npm:6.0.1"
  dependencies:
    "@discoveryjs/json-ext": "npm:^0.6.1"
    "@webpack-cli/configtest": "npm:^3.0.1"
    "@webpack-cli/info": "npm:^3.0.1"
    "@webpack-cli/serve": "npm:^3.0.1"
    colorette: "npm:^2.0.14"
    commander: "npm:^12.1.0"
    cross-spawn: "npm:^7.0.3"
    envinfo: "npm:^7.14.0"
    fastest-levenshtein: "npm:^1.0.12"
    import-local: "npm:^3.0.2"
    interpret: "npm:^3.1.1"
    rechoir: "npm:^0.8.0"
    webpack-merge: "npm:^6.0.1"
  peerDependencies:
    webpack: ^5.82.0
  peerDependenciesMeta:
    webpack-bundle-analyzer:
      optional: true
    webpack-dev-server:
      optional: true
  bin:
    webpack-cli: ./bin/cli.js
  checksum: 10/f765a492babed4d2f42eb7a42a895550ad62f8ae56fde087243490c7ed685c6a3c8a280e27603f5b08c5221f4b8189582acd57a8ceea510fe95225e8229a0c51
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^7.4.2":
  version: 7.4.2
  resolution: "webpack-dev-middleware@npm:7.4.2"
  dependencies:
    colorette: "npm:^2.0.10"
    memfs: "npm:^4.6.0"
    mime-types: "npm:^2.1.31"
    on-finished: "npm:^2.4.1"
    range-parser: "npm:^1.2.1"
    schema-utils: "npm:^4.0.0"
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
  checksum: 10/608d101b82081a5bc6c0237f9945e14a8eefce1664c10877f3feb0042710f6c8b4288b07986505f791302d81b3c51180f679b97c91c3cdabd3fd0687a464ca1c
  languageName: node
  linkType: hard

"webpack-dev-server@npm:~5.2":
  version: 5.2.2
  resolution: "webpack-dev-server@npm:5.2.2"
  dependencies:
    "@types/bonjour": "npm:^3.5.13"
    "@types/connect-history-api-fallback": "npm:^1.5.4"
    "@types/express": "npm:^4.17.21"
    "@types/express-serve-static-core": "npm:^4.17.21"
    "@types/serve-index": "npm:^1.9.4"
    "@types/serve-static": "npm:^1.15.5"
    "@types/sockjs": "npm:^0.3.36"
    "@types/ws": "npm:^8.5.10"
    ansi-html-community: "npm:^0.0.8"
    bonjour-service: "npm:^1.2.1"
    chokidar: "npm:^3.6.0"
    colorette: "npm:^2.0.10"
    compression: "npm:^1.7.4"
    connect-history-api-fallback: "npm:^2.0.0"
    express: "npm:^4.21.2"
    graceful-fs: "npm:^4.2.6"
    http-proxy-middleware: "npm:^2.0.9"
    ipaddr.js: "npm:^2.1.0"
    launch-editor: "npm:^2.6.1"
    open: "npm:^10.0.3"
    p-retry: "npm:^6.2.0"
    schema-utils: "npm:^4.2.0"
    selfsigned: "npm:^2.4.1"
    serve-index: "npm:^1.9.1"
    sockjs: "npm:^0.3.24"
    spdy: "npm:^4.0.2"
    webpack-dev-middleware: "npm:^7.4.2"
    ws: "npm:^8.18.0"
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: 10/59517409cd38c01a875a03b9658f3d20d492b5b8bead9ded4a0f3d33e6857daf2d352fe89f0181dcaea6d0fbe84b0494cb4750a87120fe81cdbb3c32b499451c
  languageName: node
  linkType: hard

"webpack-merge@npm:^6.0.1":
  version: 6.0.1
  resolution: "webpack-merge@npm:6.0.1"
  dependencies:
    clone-deep: "npm:^4.0.1"
    flat: "npm:^5.0.2"
    wildcard: "npm:^2.0.1"
  checksum: 10/39ab911c26237922295d9b3d0617c8ea0c438c35a3b21b05506616a10423f5ece1962bccbedec932c5db61af57999b6d055d56d1f1755c63e2701bd4a55c3887
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.3.2
  resolution: "webpack-sources@npm:3.3.2"
  checksum: 10/c3e7f8c387cacad619e80e75c1dfc74bd458a6c744f9fee53220da317d13acb4d8cd56c85666039edb7085761d482c35795a94f2c97d192950c08d054e758714
  languageName: node
  linkType: hard

"webpack@npm:~5.99":
  version: 5.99.9
  resolution: "webpack@npm:5.99.9"
  dependencies:
    "@types/eslint-scope": "npm:^3.7.7"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    "@webassemblyjs/ast": "npm:^1.14.1"
    "@webassemblyjs/wasm-edit": "npm:^1.14.1"
    "@webassemblyjs/wasm-parser": "npm:^1.14.1"
    acorn: "npm:^8.14.0"
    browserslist: "npm:^4.24.0"
    chrome-trace-event: "npm:^1.0.2"
    enhanced-resolve: "npm:^5.17.1"
    es-module-lexer: "npm:^1.2.1"
    eslint-scope: "npm:5.1.1"
    events: "npm:^3.2.0"
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.2.11"
    json-parse-even-better-errors: "npm:^2.3.1"
    loader-runner: "npm:^4.2.0"
    mime-types: "npm:^2.1.27"
    neo-async: "npm:^2.6.2"
    schema-utils: "npm:^4.3.2"
    tapable: "npm:^2.1.1"
    terser-webpack-plugin: "npm:^5.3.11"
    watchpack: "npm:^2.4.1"
    webpack-sources: "npm:^3.2.3"
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 10/cf4a217239bcaa892f93702639ac837a16510edb7a1326955fb042d499d297cbdb16f20a81f3be6ec041b22ab47c599c757e505fdee1dd89b7f7a1ce4337fbf3
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: "npm:>=0.5.1"
    safe-buffer: "npm:>=5.1.0"
    websocket-extensions: "npm:>=0.1.1"
  checksum: 10/17197d265d5812b96c728e70fd6fe7d067471e121669768fe0c7100c939d997ddfc807d371a728556e24fc7238aa9d58e630ea4ff5fd4cfbb40f3d0a240ef32d
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 10/b5399b487d277c78cdd2aef63764b67764aa9899431e3a2fa272c6ad7236a0fb4549b411d89afa76d5afd664c39d62fc19118582dc937e5bb17deb694f42a0d1
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10/1967b7ce17a2485544a4fdd9063599f0f773959cca24176dbe8f405e55472d748b7c549cd7920ff6abb8f1ab7db0b0f1b36de1a21c57a8ff741f4f1e792c52be
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.1":
  version: 2.0.1
  resolution: "wildcard@npm:2.0.1"
  checksum: 10/e0c60a12a219e4b12065d1199802d81c27b841ed6ad6d9d28240980c73ceec6f856771d575af367cbec2982d9ae7838759168b551776577f155044f5a5ba843c
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/0d64f2d438e0b555e693b95aee7b2689a12c3be5ac458192a1ce28f542a6e9e59ddfecc37520910c2c88eb1f82a5411260566dba5064e8f9895e76e169e76187
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10/159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.7"
  checksum: 10/3be1f5508a46c190619d5386b1ac8f3af3dbe951ed0f7b0b4a0961eed6fc626bd84b50cf4be768dabc0a05b672f5d0c5ee7f42daa557b14415d18c3a13c7d246
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/018e04ec95561d88248d53a2eaf094b4ae131e9b062f2679e6e8a62f04649bc543448f1e038125225ac6bbb25f54c1e65d7a2cc9dbc1e28b43e5e6b7162ad88e
  languageName: node
  linkType: hard

"xml-formatter@npm:^3.6.3":
  version: 3.6.6
  resolution: "xml-formatter@npm:3.6.6"
  dependencies:
    xml-parser-xo: "npm:^4.1.4"
  checksum: 10/086bc6ea58b26d0d02900fa7c68436a0ab6f9a841ce2dd353d107ca6156310846e719508e1aef62e5ce40923f8711742064c700b60238d413a12767a90d16cd5
  languageName: node
  linkType: hard

"xml-parser-xo@npm:^4.1.4":
  version: 4.1.4
  resolution: "xml-parser-xo@npm:4.1.4"
  checksum: 10/12b31b03e8250856d3c5d574f48ea11d655384b3edaddd6efc5b9b37607ce0068e3e4c6ec126160be1e98ccc49b6b0c591c972415f5c2c3ee1f6ef13c02b83b6
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10/392870b2a100bbc643bc035fe3a89cef5591b719c7bdc8721bcdb3d27ab39fa4870acdca67b0ee096e146d769f311d68eda6b8195a6d970f227795061923013f
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10/5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10/9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10/235bcbad5b7ca13e5abc54df61d42f230857c6f83223a38e4ed7b824681875b7f8b6ed52139d88a3ad007050f28dc0324b3c805deac7db22ae3b4815dae0e1bf
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10/9dc2c217ea3bf8d858041252d43e074f7166b53f3d010a8c711275e09cd3d62a002969a39858b92bbda2a6a63a585c7127014534a560b9c69ed2d923d113406e
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10/bbcc82222996c0982905b668644ca363eebe6ffd6a572fbb52f0c0e8146661d8ce5af2a7df546968779bb03d1e4186f3ad3d55dfaadd1c4f0d5187c0e3a5ba16
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10/abb3e37678d6e38ea85485ed86ebe0d1e3464c640d7d9069805ea0da12f69d5a32df8e5625e370f9c96dd1c2dc088ab2d0a4dd32af18222ef3c4224a19471576
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zustand@npm:^4.4.1":
  version: 4.5.7
  resolution: "zustand@npm:4.5.7"
  dependencies:
    use-sync-external-store: "npm:^1.2.2"
  peerDependencies:
    "@types/react": ">=16.8"
    immer: ">=9.0.6"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
  checksum: 10/21c47ea1c9bb0363b714a7e371a91b9afaeabc5c9c2f522803a0fb412605b1e037c4f975a7377529de8f2857e60d1f4586e7ade18444168ecc492e38779e605d
  languageName: node
  linkType: hard
