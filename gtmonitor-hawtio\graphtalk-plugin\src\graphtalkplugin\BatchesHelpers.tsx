import React from 'react';
import * as JulianDateTime from './JulianDateTime'
import { Icon } from '@patternfly/react-core'
import {
  CheckCircleIcon,
  TimesIcon,
  ExclamationTriangleIcon,
  ResourcesEmptyIcon,
  ResourcesAlmostEmptyIcon,
  ServiceIcon, RunningIcon,
  ResourcesAlmostFullIcon,
  ResourcesFullIcon
} from "@patternfly/react-icons";
import "./Batches.css";

  export const HEIGHT_SEARCH_EXPANDED = '100%';
  export const HEIGHT_SEARCH_COLLAPSED = '100%';
  export const TOP_TABLE_HEIGHT_EXPANDED = 50;
  export const TOP_TABLE_HEIGHT_COLLAPSED = 50;

  export const batchManagerExecutionsItemsMap = [
    { key: "execution_id",     header: "Id",        type: "int",          width: "40px",    align : 'center',   padding : '0 10px 0 0'  },
    { key: "batch_name",       header: "Name",      type: "string",       width: "250px",   align : 'left',     padding : '0 0 0 5px'  },
    { key: "creation_date",    header: "Date",      type: "julianDate",   width: "90px",    align : 'left',     padding : '0 0 0 5px'  },
    { key: "creation_time",    header: "Time",      type: "julianTime",   width: "90px",    align : 'left',     padding : '0 0 0 5px'  },
    { key: "duration",         header: "Duration",  type: "julianTime",   width: "110px",   align : 'left',     padding : '0 0 0 5px'  },
    { key: "step_counter",     header: "Steps",     type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "error_counter",    header: "Errors",    type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "warning_counter",  header: "Warnings",  type: "string",       width: "110px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "status",           header: "Status",    type: "string",       width: "180px",   align : 'left',     padding : '0 0 0 5px' },
  ];
  
  export const batchCompletedExecutionsItemsMap = [
    { key: "execution_id",     header: "Id",        type: "int",          width: "40px",    align : 'center',   padding : '0 10px 0 0' },
    { key: "batch_name",       header: "Name",      type: "string",       width: "250px",   align : 'left',     padding : '0 0 0 5px' },
    { key: "creation_date",    header: "Finished",  type: "string",       width: "200px",   align : 'center',   padding : '0 0 0 0' },
    { key: "duration",         header: "Duration",  type: "julianTime",   width: "110px",   align : 'center',   padding : '0 0 0 0' },
    { key: "step_counter",     header: "Steps",     type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "error_counter",    header: "Errors",    type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "warning_counter",  header: "Warnings",  type: "string",       width: "110px",   align : 'right',    padding : '0 55px 0 0' },
    { key: null,               header: null,        type: null,           width: "170px",   align : 'left',     padding : '0 0 0 5px' },
  ];

  export const batchUncompletedExecutionsItemsMap = [
    { key: "execution_id",     header: "Id",        type: "int",          width: "40px",    align : 'center',   padding : '0 10px 0 0' },
    { key: "batch_name",       header: "Name",      type: "string",       width: "250px",   align : 'left',     padding : '0 0 0 5px' },
    { key: "creation_date",    header: "Started",   type: "string",       width: "200px",   align : 'center',   padding : '0 0 0 0' },
    { key: null,               header: null,        type: null,           width: "110px",   align : 'center',   padding : '0 0 0 0' },
    { key: "step_counter",     header: "Steps",     type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "error_counter",    header: "Errors",    type: "string",       width: "100px",   align : 'right',    padding : '0 40px 0 0' },
    { key: "status",           header: "Status",    type: "string",       width: "170px",   align : 'left',     padding : '0 0 0 5px' },
    { key: null,               header: null,        type: null,           width: "110px",   align : 'right',    padding : '0 55px 0 0' },
  ];
  
  export const batchManagerExecutionSlicesMap = [
    { key: "slice_id",               header: "Id",              type: "int" },
    { key: "slice_display",          header: "Slice Def.",      type: "string"},
    { key: "status",                 header: "Status",          type: "string"},
    { key: "pid",                    header: "Pid",             type: "int" },
    { key: "commit_counter",         header: "Commits",         type: "int" },
    { key: "retry_counter",          header: "Retries",         type: "int" },
    { key: "step_counter",           header: "Steps",           type: "int" },
    { key: "error_counter",          header: "Errors",          type: "int" },
    { key: "abandoned_step_counter", header: <div className ="multi-line-header"><span>Abandoned</span><span>Steps</span></div>,    type: "int" },
    { key: "skipped_step_counter",   header: <div className ="multi-line-header"><span>Skipped</span><span>Steps</span></div>,      type: "int" },
    { key: "duration",               header: "Duration",        type: "string" },
  ];

  export const batchManagerExecutionErrorsMap = [
    { key: "event_number",  header: "#Event",     type: "int" },
    { key: "step_number",   header: "Step Num.",  type: "int" },
    { key: "error_number",  header: "Error num.", type: "int" },
  ];

  export const getUserParametersProp = (batchManagerExecutionDetails?: any) => {
    return [
      {
        name: "Loops per slice",
        value: batchManagerExecutionDetails?.user_parameters?.loops_per_slice ?? "N/A",
      },
      {
        name: "Steps in error",
        value: Array.isArray(batchManagerExecutionDetails?.user_parameters?.steps_in_error)
          ? batchManagerExecutionDetails.user_parameters.steps_in_error.join(", ")
          : batchManagerExecutionDetails?.user_parameters?.steps_in_error ?? "N/A",
      },
      {
        name: "Warnings",
        value: Array.isArray(batchManagerExecutionDetails?.user_parameters?.warnings)
          ? batchManagerExecutionDetails.user_parameters.warnings.join(", ")
          : batchManagerExecutionDetails?.user_parameters?.warnings ?? "N/A",
      },
    ];
  };

  export const getExecutionDetailsProp = (batchManagerExecutionDetails?: any) => {
    return [
      { name: "Id",                     value: batchManagerExecutionDetails?.execution_id },
      { name: "Batch Name",             value: batchManagerExecutionDetails?.batch_name },
      { name: "Slice Controller",       value: batchManagerExecutionDetails?.slice_ctrl },
      { name: "Table",                  value: batchManagerExecutionDetails?.slices_table },
      { name: "Created by",             value: batchManagerExecutionDetails?.restarted_by },
      { name: "Creation Date",          value: getDates("Creation Date", batchManagerExecutionDetails) },
      { name: "Completion Date",        value: getDates("Completion Date", batchManagerExecutionDetails) },
      { name: "Duration",               value: getDates("Duration", batchManagerExecutionDetails) },
      { name: "Execution Mode",         value: batchManagerExecutionDetails?.execution_mode },
      { name: "Language",               value: batchManagerExecutionDetails?.language },
      { name: "Date Effect/Date Learn", value: getDates("Date Effect / Date Learn", batchManagerExecutionDetails) },
      { name: "Process Number",         value: batchManagerExecutionDetails?.process_number ?? "N/A" },
      { name: "Commit Frequency",       value: batchManagerExecutionDetails?.commit_frequency ?? "N/A" },
      { name: "Select Frequency",       value: batchManagerExecutionDetails?.select_frequency ?? "N/A" },
      { name: "Retry Limit",            value: batchManagerExecutionDetails?.retry_limit ?? "N/A" },
      { name: "Steps",                  value: batchManagerExecutionDetails?.step_counter ?? "N/A" },
      { 
        name: "Errors", 
        value: batchManagerExecutionDetails?.error_counter, 
        color: getDynamicFontColor("Errors", batchManagerExecutionDetails?.error_counter) ?? "N/A" 
      },
      { name: "Abandoned Steps", value: batchManagerExecutionDetails?.abandoned_step_counter ?? "N/A" },
      { name: "Skipped Steps", value: batchManagerExecutionDetails?.skipped_step_counter ?? "N/A" },
      { 
        name: "Warnings", 
        value: batchManagerExecutionDetails?.warning_counter, 
        color: getDynamicFontColor("Warnings", batchManagerExecutionDetails?.warning_counter) ?? "N/A" 
      },
      { 
        name: "Status", 
        value: replaceUnderscoreWithSpace(batchManagerExecutionDetails?.status), 
        icon: getStatusIcon("status", batchManagerExecutionDetails?.status),
        color: getDynamicFontColor("status", batchManagerExecutionDetails?.status) 
      }
    ];
  };

  interface ExecutionProps {
    creation_date?: string;
    creation_time?: string;
    completion_date?: string;
    completion_time?: string;
    duration?: string;
    date_effect?: string;
    date_learn?: string;
  }
  
  export const getDates = (type: string, props: ExecutionProps = {}): string => {
    const formatDate = (date?: string) => (date ? JulianDateTime.formatJulianAsDate(date) : "N/A");
    const formatTime = (time?: string) => (time ? JulianDateTime.formatJulianAsTime(time) : "N/A");
  
    switch (type) {
      case "Creation Date":
        return `${formatDate(props?.creation_date)} - ${formatTime(props?.creation_time)}`;
      case "Started":
        return `${formatDate(props?.creation_date)} - ${formatTime(props?.creation_time)}`;
      case "Completion Date":
        return `${formatDate(props?.completion_date)} - ${formatTime(props?.completion_time)}`;
      case "Finished":
        return `${formatDate(props?.completion_date)} - ${formatTime(props?.completion_time)}`;
      case "Duration":
        return formatTime(props?.duration);
      case "Date Effect / Date Learn":
        return `${formatDate(props?.date_effect)} - ${formatDate(props?.date_learn)}`;
      default:
        return "N/A";
    }
  };

  export const executionStatuses = [
    {id : "completed",               label : "Completed",              color : "green",   icon : <CheckCircleIcon/>},
    {id : "completed_with_warning",  label : "Completed with warning", color : "orange",  icon : <ExclamationTriangleIcon/>},
    {id : "stopped",                 label : "Stopped",                color : "red",     icon : <TimesIcon/>},
    {id : "to_initialize",           label : "To initialize",          color : "#666666", icon : <ResourcesEmptyIcon/>},
    {id : "initializing",            label : "Initializing",           color : "#3f3f3f", icon : <ResourcesAlmostEmptyIcon/>},
    {id : "to_process",              label : "To process",             color : "#5eeb00", icon : <ServiceIcon/>},
    {id : "processing",              label : "Processing",             color : "#56d800", icon : <RunningIcon/>},
    {id : "to_finalize",             label : "To finalize",            color : "#47b100", icon : <ResourcesAlmostFullIcon/>},
    {id : "finalizing",              label : "Finalizing",             color : "#378900", icon : <ResourcesFullIcon/>}
  ]

  export const completedStatuses = [
    'completed', 
    'completed_with_warning'
  ];

  export const uncompletedStatuses = [
    'stopped',
    'to_initialize', 
    'initializing', 
    'to_process', 
    'processing', 
    'to_finalize', 
    'finalizing'
  ];

  export const getDynamicFontColor = (key: string, value?: number | string): string => {
    if (value === null || value === undefined) return "inherit";
  
    const keyToCompare = key.trim().toLowerCase();
  
    if (keyToCompare === "status") {
      const status = executionStatuses.find((status : any) => status.id === value);
      if (status === undefined) return "inherit";
      return status.color;
    }
  
    if (["error_counter", "errors"].includes(keyToCompare) && value !== 0) {
      return "red";
    }
  
    if (["warning_counter", "warnings"].includes(keyToCompare) && value !== 0) {
      return "orange";
    }
  
    return "inherit";
  };

  export const getStatusIcon = (key: string, value?: string): JSX.Element | null => {
    if (key !== "status" || !value) return null;
  
    const status = executionStatuses.find((status : any) => status.id === value);
    if (status === undefined) return null;
    return <Icon iconSize = "sm" color = {status.color}>{status.icon}</Icon>;
  };

  export const replaceUnderscoreWithSpace = (input: any): string => {
    return typeof input === "string" ? input.replace(/_/g, " ") : String(input);
  };

  enum DeltaValue {
    SearchExpanded = -190,
    SearchCollapsed = -40,
  }

  enum DeltaValueSummaryView {
    SearchExpanded = -143,
    SearchCollapsed = 7,
  }

  export const getDeltaValue = (isSearchExpanded: boolean, isDaySummaryView: boolean) => {
    if (isDaySummaryView)
      return isSearchExpanded ? DeltaValueSummaryView.SearchExpanded : DeltaValueSummaryView.SearchCollapsed;
    else
      return isSearchExpanded ? DeltaValue.SearchExpanded : DeltaValue.SearchCollapsed;
  };
  
  export const displayValue = (value : any, type : string) : string => {
    if (value === null || value === undefined) return "N/A";
    switch (type) {
      case "int"          : return value.toString();
      case "string"       : return replaceUnderscoreWithSpace(value);
      case "julianTime"   : return JulianDateTime.formatJulianAsTime(value);
      case "julianDate"   : return JulianDateTime.formatJulianAsDate(value);
      default             : return value;  // let it be automatically converted ...
    }
  }
  
  export const AccordionSectionKeys = {
    EXECUTION_DETAILS: 'execution-details',
    USER_PARAMETERS: 'user-parameters',
    SLICES: 'slices',
    ERRORS: 'errors',
    WARNINGS: 'warnings',
  };    
        
  export const formNumbersList = [
    { content: 'Default', value: 50 },
    { content: '10',      value: 10 },
    { content: '25',      value: 25 },
    { content: '50',      value: 50 },
    { content: '100',     value: 100 },
    { content: '200',     value: 200 },
    { content: '500',     value: 500 },
    { content: 'All',     value: Infinity }
  ]; 

  