## GtMonitor

The GtMonitor plugin displays information about GtMonitor

### Overview

The GtMonitor page provides a detailed view of the GraphTalk system's status and performance. This page is essential for administrators to monitor and manage the system effectively.

### Refresh Button

The refresh button allows to refresh the page.

### Auto Refresh Button

The auto refresh button allows to automatically refresh the page at a specified interval. The refresh interval, in seconds, can be defined using the input field next to the auto refresh button.

### Runtime Section

The Runtime section displays general information about the running instance of GraphTalk:

- **Name**: The name of the runtime instance.
- **Version**: The version of the runtime instance.
- **Status**: The current status of the runtime instance, such as "RUNNING".

## Java Section

The Java section provides detailed information about the Java runtime environment:

- **Java Runtime**: Displays information about the Java Virtual Machine (JVM) running the application.
  - **Processors**: The number of processors available to the JVM.
  - **Free Memory**: The amount of free memory available in bytes.
  - **Total Memory**: The total amount of memory allocated to the JVM in bytes.
- **Threading**: Displays information about the threading within the JVM.
  - **Threads**: The current number of active threads.
  - **Peak Threads**: The peak number of threads that have been active.
  - **Daemon Threads**: The number of daemon threads.
  - **Total Started Threads**: The total number of threads started since the JVM began execution.

## Jetty Section

The Jetty section provides detailed information about the Jetty server, which is an integral part of the GraphTalk system. This section is essential for monitoring and managing the web server and its associated applications.

### Status

The status of the Jetty server is displayed at the top of the section. The status indicates whether the Jetty server is currently running or not.

- **STARTED**: This status indicates that the Jetty server is currently running and operational.

### Details

The Details section provides additional information about the Jetty server. This section can be expanded to show more detailed information about the server's configuration and current state.

### Context Handlers

The Context Handlers section lists the context handlers that are currently active on the Jetty server. Context handlers are responsible for managing the lifecycle and processing of web applications within the server.

- **Expand all**: This option allows to expand all context handlers to view detailed information about each handler.
- **Collapse all**: This option allows to collapse all context handlers to hide the detailed information.

### Webapp Contexts

The Webapp Contexts section lists the web applications that are currently deployed and running on the Jetty server. This section provides information about each web application, including its name and status.

- **Expand all**: This option allows to expand all web application contexts to view detailed information about each application.
- **Collapse all**: This option allows to collapse all web application contexts to hide the detailed information.

### Web Applications

- **admin**: This web application is responsible for providing administrative functionalities. The status indicates whether the application is currently running or not.
  - **STARTED**: This status indicates that the admin web application is currently running and operational.

- **graphtalk**: This web application is the main application for GraphTalk. The status indicates whether the application is currently running or not.
  - **STARTED**: This status indicates that the graphtalk web application is currently running and operational.

## Instance Manager Section

The Instance Manager section provides detailed information about the various instances managed within the GraphTalk system.

### Status

The status of the Instance Manager is displayed at the top of the section. The status indicates whether the Instance Manager is currently running or not.

- **RUNNING**: This status indicates that the Instance Manager is currently running and operational.

### Details

The Details section provides additional information about the managed instances. This section can be expanded to show more detailed information about each instance and its configuration.

### Remote Admin Server

The Remote Admin Server section provides information about the remote administration server:

- **Status**: The current status of the Remote Admin Server, such as "Awaiting control client attach".
- **Port**: The port number on which the Remote Admin Server is listening for incoming connections.
- **Clients**: The number of clients currently connected to the Remote Admin Server.
- **Max clients**: The maximum number of clients that can be connected to the Remote Admin Server.

### Statistics Manager

The Statistics Manager section provides information about the statistics manager:

- **Level**: The level of the Statistics Manager, which can indicate its priority or importance.
- **Time slice (s)**: The time slice in seconds for the Statistics Manager, indicating the interval at which statistics are collected or processed.
- **Flush period (min)**: The flush period in minutes for the Statistics Manager, indicating the interval at which statistics are flushed or cleared.

### Statistics Counters

The Statistics Counters section provides information about the statistics counters:

- **Failed count**: The number of failed operations or requests.
- **Request count**: The number of requests processed.
- **Timeout count**: The number of operations or requests that have timed out.

## Listener Manager Section

The Listener Manager section provides detailed information about the various listeners managed within the GraphTalk system. This section is crucial for monitoring and managing the listeners and their associated services.

### Status

The status of the Listener Manager is displayed at the top of the section. The status indicates whether the Listener Manager is currently enabled or not.

- **ENABLED**: This status indicates that the Listener Manager is currently enabled and operational.

### Details

The Details section provides additional information about the managed listeners. This section can be expanded to show more detailed information about each listener and its configuration.

### Listeners

The Listeners section provides information about the various listeners:

- **Status**: The current status of the listener, such as "Sleeping/Waiting".
- **Failed**: The number of failed operations or requests.
- **Saved**: The number of saved operations or requests.
- **Succeeded**: The number of successful operations or requests.
- **Asynchronous**: The number of asynchronous operations or requests.
- **Timed out**: The number of operations or requests that have timed out.

### Listener Connections

The Listener Connections section provides information about the connections associated with each listener:

- **JobManagerListener**: This listener is responsible for managing job-related tasks.
  - **Status**: The current status of the listener connection, such as "Opened".
  - **Queued Jobs**: The number of jobs currently queued for processing.

### Listener Proxies

The Listener Proxies section provides information about the proxies associated with each listener:

- **JobManagerListener**: This listener is responsible for managing job-related tasks.
  - **Served**: The number of requests served by the proxy.
  - **Wait Time (ms)**: The wait time in milliseconds for the proxy.
  - **Status**: The current status of the listener proxy, such as "Awaiting job".e

## Server Pool Manager Section

The Server Pool Manager section provides detailed information about the various server pools managed within the GraphTalk system. This section is crucial for monitoring and managing the server pools and their associated services.

### Status

The status of the Server Pool Manager is displayed at the top of the section. The status indicates whether the Server Pool Manager is currently enabled or not.

- **ENABLED**: This status indicates that the Server Pool Manager is currently enabled and operational.

### Details

The Details section provides additional information about the managed server pools. This section can be expanded to show more detailed information about each server pool and its configuration.

### Server Pools

The Server Pools section provides information about the various server pools:

- **Status**: The current status of the server pool, such as "ENABLED".
- **Failed**: The number of failed operations or requests.
- **Served**: The number of served operations or requests.
- **Succeeded**: The number of successful operations or requests.

### Server Pool Queues

The Server Pool Queues section provides information about the queues associated with each server pool:

- **Queued items**: The number of items currently queued for processing.
- **Waiting callers**: The number of callers currently waiting for service.

### Server Pool Server Instances

The Server Pool Server Instances section provides information about the server instances associated with each server pool:

- **Server Id**: The unique identifier for the server instance.
- **Requests**: The number of requests processed by the server instance.
- **Restarts**: The number of times the server instance has been restarted.
- **Wait Time (ms)**: The wait time in milliseconds for the server instance.
- **Idle Time (ms)**: The idle time in milliseconds for the server instance.

## Camel Manager

The Camel Manager section provides detailed information about the Camel integration within the GraphTalk system. 

### Broker Manager

The Broker Manager section provides information about the broker management within the Camel integration.