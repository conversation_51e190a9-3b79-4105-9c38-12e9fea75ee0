.font-size-12 {
  font-size: 12px;
}

.font-size-13 {
  font-size: 13px;
}

.font-size-14 {
  font-size: 14px;
}

.font-size-13-padding-top-10 {
  font-size: 13px;
  padding-top: 30px;
}

.search_box_titles_size_padding {
  font-size: 13px;
  padding-left: 30px;
}

.search_box_titles_size_padding_60 {
  font-size: 13px;
  padding-left: 60px;
}

.padding-top-10 {
  padding-top: 10px;
}

.padding-left-10 {
  padding-left: 10px;
}

.line-height-1_2 {
  line-height: 1.2;
}

.custom-compact-table {
  font-size: 12px !important;
  line-height: 1.2 !important;
}

.pf-v5-c-table__th.truncate-header {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 50px;
  font-size: 12px;
}

.custom-compact-table_padding {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  cursor: 'pointer' !important;
}

.top-right-corner {
  position: absolute;
  top: 10px;
  right: 10px;
}

.top-central-position {
  position: absolute;
  top: 10px;
  right: 440px;
}

.checkbox-title-container {
  position: absolute;
  top: 0;
  right: 40px;
}

.checkbox-container {
  position: absolute;
  right: 10px;
}

.prev-next-today-container {
  position: absolute;
  top: 1px;
  right: 260px;
}

.accordion-name-cell {
  font-size: 12px !important;
  margin: 0;
  white-space: nowrap;
  padding: 0px 0px 0px 30px;
  font-weight: bold;
}

.accordion-value-cell {
  font-size: 12px !important;
  margin: 0;
  white-space: nowrap;
  padding: 0px 0px 0px 20px;
  font-weight: normal;
  color: inherit;
}

.accordion-altvalue-cell {
  font-size: 12px;
  margin: 0;
  font-weight: normal;
}

.accordion-td {
  padding-left: 10px;
}

.accordion-row-odd {
  background-color: var(--pf-global--BackgroundColor--light-200);
}

.accordion-row-even {
  background-color: var(--pf-global--BackgroundColor--100);
}

.pf-v5-c-menu__item {
  font-size: 13px;
}

.pf-v5-c-calendar-month {
  font-size: 13px;
}

.pagination-button {
  font-size: 13px;
  padding: 2px 8px;
  min-width: 60px;
  color: white;
}

.apply-button {
  font-size: 13px;
  padding: 2px 8px;
  margin-top: 5px;
  min-width: 60px;
  color: white;
}

.counters-custom-table tr.pf-v5-c-table__tr > td,
.counters-custom-table tr.pf-v5-c-table__tr > th {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
}

.counters-custom-table tr.pf-v5-c-table__tr>td.td-last-value {
  width: 10%;
  padding-right: 10px;
  text-align: right;
  font-weight: 500;
  color: #330072;
  font-size: 10px !important;
}