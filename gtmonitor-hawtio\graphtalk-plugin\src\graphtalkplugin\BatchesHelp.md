## Batches

The Batches plugin displays information about GraphTalk Batches.

### Search Criteria

The search criteria allows to search for specific batches using various criteria:

- **Number of batches**: Can filter batches by the number of batches.
- **Id**: Can search for batches by their unique identifier.
- **Name**: Can search for batches by their name.
- **Slice Controller**: Can filter batches by their slice controller.

### List of Batch Executions

The table displays the following information about each batch:

- **Id**: The unique identifier of the batch.
- **Name**: The name of the batch.
- **Date**: The date when the batch was created.
- **Time**: The time when the batch was created.
- **Duration**: The duration of the batch execution.
- **Steps**: The number of steps in the batch.
- **Errors**: The number of errors in the batch.
- **Warnings**: The number of warnings in the batch.
- **Status**: The current status of the batch.

The table can be sorted by clicking on the column headers and filtered using the search section.

### Statuses

The following statuses are available for batches:

- **Completed**: The batch has completed successfully.
- **Stopped**: The batch has been stopped.
- **Completed with errors**: The batch has completed with errors.
- **To initialize**: The batch is waiting to be initialized.
- **Initializing**: The batch is being initialized.
- **To process**: The batch is waiting to be processed.
- **Processing**: The batch is being processed.
- **To finalize**: The batch is waiting to be finalized.
- **Finalizing**: The batch is being finalized.

### Refresh Button

The refresh button allows to refresh the page.

### Auto-Refresh Button

The auto refresh button allows to automatically refresh the page at a specified interval. The refresh interval, in seconds, can be defined using the input field next to the auto refresh button.

### Search Button

The search button allows to search for lots according to the set criteria.

### Reset Button

The reset button allows to refresh the list of batches. Clicking the reset button will update the list of batches to reflect the current state.

### Previous button

The previous button allows to see the previous date on which there were executed batches.

### Next button

The next button allows to see the next date on which there were executed batches.

### Today button

The today button allows to see if there are executed batches on today's date.

### Day Summary Checkbox

The day summary checkbox allows to view batches divided into two tables. The first table shows completed batches, while the second shows incomplete ones

### Execution Details Tab

The execution details tab allows to view detailed information about the execution of a batch. Clicking the execution details tab will display a detailed view of the batch execution, including information about each step in the batch.

### User Parameters Tab

The user parameters tab allows to view and edit the parameters for a batch. Clicking the user parameters tab will display a form where can view and edit the parameters for the batch.

### Slices Tab

The slices tab allows to view information about the slices in a batch. Clicking the slices tab will display a list of slices in the batch, including information about each slice.

### Errors Tab

The errors tab allows to view information about the errors in a batch. Clicking the errors tab will display a list of errors in the batch, including information about each error.

### Warning Tab

The warning tab allows to view information about the warnings in a batch. Clicking the warning tab will display a list of warnings in the batch.
