## Job Manager

The Job Manager plugin displays information about GraphTalk Job Manager


### Overview

The Job Manager page provides a detailed view of various jobs managed within GraphTalk. This page is useful for monitoring and managing the execution and status of different jobs within the system.

### Search Criteria

The search field allows to search for specific jobs using various criteria:

- **Number of jobs**: Can filter jobs by the number of jobs.
- **Job Id**: Can search for jobs by their unique identifier.
- **User**: Can search for jobs by the user who submitted them.
- **Status**: Can filter jobs by their current status.

### List of Jobs

The table on the Job Manager page displays the following information:

- **Job Id**: The unique identifier of the job.
- **Name**: The name of the job.
- **User**: The user who submitted the job.
- **Date**: The date when the job was submitted.
- **Status**: The current status of the job.

The table can be sorted by clicking on the column headers and filtered using the search section.

### Statuses

The following statuses are available for jobs:

- **Cancelled**: The job has been cancelled.
- **Done**: The job has completed successfully.
- **Pending**: The job is pending execution.
- **Processing**: The job is currently being processed.
- **Suspended**: The job is currently being suspended.
- **Error**: The job encountered an error during execution.

### Refresh Button

The refresh button allows to refresh the page.

### Auto Refresh Button

The auto refresh button allows to automatically refresh the page at a specified interval. The refresh interval, in seconds, can be defined using the input field next to the auto refresh button.

### Search Button

The search button allows to search for lots according to the set criteria.

### Reset Button

The reset button allows to refresh the list of batches. Clicking the reset button will update the list of batches to reflect the current state.

### Job Information Section

The Job Information section provides detailed information about the selected job:

- **Job Id**: The unique identifier of the job.
- **Name**: The name of the job.
- **User**: The user who submitted the job.
- **Status**: The current status of the job.
- **Submitted Date**: The date when the job was submitted.
- **Submitted Time**: The time when the job was submitted.
- **Start Date**: The date when the job started processing.
- **Start Time**: The time when the job started processing.
- **Transaction**: The transaction associated with the job.
- **Dependencies**: The dependencies of the job.

### User Parameters Section

The User Parameters section provides information about the parameters set by the user for the job. This section may be empty if no user parameters are defined.

### Error Section

The Error section provides information about any errors encountered during the execution of the job. This section may be empty if no errors are encountered.

## Process Control Checkbox

The process control checkbox provides access to the interface for mananaging the job manager processor and displaying the status of the job manager processes.

### General Information Section

The General Information section provides an overview of the current state of the system processes:

- **Status**: Displays the "Scanning"status of the system processes. 
- **Slave Count**: Shows the number of active slave processes. Can adjust this number to optimize system performance.
- **Poll Time**: Indicates the interval at which the system polls for updates.

### List of Processes

The table displays detailed information about each process:

- **Name**: The name of the process.
- **Status**: The current status of the process.
- **Finished Jobs**: The number of jobs that have been completed.
- **Current Wait Time**: The current wait time for the process

Can sort the table by clicking on the column headers. Can also filter the table by using the search field to find specific jobs or statuses.

### Pause/Unpause Job Manager Button

The Pause/Unpause Job Manager button allows to pause or unpause the execution of all jobs. This can be useful for performing maintenance or troubleshooting issues without stopping the entire system.

### Kill Slave Button

The Kill Slave button allows to terminate a specific slave process. Use this function to stop a problematic or unresponsive slave process, ensuring that system resources are freed up and can be reallocated as needed.

This detailed view helps in monitoring the system's processes, allowing for effective management and control. It is designed to give you the insights and tools you need to keep your system running at its best.