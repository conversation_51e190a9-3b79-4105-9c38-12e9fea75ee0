<?xml version="1.0"?>
<!DOCTYPE Configure PUBLIC "-//Jetty//Configure//EN" "https://www.eclipse.org/jetty/configure_10_0.dtd">

<Configure id="Server" class="org.eclipse.jetty.server.Server">

  <!-- =========================================================== -->
  <!-- Get the platform MBeanServer                                -->
  <!-- =========================================================== -->
  <Call id="MBeanServer" class="java.lang.management.ManagementFactory"
    name="getPlatformMBeanServer" />

  <!-- =========================================================== -->
  <!-- Initialize the Jetty MBeanContainer                         -->
  <!-- =========================================================== -->
  <Call name="addBean">
    <Arg>
      <New id="MBeanContainer" class="org.eclipse.jetty.jmx.MBeanContainer">
        <Arg>
          <Ref refid="MBeanServer" />
        </Arg>
        <Call name="beanAdded">
          <Arg/>
          <Arg>
            <Get name="ILoggerFactory" class="org.slf4j.LoggerFactory"/>
          </Arg>
        </Call>
      </New>
    </Arg>
  </Call>
</Configure>

