import { hawtio, Hawtio, registerPlugins, configManager } from '@hawtio/react'

import React from 'react';

import ReactDOM from 'react-dom/client'

import '@patternfly/react-core/dist/styles/base.css';

import {plugin} from './index';

// Register builtin plugins
registerPlugins()

// Bootstrap Hawtio
hawtio.bootstrap()

// Register the plugin under development
plugin()

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement)

root.render(
    <Hawtio />
)
