
import { Logger, workspace, MBeanNode, MBeanTree } from '@hawtio/react'

import {
  Button,
  KeyTypes,
  PageSection,
  Spinner,
  Split, SplitItem,
  Stack, StackItem,
  Switch,
  Text, TextContent, TextVariants,
  Title
} from '@patternfly/react-core'

import React, { useEffect, useRef, useState } from 'react'

import {RedoIcon} from '@patternfly/react-icons/dist/esm/icons'

import './GraphTalk.css'


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-component");

const logPrefix             = "** GraphTalk Component ** ";
  
//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Main Custom Hooks
// ********************************************************************************************************
// ********************************************************************************************************

export function useGraphTalkComponentHeight() {
  // deltaWindowHeight: sum of folowing heights:
  // - Hawtio's main header, aka "DXC GraphTalk Management Console, Powered by Hawtio"
  // - secondary header for displaying GraphTalk
  // - GraphTalk's components nvaigation menu
  // !!!!!!!!!!!!! SHOULD BE COMPUTED INSTEAD OF HARD CODED
  const deltaHeight = 230; 

  const [graphtalkComponentHeight, setGraphTalkComponentHeight] = useState(Math.max(0, window.innerHeight - deltaHeight));

  function debounce(fn :any, ms : any) {
    let timer : any;
    return function _() {
      clearTimeout(timer)
      timer = setTimeout(function settimeout() {
      timer = null
      fn.apply(this, arguments)
      }, ms)
    };
  }

  useEffect(() => {
    const debouncedHandleResize = debounce(function handleResize() {
      const newHeight = Math.max(0, window.innerHeight - deltaHeight);
      if (newHeight !== graphtalkComponentHeight)
        setGraphTalkComponentHeight(newHeight);
      }, 1000);
      window.addEventListener("resize", debouncedHandleResize);
      return () => {
        window.removeEventListener("resize", debouncedHandleResize);
      };
  }, []);

  return graphtalkComponentHeight;
}


// ********************************************************************************************************
// ********************************************************************************************************
// Utils Components
// ********************************************************************************************************
// ********************************************************************************************************


export const GraphTalkComponentDiv : React.FunctionComponent<any> = (props: any) : any => {
  const delta = props?.delta ? props.delta : 0;
  const className = props?.className ? props.className : null;
  const graphTalkComponentHeight = useGraphTalkComponentHeight() + delta;

  const style : any = (props?.hasScroll) ? {height : graphTalkComponentHeight, overflowY : "scroll"}
                                         : {height : graphTalkComponentHeight, overflowY : "none"}

  if (className === null)
    return (
      <div tabIndex = {-1} style = {style} aria-label = "GraphTalkComponentDiv">
        {props.children}
      </div>
    )
  else
    return (
      <div tabIndex = {-1} style = {style} aria-label = "GraphTalkComponentDiv" className = {className}>
        {props.children}
      </div>
    )
}


// ********************************************************************************************************
// ********************************************************************************************************
// Main Component
// ********************************************************************************************************
// ********************************************************************************************************

const GraphTalkComponent : React.FunctionComponent<any> = (props : any) => {

  const hasRefresh = (props?.hasRefresh !== undefined) ? props.hasRefresh : true;

  // ********************************************************************************************************

  const children = (props?.children) ? props.children : <></>;
  const onCompute = (props?.onCompute) ? props.onCompute : (async () => {});

  // ********************************************************************************************************
  
  const autoRefreshIntervalMinValue     = 1;      /* in seconds */
  const autoRefreshIntervalMaxValue     = 1000;   /* in seconds */
  const autoRefreshIntervalDefaultValue = 5;      /* in seconds */
  const autoRefresh                     = useRef<any>(null);
  const autoRefreshInterval             = useRef<number>(autoRefreshIntervalDefaultValue);    /* in seconds */
 
  // ********************************************************************************************************

  const [loadingGraphTalkComponent, setLoadingGraphTalkComponent] = useState(true);

  function isLoadingGraphTalkComponent() : boolean {
    return loadingGraphTalkComponent
}

  // ********************************************************************************************************
    
  const [forceRefreshGraphTalkComponent, setForceRefreshGraphTalkComponent] = useState(true);

  const toggleForceRefreshGraphTalkComponent = () => { setForceRefreshGraphTalkComponent(!forceRefreshGraphTalkComponent) };

  // ********************************************************************************************************

  async function computeGraphTalkComponent() {
    await onCompute();
  }

  // ********************************************************************************************************

  function refreshGraphTalkComponent() {
    log.debug(logPrefix, "refreshGraphTalkComponent START");
  //setLoadingGraphTalkComponent(true);   generates refresh flickering ...

    computeGraphTalkComponent()
      .then (() => {
        log.debug(logPrefix, "refreshGraphTalkComponent OK");
        setLoadingGraphTalkComponent(false);
      })
      .catch (error => {
        log.error(logPrefix, "refreshGraphTalkComponent exception: ", error);
        setLoadingGraphTalkComponent(false);
      })
      .finally (() => { })

    log.debug(logPrefix, "refreshGraphTalkComponent END");
//  new Promise(f => setTimeout(f, 10000)).then (()=>{}).catch(error => {}).finally(() => {})
  }

  // ********************************************************************************************************

  useEffect(() => {
    refreshGraphTalkComponent()
  }, [forceRefreshGraphTalkComponent])
 
  // ********************************************************************************************************

  const Refresh = () => {
    const [refresh, setRefresh] = useState<boolean>(true);

    // ********************************************************************************************************
    const RefreshButton = () => {
      const handleRefreshButtonClick = () => {
        log.debug(logPrefix, "Refresh Button Clicked");
        refreshGraphTalkComponent();
      }
  
      const handleRefreshButtonKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        log.debug(logPrefix, "Refresh Button Enter");
        if (e.key === KeyTypes.Enter) {
          refreshGraphTalkComponent();
        }
      }

      return (
        <SplitItem tabIndex = {-1}>
          <Button
            tabIndex    = {0}
            variant     = 'primary'
            size        = 'xs'
            isDisabled  = {autoRefresh.current !== null}
            onKeyDown   = {handleRefreshButtonKeyDown}
            onClick     = {handleRefreshButtonClick}
            style       = {{fontSize : "12px"}}
            icon        = <RedoIcon style={{ fontSize: "12px" }}/>
          >Refresh</Button>
        </SplitItem>
      )
    }

    // ********************************************************************************************************
    const AutoRefreshSwitch = () => {
      const handleChange = () => {
        if (autoRefresh.current === null) {
          try { autoRefresh.current = setInterval(refreshGraphTalkComponent, autoRefreshInterval.current * 1000 /* in milliseconds */); } catch(e) {};
        }
        else {
          try { clearInterval(autoRefresh.current); } catch (e) {}
          autoRefresh.current = null;
        }
        setRefresh(!refresh);
      }
      return (
        <>
          <SplitItem tabIndex = {-1} >
            <Switch tabIndex = {0} aria-label = 'gtmonitor-autorefresh' hasCheckIcon = {false} isChecked = {autoRefresh.current !== null} onChange = {() => handleChange()}/>
          </SplitItem>
          <SplitItem tabIndex = {-1}>
            <TextContent tabIndex = {-1}>
              <Text tabIndex = {-1} component = {TextVariants.p} style = {{fontSize : "14px"}}>Auto Refresh, interval in (s):</Text>
            </TextContent>
          </SplitItem>
        </>
      )
    }

    // ********************************************************************************************************
    const AutoRefreshInterval = () => {
      const [value, setValue] = useState<string>(autoRefreshInterval.current.toString());
      const [error, setError] = useState(false);
      const [showErrorText, setShowErrorText] = useState(false);
      const ref = useRef(null);
  
      const isNewValueValid = (event: React.FormEvent<HTMLInputElement>) : boolean => {
        let newValueIsValid = !((event.target as HTMLInputElement).validity.patternMismatch);
        if (newValueIsValid) {
          const checkVal = Number((event.target as HTMLInputElement).value);
          newValueIsValid = (!isNaN(checkVal) && checkVal >= autoRefreshIntervalMinValue && checkVal <= autoRefreshIntervalMaxValue)
        }
        if (error && newValueIsValid) {
          setError(false);
          setShowErrorText(false);
        }
        if (!error && !newValueIsValid) {
          setError(true);
          setShowErrorText(true);
        }
        return newValueIsValid;
      }

      const handleChange = (event: React.FormEvent<HTMLInputElement>) => {
        log.debug(logPrefix, "AutoRefreshInterval / onChange / ", (event.target as HTMLInputElement).value);
        const newValue = (event.target as HTMLInputElement).value;
        setValue(newValue);
      };

      const handleBlur = (event: React.FormEvent<HTMLInputElement>) => {
        log.debug(logPrefix, "AutoRefreshInterval / onBlur / ", (event.target as HTMLInputElement).value);
        const newValueIsValid = isNewValueValid(event);
        let newValue = (event.target as HTMLInputElement).value;
        if (newValueIsValid) {
          const newInterval = Number(newValue);
          if (newInterval !== autoRefreshInterval.current) {
            log.debug(logPrefix, "AutoRefreshInterval / New interval is : / ", newInterval);
            autoRefreshInterval.current = newInterval;
          }
        }
        setValue(newValue);
        if (!newValueIsValid && ref !== null && ref.current !== null)
            ref.current.focus();
      }

      const handleFocus = () => {
        log.debug(logPrefix, "AutoRefreshInterval / onFocus");
        if (error) {
          setShowErrorText(true);
        }
      };

      return (
        <>
          <SplitItem tabIndex = {-1}>
            <input tabIndex = {0}
              disabled          = {autoRefresh.current !== null}
              type              = "text"
              id                = "autorefreshinterval"
              name              = "autorefreshinterval"
              title             = "Auto refresh interval"
              value             = {value}  
              inputMode         = 'decimal' 
              onChange          = {handleChange}
              onBlur            = {handleBlur}
              onFocus           = {handleFocus}
              pattern           = "[0-9]+"
              ref               = {ref}
              required
              size              =  {5}
              style             = {{fontSize : "14px"}}
            />
          </SplitItem>
          <SplitItem tabIndex = {-1}>
          {
            (showErrorText && (
              <span tabIndex = {-1} role = "alert" style = {{ color: "red" }}>
              Auto refresh interval must be an <em>integer</em> between <em>{autoRefreshIntervalMinValue} s</em> and <em>{autoRefreshIntervalMaxValue} s</em>
              </span>
            ))
          }
          </SplitItem>
        </>
      );
    };

    // ********************************************************************************************************
    return (
      <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
        <RefreshButton/>
        <AutoRefreshSwitch/>
        <AutoRefreshInterval/>
      </Split>
    )
  }

  // ********************************************************************************************************

  if (isLoadingGraphTalkComponent()) {
    log.debug(logPrefix, "Displaying Spinner");
    return (
      <PageSection tabIndex = {-1} padding = {{default : 'noPadding', xl : 'padding'}}>
        <Spinner tabIndex = {-1} size = "xl">Loading ...</Spinner>
      </PageSection>
    )
  }

  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying Content");
    
  return (
    <div style={{ overflowY: 'hidden' }}>
      <React.Fragment>
        <PageSection variant='light' style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title headingLevel='h1'>{props.title}</Title>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            {hasRefresh && <Refresh/>}
          </div>
        </PageSection>
        <PageSection tabIndex={-1}>
          <Stack tabIndex={-1} hasGutter>
            <StackItem tabIndex={-1}>
              {props.children}
            </StackItem>
          </Stack>
        </PageSection>
      </React.Fragment>
    </div>
  )
}

export default GraphTalkComponent;
