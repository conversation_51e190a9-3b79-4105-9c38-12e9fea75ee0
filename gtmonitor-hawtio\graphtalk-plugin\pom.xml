<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

<!--
    <parent>
        <groupId>io.hawt</groupId>
        <artifactId>project</artifactId>
        <version>4.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
-->

    <groupId>io.hawt.plugins</groupId>
    <artifactId>graphtalk-plugin</artifactId>
    <name>GraphTalkPlugin</name>
    <description>GraphTalk Plugin</description>
    <packaging>war</packaging>
    <version>99.0.00</version>

    <properties>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--
          Path to the Hawtio plugin TS project. You can specify the path if it's placed
          differently from the project root.
        -->
        <plugin.path>${project.basedir}</plugin.path>

        <hawtio.version>4.3.0</hawtio.version>

        <servlet-api.version>5.0.0</servlet-api.version>
        <slf4j.version>2.0.17</slf4j.version>

        <!-- Used for maven-frontend-plugin -->
        <node.version>v20.9.0</node.version>
        <yarn.version>v4.3.1</yarn.version>
        <yarn-berry.version>4.3.1</yarn-berry.version>

        <!-- Maven plugins -->
        <frontend-maven-plugin.version>1.15.0</frontend-maven-plugin.version>
        <maven-clean-plugin.version>3.3.2</maven-clean-plugin.version>
        <maven-dependency-plugin.version>3.6.1</maven-dependency-plugin.version>
		<maven-war-plugin.version>3.4.0</maven-war-plugin.version>
    </properties>

    <dependencies>
        <!--
          We only need to embed this dependency in the war. This contains
          a nice helper class that our plugin can use to export its plugin
          mbean.
        -->
        <dependency>
              <groupId>io.hawt</groupId>
              <artifactId>hawtio-plugin-mbean</artifactId>
              <version>${hawtio.version}</version>
        </dependency>

        <!-- Servlet API is provided by the container -->
        <dependency>
              <groupId>jakarta.servlet</groupId>
              <artifactId>jakarta.servlet-api</artifactId>
              <version>${servlet-api.version}</version>
              <scope>provided</scope>
        </dependency>

        <!-- hawtio-war is needed to ensure build order -->
        <dependency>
              <groupId>io.hawt</groupId>
              <artifactId>hawtio-war</artifactId>
              <version>${hawtio.version}</version>
              <scope>provided</scope>
              <type>war</type>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- Include the build output of the TS project as the resources to JAR -->
        <resources>
            <resource>
                <directory>${plugin.path}/build</directory>
                <targetPath>${project.build.directory}/${project.artifactId}-${project.version}</targetPath>
                <excludes>
                    <exclude>index.html</exclude>
                </excludes>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.github.eirslett</groupId>
                    <artifactId>frontend-maven-plugin</artifactId>
                    <version>${frontend-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
				<plugin>
					<artifactId>maven-war-plugin</artifactId>
					<version>${maven-war-plugin.version}</version>
				</plugin>
            </plugins>
        </pluginManagement>

        <plugins>

            <!-- Clean up the plugin's 'build/' and 'node_modules/' directories as well -->
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${plugin.path}/node_modules</directory>
                            <includes>
                                <include>**/*</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                        <fileset>
                            <directory>${plugin.path}</directory>
                            <includes>
                                <include>node_modules/**/*</include>
                                <include>node_modules</include>
                                <include>.vscode/**/*</include>
                                <include>.vscode</include>
                                <include>build/**/*</include>
                                <include>build</include>
                                <include>target/**/*</include>
                                <include>target</include>
                                <include>package-lock.json</include>
                                <include>.pnp.cjs</include>
                                <include>.pnp.loader.mjs</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                        <fileset>
                            <directory>${plugin.path}/.yarn</directory>
                            <includes>
                                <include>cache/**/*</include>
                                <include>cache</include>
                                <include>sdks/**/*</include>
                                <include>sdks</include>
                                <include>unplugged/**/*</include>
                                <include>unplugged</include>
                                <include>install-state.gz</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                        <fileset>
                            <directory>${plugin.path}/node</directory>
                            <includes>
                                <include>node.exe</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>

            <!-- Build the TS plugin project from Maven -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <configuration>
                    <workingDirectory>${plugin.path}</workingDirectory>
                </configuration>
                <executions>
                    <!--<execution>
                        <id>install node and yarn</id>
                        <goals>
                            <goal>install-node-and-yarn</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>${node.version}</nodeVersion>
                            <yarnVersion>${yarn.version}</yarnVersion>
                        </configuration>
                    </execution>-->
                    <execution>
                        <id>yarn set</id>
                        <goals>
                            <goal>yarn</goal>
                        </goals>
                        <configuration>
                            <arguments>set version ${yarn-berry.version}</arguments>
                        </configuration>
                    </execution>
<!--
                    <execution>
                        <id>yarn npm audit</id>
                        <goals>
                            <goal>yarn</goal>
                        </goals>
                        <configuration>
                            <arguments>npm audit -A</arguments>
                        </configuration>
                    </execution>
-->
                    <execution>
                        <id>yarn install</id>
                        <goals>
                            <goal>yarn</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>yarn set sdk for vscode</id>
                        <goals>
                            <goal>yarn</goal>
                        </goals>
                        <configuration>
                            <arguments>dlx @yarnpkg/sdks vscode</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>yarn build</id>
                        <goals>
                            <goal>yarn</goal>
                        </goals>
                        <configuration>
                            <arguments>build</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
<!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.csc.graphtalk</groupId>
                                    <artifactId>gtmonitor-hawtio</artifactId>
                                    <version>${project.version}</version>
                                    <type>war</type>
                                    <outputDirectory>${project.build.directory}/deploy</outputDirectory>
                                    <destFileName>hawtio.war</destFileName>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
-->
    </plugins>
  </build>

</project>
