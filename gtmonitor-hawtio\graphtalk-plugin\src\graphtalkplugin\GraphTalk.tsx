
import { camel, Logger, hawtio, configManager, jolokiaService, HawtioPlugin, helpRegistry, preferencesRegistry } from '@hawtio/react'
import { ReadResponseValue } from 'jolokia.js';
import { IJolokiaSimple } from '@jolokia.js/simple'
import React from 'react';

import '@patternfly/react-core/dist/styles/base.css';

import GtMonitor                from './GtMonitor'
import JobManager               from './JobManager'
import WebErrors                from './WebErrors'
import Batches                  from './Batches'
import Counters                 from './Counters'
import Security                 from './Security'

import GtMonitorHelp            from './GtMonitorHelp.md'
import JobManagerHelp           from './JobManagerHelp.md'
import WebErrorsHelp            from './WebErrorsHelp.md'
import BatchesHelp              from './BatchesHelp.md'
import CountersHelp             from './CountersHelp.md'
import SecurityHelp             from './SecurityHelp.md'

import GtMonitorPreferences     from './GtMonitorPreferences'
import JobManagerPreferences    from './JobManagerPreferences'
import WebErrorsPreferences     from './WebErrorsPreferences'
import BatchesPreferences       from './BatchesPreferences'
import CountersPreferences      from './CountersPreferences'
import SecurityPreferences      from './SecurityPreferences'

import { getContext } from './Context';

import './GraphTalk.css'


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-plugin");

const logPrefix = "** GraphTalk ** ";
  
//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);

//const rbaclog = Logger.get("hawtio-rbac");
//rbaclog.setLevel(Logger.DEBUG);


// ********************************************************************************************************
// ********************************************************************************************************
// GraphTalk
// ********************************************************************************************************
// ********************************************************************************************************

/*
  Example of using configManager
*/
/*
import { Button, Modal } from '@patternfly/react-core'

const ExampleHeaderItem: React.FunctionComponent = () => {
  const [isModalOpen, setIsModalOpen] = React.useState(false)

  const handleModalToggle = () => {
    setIsModalOpen(!isModalOpen)
  }

  return (
    <React.Fragment>
      <Button id='example-header-item-button' variant='primary' onClick={handleModalToggle}>
        Click Me !
      </Button>

      <Modal
        id='example-header-item-modal'
        title='Basic modal'
        isOpen={isModalOpen}
        onClose={handleModalToggle}
        actions={[
          <Button key='confirm' variant='primary' onClick={handleModalToggle}>
            Confirm
          </Button>,
          <Button key='cancel' variant='link' onClick={handleModalToggle}>
            Cancel
          </Button>,
        ]}
      >
        Hello World! I am part of the Security plugin
      </Modal>
    </React.Fragment>
  )
}

configManager.addProductInfo('GraphTalk Plugin', '99.0.0')

configManager.configure(config => {
  // Branding & styles
  config.branding = {
    appName             : "DXC GraphTalk Management Console",
    showAppName         : true,
    appLogoUrl          : "static/dxc/DXC_Logo_GraphTalk.svg",
    css                 : "static/dxc/dxc.css",
    favicon             : "static/dxc/favicon.ico"
  }

  // Login page
  config.login = {
    title: 'Login to your Account',
    description: 'Login to our Account',
    links: [
      { url: '#terms',      text: 'Terms of use' },
      { url: '#help',       text: 'Help' },
      { url: '#privacy',    text: 'Privacy policy' },
    ],
  }
  
  // About modal
  if (!config.about) {
    config.about = {}
  }
  config.about.title        = "DXC GraphTalk Management Console"
  config.about.description  = "About page for DXC GraphTalk Management Console"
  config.about.imgSrc       = "static/dxc/DXC_Logo-big.svg"
//config.about.backgroundImgSrc = "static/dxc/DXC_Logo-big.svg"
  if (!config.about.productInfo) {
    config.about.productInfo = []
  }
  config.about.productInfo.push(
    { name: 'Hawtio Sample Plugin - simple-plugin', value: '1.0.0' },
    { name: 'Hawtio Sample Plugin - custom-tree',   value: '1.0.0' },
  )
  config.about.copyright = "DXC All Rights Reserved";

  // If you want to disable specific plugins, you can specify the paths to disable them.
  config.disabledRoutes = ['/connect']
})
*/

const hawtioPlugins = [
  // reordered, order -1 means keep original order:
  {id : "runtime",    order : 201,    enabled : true},
  {id : "jmx",        order : 202,    enabled : true},
  {id : "camel",      order : 203,    enabled : true},
  // disabled:
  {id : "connect",    order : -1,     enabled : false},
  {id : "logs",       order : -1,     enabled : false},
  {id : "quartz",     order : -1,     enabled : false},
  {id : "springboot", order : -1,     enabled : false}
]

const disabledRoutes = hawtioPlugins
                        .filter((plugin : any) => !plugin.enabled)
                        .map((plugin : any) => { return "/" + plugin.id });

configManager.configure(async config => {
  // Disable specific plugins by specifying their path:

  let jettyVersion      = "Unknown";
  let camelVersion      = "Unknown";
  let gtMonitorVersion  = "Unknown";
  let artemisVersion    = "Unknown";
  let jdkVersion        = "Unknown";
  
  if (!config.about) {
    config.about = {};
  }

  if (!config.about.productInfo) {
    config.about.productInfo = [];
  }

  let jolokia: IJolokiaSimple | null = null;
  
  try {
   jolokia = await jolokiaService.getJolokia()
  }
  catch (e) {}
   
  if (jolokia !== null) {
      //GET CAMEL VERSION
      try {
          const mbeans = await jolokia.search("org.apache.camel:context=*,type=context,name=*");
          if (mbeans !== null && mbeans.length > 0) {
            const result : any = await jolokia.getAttribute(mbeans[0], "CamelVersion");
            if (result != null)
              camelVersion = result;
          }
      } catch (e) {}

      //GET JAVA JDK VERSION
      try {
        const result : any = await jolokia.getAttribute("java.lang:type=Runtime", "SystemProperties");
        if (result != null)
          jdkVersion = result["java.version"] + " " + result["java.vendor"] + " " + result["java.vm.name"]
      } catch (e) {}
     

      //GET GTMONITOR VERSION
      try {
        const mbeans = await jolokia.search("com.csc.gtmonitor.*:type=InstanceManager");
        if (mbeans !== null && mbeans.length > 0) {
          const result : any = await jolokia.getAttribute(mbeans[0], "gtMonitorVersion");
          if (result !== null)
            gtMonitorVersion = result;
        }
      } catch (e) {}
        

      //GET ACTIVEMQ ARTEMIS VERSION
      try {
        const mbeans = await jolokia.search("org.apache.activemq.artemis:broker=*");
        if (mbeans !== null && mbeans.length > 0) {
          const result : any = await jolokia.getAttribute(mbeans[0], "Version");
          if (result !== null)
            artemisVersion = result;
         }
      } catch (e) {}

      //GET JETTY VERSION
      try {
        const mbeans = await jolokia.search("com.csc.gtmonitor.jetty:context=*,type=server,id=*");
        if (mbeans !== null && mbeans.length > 0) {
          const result : any = await jolokia.getAttribute(mbeans[0], "version");
          if (result !== null)
            jettyVersion = result;
        }
      } catch (e) {}
  }

  config.about.productInfo.push(
    { name: 'Jetty',        value: jettyVersion },
    { name: 'GtMonitor',    value: gtMonitorVersion },
    { name: 'Artemis',      value: artemisVersion },
    { name: 'Java',         value: jdkVersion },
    { name: 'Camel',        value: camelVersion }
  );
  
  if (!config.about) config.about = {}
  // Disable specific plugins by specifying their path:
  config.disabledRoutes = disabledRoutes;

})

const graphtalkPlugins = [
  {component : GtMonitor,       id : "GtMonitor",       title : "GtMonitor",        help : GtMonitorHelp,   preferences : GtMonitorPreferences},
  {component : JobManager,      id : "JobManager",      title : "Job Manager",      help : JobManagerHelp,  preferences : JobManagerPreferences},
  {component : WebErrors,       id : "WebErrors",       title : "Web Errors",       help : WebErrorsHelp,   preferences : WebErrorsPreferences},
  {component : Batches,         id : "Batches",         title : "Batches",          help : BatchesHelp,     preferences : BatchesPreferences},
  {component : Counters,        id : "Counters",        title : "Counters",         help : CountersHelp,    preferences : CountersPreferences},
  {component : Security,        id : "Security",        title : "Security",         help : SecurityHelp,    preferences : SecurityPreferences},
]

const GraphTalk : HawtioPlugin = () => {

  log.info("Loading Graphtalk plugin(s) ...")

  graphtalkPlugins.forEach((plugin, pluginIndex) => {
    const pluginOrder = 101 + pluginIndex;
    hawtio.addPlugin({
      id          : plugin.id,
      title       : plugin.title,
      path        : "/" + plugin.id,
      component   : plugin.component,
      order       : pluginOrder,
//    headerItems : (plugin.id === "Security") ? [ExampleHeaderItem] : [],
      isActive    : async () => {
                      const context = await getContext();
                      const canAccess : {[key : string] : boolean} = {
                        "GtMonitor"         : context?.canAccessGtMonitor,
                        "JobManager"        : context?.canAccessJobManager,
                        "WebErrors"         : context?.canAccessWeberrors,
                        "Batches"           : context?.canAccessBatchManager,
                        "Counters"          : context?.canAccessCounters,
                        "Security"          : context?.canAccessSecurity
                      }
                      const canAccessPlugin = canAccess[plugin.id];
                      if (canAccessPlugin) {
                        try {
                          if (plugin.preferences) preferencesRegistry.add(plugin.id, plugin.title, plugin.preferences, pluginOrder)
                          if (plugin.help) helpRegistry.add(plugin.id, plugin.title, plugin.help, pluginOrder)
                        }
                        catch {}
                      }
                      return canAccessPlugin;
                    }
    })
  })
}

/* reorder Hawtio plugins */
configManager.getHawtconfig().then (
  (result : any) => {
    log.info("Reordering Hawtio plugin(s) ...")
    const plugins = hawtio.getPlugins()
    plugins.forEach ((plugin : any) => {
      const hawtioPlugin = hawtioPlugins.find((hawtioPlugin : any) => hawtioPlugin.id === plugin.id);
      if (hawtioPlugin && hawtioPlugin.order > -1)
        plugin.order = hawtioPlugin.order;
    })
  }
)

export default GraphTalk
