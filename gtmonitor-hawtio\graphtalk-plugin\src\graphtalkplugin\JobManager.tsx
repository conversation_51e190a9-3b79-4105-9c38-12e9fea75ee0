
import * as JulianDateTime from './JulianDateTime'

import { jmx, Logger, jolokiaService } from '@hawtio/react'

import { IJolokiaSimple } from '@jolokia.js/simple'

import {
  Badge,
  Bullseye,
  Button,
  DatePicker, TimePicker,
  EmptyState, EmptyStateVariant, EmptyStateHeader, EmptyStateBody, EmptyStateIcon,
  Divider,
  Drawer, DrawerPanelContent, DrawerContent, DrawerContentBody,
  ExpandableSection,
  Grid, GridItem,
  Icon,
  Modal, ModalVariant,
  Pagination,
  Split, SplitItem,
  Text, TextContent, TextInput, TextVariants,
  Title,
  Tooltip,
  isValidDate,
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionToggle, Dropdown, DropdownItem, DropdownList, MenuToggle, MenuToggleElement, Checkbox
} from '@patternfly/react-core'


import {
  Table,
  Thead,
  Tr,
  Th, ThProps,
  Tbody,
  Td,
  InnerScrollContainer
} from '@patternfly/react-table';

import { DownloadIcon, EraserIcon, SearchIcon, TrashIcon, PlayIcon, StopIcon, PauseIcon } from '@patternfly/react-icons/dist/esm/icons'

import React, { useEffect, useRef, useState } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import { getContext } from './Context';

import './GlobalPlugins.css';

import {
  jobManagerItemsMap,
  formNumbersList,
  statusList,
  AccordionSectionKeys,
  getJobInformationProp,
  getDeltaValue
} from "./JobManagerHelpers"
import './GlobalPlugins.css';
import ProcessControl from './ProcessControl';


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-jobmanager");

const logPrefix = "** JobManager ** ";

//log.setLevel(Logger.INFO);

log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Job Manager
// ********************************************************************************************************
// ********************************************************************************************************

const adminPath = "com.csc.gtmonitor.*:type=Hawtio";

const JobManager : React.FunctionComponent = () => {

  type JobManagerType = {
    retrievedData : any,
    computedData  : any
  }

  // ********************************************************************************************************

  const [jobManagerJobs, setJobManagerJobs] = useState<JobManagerType>({
    retrievedData   : false,
    computedData    : []
  })

  const [allJobManagerJobsCount, setAllJobManagerJobsCount] = useState<JobManagerType>({
    retrievedData   : false,
    computedData    : []
  })

  const [isProcessControlView, setisProcessControlView] = useState<boolean>(false);




  useEffect(() => {
  }, [isProcessControlView]);

  const handleCheckboxChange = (checked : boolean) => {
    setisProcessControlView(checked);
  };

  // ********************************************************************************************************

  const activeTabKey = useRef<string | number>(0);

  const expandedSectionsRef = useRef<string[]>([]);

  // ********************************************************************************************************

  const jobManagerPanelContentSize = useRef<any>("25%");

  // ********************************************************************************************************

  const defaultSortIndex = 0;
  const defaultSortDirection = 'desc';

  const activeSortIndex = useRef<any>(defaultSortIndex);
  const activeSortDirection = useRef<any>(defaultSortDirection);


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia : IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Search Criterias
  // ********************************************************************************************************
  // ********************************************************************************************************

  const searchExpanded = useRef<boolean>(true);
  const setSearchExpanded = (newSearchExpanded : boolean) => { searchExpanded.current = newSearchExpanded; }
  const isSearchExpanded = () => { return searchExpanded.current }
  const toggleSearchExpanded = () => { searchExpanded.current = !searchExpanded.current; }

  const defaultSearchStart            = 0;
  const defaultSearchNum              = 50;
  const defaultSearchCount            = 1;
  const defaultSearchId               = null;
  const defaultSearchUser             = null;
  const defaultSearchStatus           = null;
  const defaultSearchSubmittedDateMin = null;
  const defaultSearchSubmittedTimeMin = null;
  const defaultSearchSubmittedDateMax = null;
  const defaultSearchSubmittedTimeMax = null;
  const defaultSearchStartDateMin     = null;
  const defaultSearchStartTimeMin     = null;
  const defaultSearchStartDateMax     = null;
  const defaultSearchStartTimeMax     = null;

  const searchStart                   = useRef<number | null>(null);
  const searchNum                     = useRef<number | null>(defaultSearchNum);
  const searchCount                   = useRef<number | null>(null);
  const searchId                      = useRef<number | null>(null);
  const searchUser                    = useRef<string | null>(null);
  const searchStatus                  = useRef<string | null>(null);
  const searchSubmittedDateMin        = useRef<Date | null>(null);
  const searchSubmittedTimeMin        = useRef<Date | null>(null);
  const searchSubmittedDateMax        = useRef<Date | null>(null);
  const searchSubmittedTimeMax        = useRef<Date | null>(null);
  const searchStartDateMin            = useRef<Date | null>(null);
  const searchStartTimeMin            = useRef<Date | null>(null);
  const searchStartDateMax            = useRef<Date | null>(null);
  const searchStartTimeMax            = useRef<Date | null>(null);

  const searchReset = () => {
    searchStart.current               = null;
    searchNum.current                 = defaultSearchNum;
    searchCount.current               = null;
    searchId.current                  = null;
    searchUser.current                = null;
    searchStatus.current              = null;
    searchSubmittedDateMin.current    = null;
    searchSubmittedTimeMin.current    = null;
    searchSubmittedDateMax.current    = null;
    searchSubmittedTimeMax.current    = null;
    searchStartDateMin.current        = null;
    searchStartTimeMin.current        = null;
    searchStartDateMax.current        = null;
    searchStartTimeMax.current        = null;
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Pagination Variables
  // ********************************************************************************************************
  // ********************************************************************************************************

  const defaultPerPage = 20;

  const perPage = useRef<number>(defaultPerPage);

  const setPerPage = (newPerPage : number) => {
    perPage.current = newPerPage;
  }

  const getPerPage = () : number => {
    return perPage.current;
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  const retrieveJobManagerSignature = "retrieveJobManagerJobs(java.lang.String)";

  async function retrieveJobManagerJobs(jobManagerJobs : any) {
    //const args = {"sort" : "-num", "_num" : 5, "_attributes" : "start_time, status, actions" };
    const args : { [key : string] : string | number } = {"_attributes" : "start_time, status, actions, post_date, start_date"};

    const argStart            = (searchStart.current === null) ? defaultSearchStart : searchStart.current;
    const argNum              = (searchNum.current === null) ? defaultSearchNum : searchNum.current;
    const argCount            = (searchCount.current === null) ? defaultSearchCount : searchCount.current;
    const argId               = (searchId.current === null) ? defaultSearchId : searchId.current;
    const argUser             = (searchUser.current === null) ? defaultSearchUser : searchUser.current;
    const argStatus           = (searchStatus.current === null) ? defaultSearchStatus : searchStatus.current;
    const argSubmittedDateMin = (searchSubmittedDateMin.current === null) ? defaultSearchSubmittedDateMin : JulianDateTime.dateToJulian(searchSubmittedDateMin.current);
    const argSubmittedTimeMin = (searchSubmittedTimeMin.current === null) ? defaultSearchSubmittedTimeMin : JulianDateTime.timeToJulian(searchSubmittedTimeMin.current);
    const argSubmittedDateMax = (searchSubmittedDateMax.current === null) ? defaultSearchSubmittedDateMax : JulianDateTime.dateToJulian(searchSubmittedDateMax.current);
    const argSubmittedTimeMax = (searchSubmittedTimeMax.current === null) ? defaultSearchSubmittedTimeMax : JulianDateTime.timeToJulian(searchSubmittedTimeMax.current);
    const argStartDateMin     = (searchStartDateMin.current === null) ? defaultSearchStartDateMin : JulianDateTime.dateToJulian(searchStartDateMin.current);
    const argStartTimeMin     = (searchStartTimeMin.current === null) ? defaultSearchStartTimeMin : JulianDateTime.timeToJulian(searchStartTimeMin.current);
    const argStartDateMax     = (searchStartDateMax.current === null) ? defaultSearchStartDateMax : JulianDateTime.dateToJulian(searchStartDateMax.current);
    const argStartTimeMax     = (searchStartTimeMax.current === null) ? defaultSearchStartTimeMax : JulianDateTime.timeToJulian(searchStartTimeMax.current);

    if (argStart !== null && argStart > 0) args["_start"] = argStart; else args["_start"] = 0;
    if (argNum !== null && argNum > 0) args["_num"] = argNum;
    if (argCount !== null && argCount > 0) args["_count"] = argCount;
    if (argId !== null && argId > 0) args["num"] = argId;
    if (argUser !== null && argUser.length > 0) args["userId"] = argUser;
    if (argStatus !== null && argStatus.length > 0) args["status"] = argStatus;
    if (argSubmittedDateMin !== null && argSubmittedDateMin > 0) args["post_date_min"] = argSubmittedDateMin;
    if (argSubmittedTimeMin !== null && argSubmittedTimeMin > 0) args["post_time_min"] = argSubmittedTimeMin;
    if (argSubmittedDateMax !== null && argSubmittedDateMax > 0) args["post_date_max"] = argSubmittedDateMax;
    if (argSubmittedTimeMax !== null && argSubmittedTimeMax > 0) args["post_time_max"] = argSubmittedTimeMax;
    if (argStartDateMin !== null && argStartDateMin > 0) args["start_date_min"] = argStartDateMin;
    if (argStartTimeMin !== null && argStartTimeMin > 0) args["start_time_min"] = argStartTimeMin;
    if (argStartDateMax !== null && argStartDateMax > 0) args["start_date_max"] = argStartDateMax;
    if (argStartTimeMax !== null && argStartTimeMax > 0) args["start_time_max"] = argStartTimeMax;

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveJobManagerSignature;
          log.debug(logPrefix, "retrieveJobManagerJobs, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrieveJobManagerJobs, result: ", json);
          jobManagerJobs.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrieveJobManagerJobs, exception: ", e);
      }
    }
  }

  async function retrieveAllJobManagerCount(allJobManagerJobsCount : any) {
    const args : any = { "_num" : 0 };

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveJobManagerSignature;
          log.debug(logPrefix, "retrieveJobManagerJobs, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrieveJobManagerJobs, result: ", json);
          allJobManagerJobsCount.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrieveJobManagerJobs, exception: ", e);
      }
    }
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************


  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying Job Manager ****************************************************************************");

  // ********************************************************************************************************

  const JobManagerItems = (props : any) => {

    const stickyColumn = -1;   // no sticky column, else put the column index like 0 for the first one
    const [jobManagerDetails, setJobManagerDetails] = useState<any>(null);
    const [expandedSections, setExpandedSections] = useState<string[]>([]);

    const [activeSortRefresh, setActiveSortRefresh] = useState<boolean>(false);
    const toggleActiveSortRefresh = () => { setActiveSortRefresh(!activeSortRefresh); }

    const setActiveSortIndex = (sortIndex : any) => { activeSortIndex.current = sortIndex; toggleActiveSortRefresh(); }
    const getActiveSortIndex = () : any => { return activeSortIndex.current; }

    const setActiveSortDirection = (sortDirection : any) => { activeSortDirection.current = sortDirection; toggleActiveSortRefresh(); }
    const getActiveSortDirection = () : any => { return activeSortDirection.current; }

    let jobManagerJobItems : { [key : number] : any }[] = jobManagerJobs.computedData["job-list"]?.items;
    if (jobManagerJobItems === null || jobManagerJobItems === undefined) {
      jobManagerJobItems = [];
    }

    let sortedJobManagerItems = jobManagerJobItems;
    if (activeSortIndex !== null) {
      sortedJobManagerItems = jobManagerJobItems.sort((a, b) => {
        const column = jobManagerItemsMap[getActiveSortIndex()];
        const key    : any = column.key;
        const aValue : any = a[key];
        const bValue : any = b[key];
        if (typeof aValue === 'number') {
          // Numeric sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as number) - (bValue as number);
          }
          return (bValue as number) - (aValue as number);
        }
        else {
          // String sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as string).localeCompare(bValue as string);
          }
          return (bValue as string).localeCompare(aValue as string);
        }
      });
    }

    const getSortParams = (columnIndex : number) : ThProps['sort'] => ({
      sortBy : {
        index             : getActiveSortIndex(),
        direction         : getActiveSortDirection(),
        defaultDirection  : 'asc' // starting sort direction when first sorting a column. Defaults to 'asc'
      },
      onSort : (_event, index, direction) => {
        setActiveSortIndex(index);
        setActiveSortDirection(direction);
      },
      columnIndex
    });

    // ********************************************************************************************************

    const isJobManagerJobItemSelectable = (jobManagerJobItem : any) => jobManagerJobItem.num !== '0'; // Arbitrary logic for this example

    const selectableJobManagerItems = jobManagerJobItems.filter(isJobManagerJobItemSelectable);

    const [selectedJobManagerJobItemNums, setSelectedJobManagerJobItemNums] = useState<string[]>([]);

    const setJobManagerJobItemSelected = (jobManagerJobItem : any, isSelecting = true) =>
      setSelectedJobManagerJobItemNums((prevSelected) => {
        const otherSelectedJobManagerItemsNums = prevSelected.filter((w) => w !== jobManagerJobItem.num);
        return isSelecting && isJobManagerJobItemSelectable(jobManagerJobItem) ? [...otherSelectedJobManagerItemsNums, jobManagerJobItem.num]
          : otherSelectedJobManagerItemsNums;
      });

    const selectAllJobManagerItems = (isSelecting = true) =>
      setSelectedJobManagerJobItemNums(isSelecting ? selectableJobManagerItems.map((jobManagerJobItem : any) => jobManagerJobItem.num) : []);

    const areAllJobManagerItemsSelected = () => { return (selectedJobManagerJobItemNums.length === selectableJobManagerItems.length) };

    const areSeveralJobManagerItemsSelected = () => { return (selectedJobManagerJobItemNums.length > 0) };

    const isJobManagerItemSelected = (jobManagerJobItem : any) => selectedJobManagerJobItemNums.includes(jobManagerJobItem.num);

    const [recentSelectedRowIndex, setRecentSelectedRowIndex] = useState<number | null>(null);

    // ********************************************************************************************************

    const shiftKey      = useRef(false);
    const setShiftKey   = (state : boolean) => { shiftKey.current = state; }
    const isShiftKey    = () : boolean => { return shiftKey.current; }

    const controlKey    = useRef(false);
    const setControlKey = (state : boolean) => { controlKey.current = state; }
    const isControlKey  = () : boolean => { return controlKey.current; }

    // ********************************************************************************************************

    const onSelectJobManagerItem = (jobManagerJobItem : any, rowIndex : number, isSelecting : boolean) => {
      // If the user does control + selecting the checkboxes, then selected checkbox should be added to existing ones
      // If the user does shift + selecting the checkboxes, then all intermediate checkboxes should be selected
      if (isShiftKey() && recentSelectedRowIndex !== null) {
        const numberSelected = rowIndex - recentSelectedRowIndex;
        const intermediateIndexes =
          numberSelected > 0
            ? Array.from(new Array(numberSelected + 1), (_x, i) => i + recentSelectedRowIndex)
            : Array.from(new Array(Math.abs(numberSelected) + 1), (_x, i) => i + rowIndex);
        intermediateIndexes.forEach((index) => setJobManagerJobItemSelected(jobManagerJobItems[index], isSelecting));
      }
      else
        if (isControlKey())
          setJobManagerJobItemSelected(jobManagerJobItem, isSelecting);
        else
          if (isJobManagerJobItemSelectable(jobManagerJobItem) && !isJobManagerItemSelected(jobManagerJobItem))
            setSelectedJobManagerJobItemNums([jobManagerJobItem.num]);
          else
            setSelectedJobManagerJobItemNums([]);

      setRecentSelectedRowIndex(rowIndex);
    };

    async function retrieveJobManagerDetails(jobManagerJobItem : any) : Promise<any> {
      log.debug(logPrefix, "retrieveJobManagerDetails : " + jobManagerJobItem?.num + " / " + jobManagerJobItem)

      const JobManagerJobNum = jobManagerJobItem?.num;

      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }

      let json : any = null;
      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "retrieveJobManagerJob(java.lang.String,java.lang.String)";
          const result = await jolokia.execute(mbean, operation, JobManagerJobNum, "");
          log.debug(logPrefix, "retrieveJobManagerDetails, result: ", result);
          json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
        }
      }

      log.debug(logPrefix, "retrieveJobManagerDetails, json: ", json);

      let job_list = (json === undefined || json === null) ? null : json.job;

      if (job_list === null) {
        log.debug(logPrefix, "retrieveJobManagerDetails, cannot retrieve job details for: ", JobManagerJobNum);
        job_list = -1;
      }
      else
        log.debug(logPrefix, "retrieveJobManagerDetails, job details retrieved for: ", JobManagerJobNum);

      setJobManagerDetails(job_list);
    }

    // ********************************************************************************************************

    useEffect(() => {
      const nbSelected = selectedJobManagerJobItemNums.length;

      if (nbSelected === 1) {
        const jobManagerJobItem : any = jobManagerJobItems.find((item : any) => (item.num === selectedJobManagerJobItemNums[0]));
        retrieveJobManagerDetails(jobManagerJobItem)
          .then(() => {
            log.debug(logPrefix, "retrieveJobManagerDetails OK");
          })
          .catch(error => {
            log.debug(logPrefix, "retrieveJobManagerDetails exception: ", error);
            setJobManagerDetails(null);
          })
      }
      else
        setJobManagerDetails(null);
    }, [selectedJobManagerJobItemNums]);


    useEffect(() => {
      if (jobManagerDetails && jobManagerDetails !== -1) {
        if (expandedSectionsRef.current.length === 0) {
          setExpandedSections([
            AccordionSectionKeys.JOB_INFORMATION,
            AccordionSectionKeys.USER_PARAMETERS,
            AccordionSectionKeys.ERROR
          ]);
        } else {
          setExpandedSections(expandedSectionsRef.current);
        }
      } else {
        setExpandedSections([]);
      }
    }, [jobManagerDetails]);

    const jobInformationProp = getJobInformationProp(jobManagerDetails);

    // ********************************************************************************************************

    useEffect(() => {
      const onKeyDown = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(true);
        if (e.key === 'Control')
          setControlKey(true);
      };

      const onKeyUp = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(false);
        if (e.key === 'Control')
          setControlKey(false);
      };

      document.addEventListener('keydown', onKeyDown);
      document.addEventListener('keyup', onKeyUp);

      return () => {
        document.removeEventListener('keydown', onKeyDown);
        document.removeEventListener('keyup', onKeyUp);
      };
    }, []);

    // ********************************************************************************************************

    // Selected rows are tracked by the job manager job num field from each row.
    // This is to prevent state from being based on the row order index which is subject to sorting.

    // ********************************************************************************************************

    const JobManagerDetails = () => {

      const JobManagerJobProperty = ({name, value, altvalue, index, style = {}} : any) => {
        const rowClass = index % 2 === 0 ? "accordion-row-odd" : "accordion-row-even";

        return (
          <Tr tabIndex = {-1} className = {rowClass}>
            <Td tabIndex = {-1} modifier = "fitContent">
              <p
                tabIndex  = {-1}
                className = "accordion-name-cell"
                style     = {{fontWeight : style?.fontWeight || 'bold'}}
              >
                {name}
              </p>
            </Td>
            <Td tabIndex = {-1} modifier = "fitContent" style = {{paddingLeft : "10px"}}>
              <p
                tabIndex  = {-1}
                className = "accordion-value-cell"
                style     = {{color : style.color || 'inherit'}}
              >
                {value}
              </p>
            </Td>
            {altvalue && (
              <Td tabIndex = {-1} modifier = "fitContent">
                <p
                  tabIndex  ={-1}
                  className = "accordion-altvalue-cell"
                >
                  ({altvalue})
                </p>
              </Td>
            )}
          </Tr>
        );
      };


      // ********************************************************************************************************

      const toggleSection = (id : string) => {
        setExpandedSections((prev) => {
          const newExpandedSections = prev.includes(id)
            ? prev.filter((section) => section !== id)
            : [...prev, id];

          expandedSectionsRef.current = newExpandedSections;
          return newExpandedSections;
        });
      };

      return (
        <GraphTalkComponentDiv hasScroll>
          {isProcessControlView ? (
            <Accordion asDefinitionList togglePosition = "start">
              <AccordionItem>
                <AccordionToggle
                  tabIndex    = {-1}
                  onClick     = {() => toggleSection('ACCORDION_1')}
                  isExpanded  = {expandedSections.includes('ACCORDION_1')}
                  id          = "ACCORDION_1"
                >
                  Accordion 1
                </AccordionToggle>
                <AccordionContent isHidden = {!expandedSections.includes('ACCORDION_1')}>
                  <JobManagerJobProperty name = "No Accordion 1 Information" style = {{fontWeight : 'normal'}}/>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem>
                <AccordionToggle
                  tabIndex    = {-1}
                  onClick     = {() => toggleSection('ACCORDION_2')}
                  isExpanded  = {expandedSections.includes('ACCORDION_2')}
                  id          = "ACCORDION_2"
                >
                  Accordion 2
                </AccordionToggle>
                <AccordionContent isHidden = {!expandedSections.includes('ACCORDION_2')}>
                  <JobManagerJobProperty name = "No Accordion 2 Information" style = {{fontWeight : 'normal'}}/>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ) : (
            <Accordion asDefinitionList togglePosition = "start">
              <AccordionItem>
                <AccordionToggle
                  tabIndex    = {-1}
                  onClick     = {() => toggleSection(AccordionSectionKeys.JOB_INFORMATION)}
                  isExpanded  = {expandedSections.includes(AccordionSectionKeys.JOB_INFORMATION)}
                  id          = {AccordionSectionKeys.JOB_INFORMATION}
                >
                  Job Information
                </AccordionToggle>
                <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.JOB_INFORMATION)}>
                  {jobManagerDetails ? (
                    jobInformationProp.map((item, index) => (
                      <JobManagerJobProperty
                        key     = {item.name}
                        index   = {index}
                        name    = {item.name}
                        value   = {<>{item.icon} {item.value}</>}
                        style   = {{color : item.color || "inherit"}}
                      />
                    ))
                  ) : (
                    <JobManagerJobProperty name = "No Job Information" style = {{fontWeight : 'normal'}}/>
                  )}
                </AccordionContent>
              </AccordionItem>

              <AccordionItem>
                <AccordionToggle
                  tabIndex    = {-1}
                  onClick     = {() => toggleSection(AccordionSectionKeys.USER_PARAMETERS)}
                  isExpanded  = {expandedSections.includes(AccordionSectionKeys.USER_PARAMETERS)}
                  id          = {AccordionSectionKeys.USER_PARAMETERS}
                >
                  User Parameters
                </AccordionToggle>
                <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.USER_PARAMETERS)}>
                  <JobManagerJobProperty name = "No User Parameters" style = {{fontWeight : 'normal'}}/>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem>
                <AccordionToggle
                  tabIndex    = {-1}
                  onClick     = {() => toggleSection(AccordionSectionKeys.ERROR)}
                  isExpanded  = {expandedSections.includes(AccordionSectionKeys.ERROR)}
                  id          = {AccordionSectionKeys.ERROR}
                >
                  Error
                </AccordionToggle>
                <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.ERROR)}>
                  <JobManagerJobProperty name = "No Error" style = {{fontWeight : 'normal'}}/>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </GraphTalkComponentDiv>
      )
    }

    // ********************************************************************************************************

    const displayValue = (value : any, type : string) : string => {
      switch (type) {
        case "int"        : return value.toString();
        case "string"     : return value;
        case "julianTime" : return JulianDateTime.formatJulianAsTime(value);
        case "julianDate" : return JulianDateTime.formatJulianAsDate(value);
        default : return value;  // let it be automatically converted ...
      }
    }

    // ********************************************************************************************************

    const JobManagerJobItem = (props : any) => {
      const jobManagerJobItem = props.jobManagerJobItem;
      const rowIndex = props.rowIndex;

      return (
        <Tr tabIndex = {-1} style = {{cursor : 'pointer', fontFamily : 'monospace'}} onClick = {() => onSelectJobManagerItem(jobManagerJobItem, rowIndex, true)}>
          {
            (stickyColumn >= 0) ?
              <Td
                tabIndex  = {-1}
                className = "custom-compact-table_padding"
                select    = {{
                  rowIndex    : rowIndex,
                  onSelect    : (_event, isSelecting) => onSelectJobManagerItem(jobManagerJobItem, rowIndex, isSelecting),
                  isSelected  : isJobManagerItemSelected(jobManagerJobItem),
                  isDisabled  : !isJobManagerJobItemSelectable(jobManagerJobItem)
                }}
              />
              :
              <Td
                tabIndex  = {-1}
                className = "custom-compact-table_padding"
                select    = {{
                  rowIndex    : rowIndex,
                  onSelect    : (_event, isSelecting) => onSelectJobManagerItem(jobManagerJobItem, rowIndex, isSelecting),
                  isSelected  : isJobManagerItemSelected(jobManagerJobItem),
                  isDisabled  : !isJobManagerJobItemSelectable(jobManagerJobItem)
                }}
              />
          }
          {
            jobManagerItemsMap.map((column : any) =>
              <Td
                tabIndex  = {-1}
                className = "custom-compact-table_padding"
                key       = {column.key}
                modifier  = "truncate"
                dataLabel = {column.key}
                style     = {{fontSize : 'inherit', textAlign : column.align, padding : column.padding}}
              >
                {displayValue(jobManagerJobItem[column.key], column.type)}
              </Td>
            )
          }
        </Tr>
      )
    }

    // ********************************************************************************************************

    const handleOnPanelContentResize = (_event : MouseEvent | TouchEvent | React.KeyboardEvent, newWidth : number, id : string) => {
      jobManagerPanelContentSize.current = newWidth + "px";
    };

    const jobManagerPanelContent = (
      <DrawerPanelContent
        isResizable
        onResize    = {handleOnPanelContentResize}
        id          = "drawerPanelContent"
        defaultSize = {jobManagerPanelContentSize.current}
        minSize     = "10%"
        maxSize     = "50%"
      >
        <DrawerContentBody tabIndex = {-1}>
          <JobManagerDetails/>
        </DrawerContentBody>
      </DrawerPanelContent>
    );

    // ********************************************************************************************************

    const JobManagerJobDate = (props : any) => {
      const ddMMyyyyFormat = (date : Date) =>
        (date === undefined || date === null) ? "" : `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;

      const datePickerRef = useRef<any>();

      const dateParse = (value : string): any => {
        if (value === undefined || value === null)
          return null;
        const splittedDate = value.split('/').map(Number);
        if (splittedDate.length === 3) {
          const date = new Date(Date.UTC(splittedDate[2], splittedDate[1] - 1, splittedDate[0]));
          date.setHours(0, 0, 0);
          if (isValidDate(date) && value === ddMMyyyyFormat(date)) {
            return date
          }
        }
        return null;
      }

      const changeDate = (value : string, date? : Date | undefined) => {
        if (value !== undefined && value !== null && value.length > 0 && date !== undefined && isValidDate(date) && ddMMyyyyFormat(date) === value)
          props.dateHandler(date);
        else
          props.dateHandler(null);
      }

      const onDateChange = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        if (datePickerRef.current?.isCalendarOpen)
          changeDate(value, date);
      }

      const onDateBlur = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        changeDate(value, date);
      }

      return (
        <DatePicker
          ref         = {datePickerRef}
          id          = {props.id}
          dateFormat  = {ddMMyyyyFormat}
          placeholder = "DD/MM/YYYY"
          dateParse   = {(value : string) => dateParse(value)}
          value       = {(props.date === undefined || props.Date === null) ? undefined : ddMMyyyyFormat(props.date)}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : { fontSize : "12px", padding : "10px 0px 0px 10px"}}}
          onChange    = {onDateChange}
          onBlur      = {onDateBlur}
        />
      )
    }

    // ********************************************************************************************************

    const JobManagerJobTime = (props : any) => {
      const onTimeChange = (event : React.FormEvent<HTMLInputElement>, time : string, hour? : number | undefined, minute? : number | undefined, seconds? : number | undefined, isValid? : boolean | undefined) => {
        if (isValid && time !== null && time.length > 0 && hour != null && minute !== null && seconds !== null) {
          const dateTime = new Date(0);
          dateTime.setHours(hour ? hour : 0, minute ? minute : 0, seconds ? seconds : 0);
          props.timeHandler(dateTime);
        }
        else
          props.timeHandler(null);
      }

      return (
        <TimePicker
          id      = {props.id}
          placeholder = 'HH:mm:ss'
          stepMinutes = {15}
          time        = {(props.time === undefined || props.time === null) ? undefined : props.time}
          onChange    = {onTimeChange}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : { fontSize : "12px", padding : "5px 0px 0px 10px" }}}
          is24Hour
          includeSeconds
        />
      )
    }

    // ********************************************************************************************************

    const [heightDelta, setHeightDelta] = useState<number>(
      getDeltaValue(isSearchExpanded())
    );

    const refreshHeightDelta = () => {
      setHeightDelta(getDeltaValue(isSearchExpanded()));
    };

    // ********************************************************************************************************

    const JobManagerSearch = () => {
      const [searchRefresh, setSearchRefresh] = useState<boolean>(false);
      const [formRestartDate, setFormRestartDate] = useState<any>(null);
      const forceSearchRefresh = () => { setSearchRefresh(!searchRefresh) }
      const [isNumbersDropdownOpen, setIsNumbersDropdownOpen] = React.useState(false);
      const [isStatusDropdownOpen, setIsStatusDropdownOpen] = React.useState(false);


      const [formSearchId, setFormSearchId] = useState<number | null>((searchId.current !== undefined) ? searchId.current : defaultSearchId);
      const [formSearchNum, setFormSearchNum] = useState<number | null>((searchNum.current !== undefined) ? searchNum.current : null);
      const [formSearchUser, setFormSearchUser] = useState<string | null>((searchUser.current !== undefined) ? searchUser.current : null);
      const [formSearchStatus, setFormSearchStatus] = useState<string | null>((searchStatus.current !== undefined) ? searchStatus.current : null);
      const [formSearchSubmittedDateMin, setFormSearchSubmittedDateMin] = useState<Date | null>((searchSubmittedDateMin.current !== undefined) ? searchSubmittedDateMin.current : null);
      const [formSearchSubmittedTimeMin, setFormSearchSubmittedTimeMin] = useState<Date | null>((searchSubmittedTimeMin.current !== undefined) ? searchSubmittedTimeMin.current : null);
      const [formSearchSubmittedDateMax, setFormSearchSubmittedDateMax] = useState<Date | null>((searchSubmittedDateMax.current !== undefined) ? searchSubmittedDateMax.current : null);
      const [formSearchSubmittedTimeMax, setFormSearchSubmittedTimeMax] = useState<Date | null>((searchSubmittedTimeMax.current !== undefined) ? searchSubmittedTimeMax.current : null);
      const [formSearchStartDateMin, setFormSearchStartDateMin] = useState<Date | null>((searchStartDateMin.current !== undefined) ? searchStartDateMin.current : null);
      const [formSearchStartTimeMin, setFormSearchStartTimeMin] = useState<Date | null>((searchStartTimeMin.current !== undefined) ? searchStartTimeMin.current : null);
      const [formSearchStartDateMax, setFormSearchStartDateMax] = useState<Date | null>((searchStartDateMax.current !== undefined) ? searchStartDateMax.current : null);
      const [formSearchStartTimeMax, setFormSearchStartTimeMax] = useState<Date | null>((searchStartTimeMax.current !== undefined) ? searchStartTimeMax.current : null);

      const handleFormSearchSubmittedDateMin = (date : any) => {
        setFormSearchSubmittedDateMin((date !== undefined && date !== null) ? date : defaultSearchSubmittedDateMin);
      }

      const handleFormSearchSubmittedTimeMin = (time : any) => {
        setFormSearchSubmittedTimeMin((time !== undefined && time !== null) ? time : defaultSearchSubmittedTimeMin);
      }

      const handleFormSearchSubmittedDateMax = (date : any) => {
        setFormSearchSubmittedDateMax((date !== undefined && date !== null) ? date : defaultSearchSubmittedDateMax);
      }

      const handleFormSearchSubmittedTimeMax = (time : any) => {
        setFormSearchSubmittedTimeMax((time !== undefined && time !== null) ? time : defaultSearchSubmittedTimeMax);
      }

      const handleFormSearchStartDateMin = (date : any) => {
        setFormSearchStartDateMin((date !== undefined && date !== null) ? date : defaultSearchStartDateMin);
      }

      const handleFormSearchStartTimeMin = (time : any) => {
        setFormSearchStartTimeMin((time !== undefined && time !== null) ? time : defaultSearchStartTimeMin);
      }

      const handleFormSearchStartDateMax = (date : any) => {
        setFormSearchStartDateMax((date !== undefined && date !== null) ? date : defaultSearchStartDateMax);
      }

      const handleFormSearchStartTimeMax = (time : any) => {
        setFormSearchStartTimeMax((time !== undefined && time !== null) ? time : defaultSearchStartTimeMax);
      }

      const formSearchReset = () => {
        setFormSearchNum(defaultSearchNum);
        setFormSearchId(defaultSearchId);
        setFormSearchUser(defaultSearchUser);
        setFormSearchStatus(defaultSearchStatus);
        setFormSearchSubmittedDateMin(defaultSearchSubmittedDateMin);
        setFormSearchSubmittedTimeMin(defaultSearchSubmittedTimeMin);
        setFormSearchSubmittedDateMax(defaultSearchSubmittedDateMax);
        setFormSearchSubmittedTimeMax(defaultSearchSubmittedTimeMax);
        setFormSearchStartDateMin(defaultSearchStartDateMin);
        setFormSearchStartTimeMin(defaultSearchStartTimeMin);
        setFormSearchStartDateMax(defaultSearchStartDateMax);
        setFormSearchStartTimeMax(defaultSearchStartTimeMax);
        computeJobManagerJobs();
      }

      const onSearchSubmit = () => {
        searchNum.current = formSearchNum ? formSearchNum : defaultSearchNum;
        searchId.current = formSearchId ? formSearchId : defaultSearchId;
        searchUser.current = formSearchUser ? formSearchUser : defaultSearchUser;
        searchStatus.current = formSearchStatus ? formSearchStatus : defaultSearchStatus;
        searchSubmittedDateMin.current = formSearchSubmittedDateMin ? formSearchSubmittedDateMin : defaultSearchSubmittedDateMin;
        searchSubmittedTimeMin.current = formSearchSubmittedTimeMin ? formSearchSubmittedTimeMin : defaultSearchSubmittedTimeMin;
        searchSubmittedDateMax.current = formSearchSubmittedDateMax ? formSearchSubmittedDateMax : defaultSearchSubmittedDateMax;
        searchSubmittedTimeMax.current = formSearchSubmittedTimeMax ? formSearchSubmittedTimeMax : defaultSearchSubmittedTimeMax;
        searchStartDateMin.current = formSearchStartDateMin ? formSearchStartDateMin : defaultSearchStartDateMin;
        searchStartTimeMin.current = formSearchStartTimeMin ? formSearchStartTimeMin : defaultSearchStartTimeMin;
        searchStartDateMax.current = formSearchStartDateMax ? formSearchStartDateMax : defaultSearchStartDateMax;
        searchStartTimeMax.current = formSearchStartTimeMax ? formSearchStartTimeMax : defaultSearchStartTimeMax;
        computeJobManagerJobs();
      }

      const onSearchReset = () => {
        searchReset();
        formSearchReset();
        forceSearchRefresh();
      }

      const onToggleSearchExpanded = (_event : React.MouseEvent, isExpanded : boolean) => {
        setSearchExpanded(isExpanded);
        forceSearchRefresh();
        refreshHeightDelta();
      };

      const onSearchNumChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {

        const count = (allJobManagerJobsCount.computedData["job-list"]?.count) ? allJobManagerJobsCount.computedData["job-list"].count : null;
        setFormSearchNum(
          value !== undefined && value !== null && value !== "Infinity"
            ? parseInt(value)
            : count !== null
              ? count
              : defaultSearchNum
        );
      }

      const onSearchIdChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchId((value !== undefined && value !== null) ? parseInt(value) : defaultSearchId);
      }

      const onSearchUserChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchUser((value !== undefined && value !== null) ? value : defaultSearchUser);
      }

      const onSearchStatusChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchStatus((value !== undefined && value !== null) ? value : defaultSearchStatus);
      }


      async function retrievJobManagerJobsRestartDate(newDateArg? : any) {
        const args : any = {"_attributes" : "_none, uri", "_count" : 0, "_num" : 1, "_start" : 0 };
        const mergedArgs = {...args, ...newDateArg};
        await refreshJolokia();

        let json : any = null;
        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = retrieveJobManagerSignature;
            log.debug(logPrefix, "retrieveJobManagerJobs, execute : ", JSON.stringify(mergedArgs));
            const result = await jolokia.execute(mbean, operation, JSON.stringify(mergedArgs));
            json = (result === null) ? null : JSON.parse(result.toString());
          }
        }

        log.info(logPrefix, "retrievJobManagerJobsRestartDate, json: ", json);

        let restart_date = (json?.["job-list"]?.items === undefined || json?.["job-list"]?.items === null || json?.["job-list"]?.items[0] === undefined || json?.["job-list"]?.items[0] === null) ? null : json["job-list"].items[0].startDate;
        log.info(logPrefix, "retrievJobManagerJobsRestartDate, restart_date: ", restart_date);

        setFormRestartDate(restart_date);

        log.info(logPrefix, "retrievJobManagerJobsRestartDate, formRestartDate: ", formRestartDate);
      }

      useEffect(() => {
        if (formRestartDate !== null) {
          log.info(logPrefix, "formRestartDate has been updated: ", formRestartDate);
          onToday();
        }
      }, [formRestartDate]);


      const onToday = () => {
        const today = new Date();

        let currentDayStart = new Date();
        let currentDayEnd   = new Date();

        if (typeof formRestartDate === "number") {
          //const args : any = { "restart_date" : formRestartDate, "_attributes" : "step_counter, error_counter, warning_counter" }
          handleFormSearchSubmittedDateMax(JulianDateTime.julianToDate(formRestartDate));
          searchSubmittedDateMax.current = JulianDateTime.julianToDate(formRestartDate);
          handleFormSearchSubmittedDateMin(JulianDateTime.julianToDate(formRestartDate));
          searchSubmittedDateMin.current = JulianDateTime.julianToDate(formRestartDate);
          computeJobManagerJobs()
        }
        else {
          currentDayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          currentDayEnd   = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          handleFormSearchSubmittedDateMin(currentDayStart);
          handleFormSearchSubmittedDateMax(currentDayEnd);
          searchSubmittedDateMin.current = currentDayStart;
          searchSubmittedDateMax.current = currentDayEnd;
          computeJobManagerJobs();
        }
      };

      const onPrevious = () => {
        let julianDateMax : number | null;
        let newDateMax : number | null;
        if (searchSubmittedDateMax.current instanceof Date && !isNaN(searchSubmittedDateMax.current.getTime())) {
          julianDateMax = JulianDateTime.dateToJulian(searchSubmittedDateMax.current);
          newDateMax = julianDateMax - 1;
        } else {
          julianDateMax = JulianDateTime.dateToJulian(new Date());
          newDateMax = julianDateMax - 1;
        }
        const arg : any = { "date_max" : newDateMax, "_sort" : "-startDate" };
        retrievJobManagerJobsRestartDate(arg);

      };

      const onNext = () => {
        let julianDateMin : number | null;
        let newDateMin : number | null;
        if (searchSubmittedDateMin.current instanceof Date && !isNaN(searchSubmittedDateMin.current.getTime())) {
          julianDateMin = JulianDateTime.dateToJulian(searchSubmittedDateMin.current);
          newDateMin = julianDateMin + 1;;
          const arg : any = { "date_min" : newDateMin, "_sort" : "startDate" };
          retrievJobManagerJobsRestartDate(arg);
        } else {
          onToday();
        }

      };

      const PrevNextTodayButtons = () => {
        return (
          <>
            <GridItem tabIndex = {-1} span = {2}/>
            <GridItem tabIndex = {-1} span = {1}>
              <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onPrevious()}
                  >
                    &lt; Prev.
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onNext()}
                  >
                    Next &gt;
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onToday()}
                  >
                    Today
                  </Button>
                </SplitItem>
              </Split>
            </GridItem>
          </>
        );
      }

      const selectedNumbersLabel = formNumbersList.find((j) => j.value === formSearchNum)?.content || "All";
      const selectedStatusLabel = statusList.find((s) => s.value === formSearchStatus)?.content || "Select Status";

      const onFormNumbersSelect = (_event : React.MouseEvent<Element, MouseEvent> | undefined, value : string | number | undefined) => {
        if (typeof value === "number") {
          onSearchNumChange({} as React.FormEvent<HTMLInputElement>, value.toString());
        }
        setIsNumbersDropdownOpen(false);
      };

      const onStatusSelect = (_event : React.MouseEvent<Element, MouseEvent> | undefined, value : string | number | undefined) => {
        if (typeof value === "string") {
          onSearchStatusChange({} as React.FormEvent<HTMLInputElement>, value);
        }
        setIsStatusDropdownOpen(false);
      };

      ///////////////////
      //////////////////
      //////////////////
      return (
        <div style = {{position : 'relative'}}>
          <ExpandableSection toggleText = "Search Criteria" isIndented isExpanded = {isSearchExpanded()} onToggle = {onToggleSearchExpanded}>
            <Grid tabIndex = {-1} hasGutter = {false}>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>Number of jobs</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2} >
                    <Dropdown
                      isOpen        = {isNumbersDropdownOpen}
                      onSelect      = {onFormNumbersSelect}
                      onOpenChange  = {(isOpen : boolean) => setIsNumbersDropdownOpen(isOpen)}
                      toggle        = {(toggleRef : React.Ref<MenuToggleElement>) => (
                        <MenuToggle className = "font-size-13" ref = {toggleRef} onClick = {() => setIsNumbersDropdownOpen(!isNumbersDropdownOpen)} isExpanded = {isNumbersDropdownOpen}>
                          {selectedNumbersLabel}
                        </MenuToggle>
                      )}
                      ouiaId        = "JobsDropdown"
                      shouldFocusToggleOnSelect
                    >
                      <DropdownList>
                        {
                          formNumbersList.map((option, index) => (
                            <DropdownItem key = {index} value = {option.value}>
                              {option.content}
                            </DropdownItem>
                          ))
                        }
                      </DropdownList>
                    </Dropdown>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {4}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>{getAllJobManagerJobsCount()}</Text>
                    </TextContent>
                  </GridItem>
                  {/* <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>Submitted date between</Text>
                    </TextContent>
                  </GridItem>                  
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobDate id = "searchSubmittedDateMin" date = {formSearchSubmittedDateMin} dateHandler = {handleFormSearchSubmittedDateMin}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobTime id = "searchSubmittedTimeMin" time = {formSearchSubmittedTimeMin} timeHandler = {handleFormSearchSubmittedTimeMin}/>
                      </SplitItem>
                    </Split>
                  </GridItem> */}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>Job Id</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextInput
                      className   = "font-size-13"
                      id          = "searchId"
                      type        = "number"
                      value       = {formSearchId ? formSearchId : ""}
                      onChange    = {onSearchIdChange}
                    />
                  </GridItem>
                  {/* <GridItem tabIndex = {-1} span = {4}/>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "search_box_titles_size_padding_60" style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>and</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobDate id = "searchSubmittedDateMax" date = {formSearchSubmittedDateMax} dateHandler = {handleFormSearchSubmittedDateMax}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobTime id = "searchSubmittedTimeMax" time = {formSearchSubmittedTimeMax} timeHandler = {handleFormSearchSubmittedTimeMax}/>
                      </SplitItem>
                    </Split>
                  </GridItem> */}

                </Grid>
              </GridItem>

              {/* <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  
                  {isSearchExpanded() && <PrevNextTodayButtons/>}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem> */}
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>User</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {5}>
                    <TextInput
                      className = "font-size-13"
                      id        = "searchUser"
                      type      = "text"
                      value     = {formSearchUser ? formSearchUser : ""}
                      onChange  = {onSearchUserChange}
                    />
                  </GridItem>
                  {/* <GridItem tabIndex = {-1} span = {1}/>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>Start date between</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobDate id = "searchStartDateMin" date = {formSearchStartDateMin} dateHandler = {handleFormSearchStartDateMin}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobTime id = "searchStartTimeMin" time = {formSearchStartTimeMin} timeHandler = {handleFormSearchStartTimeMin}/>
                      </SplitItem>
                    </Split>
                  </GridItem> */}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className=  "font-size-13" component = {TextVariants.p}>Status</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {5}>
                    <Dropdown
                      isOpen        = {isStatusDropdownOpen}
                      onSelect      = {onStatusSelect}
                      onOpenChange  = {(isOpen : boolean) => setIsStatusDropdownOpen(isOpen)}
                      toggle        = {(toggleRef : React.Ref<MenuToggleElement>) => (
                        <MenuToggle className = "font-size-13" ref = {toggleRef} onClick = {() => setIsStatusDropdownOpen(!isStatusDropdownOpen)} isExpanded = {isStatusDropdownOpen} >
                          {selectedStatusLabel}
                        </MenuToggle>
                      )}
                      ouiaId        = "DynamicDropdown"
                      shouldFocusToggleOnSelect
                    >
                      <DropdownList>
                      {
                        statusList.map((option, index) => (
                          <DropdownItem key = {index} value = {option.value}>
                            {option.content}
                          </DropdownItem>
                        ))
                      }
                      </DropdownList>
                    </Dropdown>
                  </GridItem>
                  {/* <GridItem tabIndex = {-1} span = {1}/>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "search_box_titles_size_padding_60" style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>and</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobDate id = "searchStartDateMax" date = {formSearchStartDateMax} dateHandler = {handleFormSearchStartDateMax}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <JobManagerJobTime id = "searchStartTimeMax" time = {formSearchStartTimeMax} timeHandler = {handleFormSearchStartTimeMax}/>
                      </SplitItem>
                    </Split>
                  </GridItem> */}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {12}>
                <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <SplitItem tabIndex = {-1} isFilled>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                    <Button
                      className   = "font-size-13"
                      variant     = "primary"
                      size        = "sm"
                      onClick     = {() => onSearchSubmit()}
                      icon        = {<SearchIcon className = "font-size-13"/>}
                    >
                      Search
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                    <Button
                      className   = "font-size-13"
                      variant     = "secondary"
                      size        = "sm"
                      onClick     = {() => onSearchReset()}
                      icon        = {<EraserIcon className = "font-size-13"/>}
                    >
                      Reset
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                  </SplitItem>
                </Split>
              </GridItem>

            </Grid>
          </ExpandableSection>


          <div className = "top-right-corner">
            {/* <div style = {{position : 'relative'}}>
              {!isSearchExpanded() && (
                <div className = "prev-next-today-container">
                  <PrevNextTodayButtons/>
                </div>
              )}
            </div> */}
            <div className = "checkbox-title-container">
              <TextContent tabIndex = {-1} className = "day_summary_style">
                <Text tabIndex = {-1} component = {TextVariants.p}>
                  Process Control
                </Text>
              </TextContent>
            </div>
            <div className = "checkbox-container">
              <Checkbox
                id        = "checkbox-reversed"
                name      = "checkbox-reversed"
                onChange  = {(_, checked) => handleCheckboxChange(checked)}
                isChecked = {isProcessControlView}
                className = "font-size-12"
              />
            </div>
          </div>
          {/* <div className = "top-right-corner">
              <div style = {{position : 'relative'}}>
                {!isSearchExpanded() && (
                  <div className = "prev-next-today-container">
                    <PrevNextTodayButtons/>
                  </div>
                )}
              </div>            
            </div> */}
        </div>
      )
    }

    const JobManagerJobsTable = () => {
      return (
        <div>
          <Divider tabIndex = {-1} component = "hr" className = "padding-top-10"/>
          <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
            <SplitItem tabIndex = {-1} className = "padding-left-10">
              <TextContent tabIndex = {-1}>
                <Text tabIndex = {-1} className = "font-size-13">Selected Jobs:</Text>
              </TextContent>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <Badge tabIndex = {-1} className = "font-size-12">{selectedJobManagerJobItemNums.length}</Badge>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              {(jobManagerDetails?.actions & 3) !== 0 && (
                <JobManagerSuspendResume/>
              )}
            </SplitItem>
            <SplitItem>
              {(jobManagerDetails?.actions & 4) === 4 && (
                <JobManagerCancel/>
              )}
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              {(jobManagerDetails?.actions & 8) === 8 && (
                <JobManagerJobDelete/>
              )}
            </SplitItem>
            {/* <SplitItem tabIndex = {-1}>
              <JobManagerJobDownload/>
            </SplitItem> */}
            <SplitItem tabIndex = {-1} isFilled>
              <JobManagerJobsPagination/>
            </SplitItem>
          </Split>
          <GraphTalkComponentDiv delta = {heightDelta}>
            <InnerScrollContainer>
              <Table
                tabIndex    = {-1}
                className   = "custom-compact-table"
                isStriped   = {sortedJobManagerItems.length > 0}
                aria-label  = "Job Manager table"
                variant     = "compact"
                borders     = {false}
                isStickyHeader
              >
                <Thead tabIndex = {-1}>
                  <Tr tabIndex = {-1}>
                    <Th
                      tabIndex          = {-1}
                      select            = {{
                        onSelect : (_event, isSelecting) => selectAllJobManagerItems(isSelecting),
                        isSelected : areSeveralJobManagerItemsSelected()
                      }}
                      aria-label        = "Job Manager Table Select "
                      isStickyColumn    = {(stickyColumn >= 0)}
                      stickyMinWidth    = {(stickyColumn >= 0) ? "auto" : undefined}
                      stickyLeftOffset  = {(stickyColumn >= 0) ? "0px" : undefined}
                    />
                    {
                      jobManagerItemsMap.map((column : any, columnIndex : number) =>
                        <Th
                          tabIndex          = {-1}
                          key               = {column.key}
                          className         = "pf-v5-c-table__th truncate-header"
                          isStickyColumn    = {(columnIndex === stickyColumn)}
                          stickyMinWidth    = {(columnIndex === stickyColumn) ? "auto" : undefined}
                          stickyLeftOffset  = {(columnIndex === stickyColumn) ? "0px" : undefined}
                          hasRightBorder    = {(columnIndex === stickyColumn)}
                          sort              = {getSortParams(columnIndex)}
                          modifier          = "truncate"
                          style             = {{width : column.width, minWidth : column.width, justifyItems : column.align}}
                        >
                          {column.header}
                        </Th>
                      )
                    }
                  </Tr>
                </Thead>
                <Tbody tabIndex = {-1}>
                  {
                    (sortedJobManagerItems.length > 0) ?
                      sortedJobManagerItems.slice(indexStart, indexEnd).map((jobManagerJobItem : any, jobManagerJobItemIndex : number) =>
                        <JobManagerJobItem tabIndex = {-1} key = {jobManagerJobItemIndex} jobManagerJobItem = {jobManagerJobItem} rowIndex = {jobManagerJobItemIndex}/>)
                      :
                      <Tr tabIndex = {-1}>
                        <Td tabIndex = {-1} colSpan = {jobManagerItemsMap.length + 1}>
                          <Bullseye tabIndex = {-1}>
                            <EmptyState tabIndex = {-1} variant = {EmptyStateVariant.xs}>
                              <EmptyStateHeader tabIndex = {-1} titleText = "No Job Manager Found" icon = {<EmptyStateIcon icon = {SearchIcon} className = "font-size-14"/>}/>
                              <EmptyStateBody tabIndex = {-1}>
                              </EmptyStateBody>
                            </EmptyState>
                          </Bullseye>
                        </Td>
                      </Tr>
                  }
                </Tbody>
              </Table>
            </InnerScrollContainer>
          </GraphTalkComponentDiv>
        </div>
      )

    }

    // ********************************************************************************************************

    const jobManagerJobsCount = () : any => {
      const count = (jobManagerJobs.computedData["job-list"]?.count) ? jobManagerJobs.computedData["job-list"].count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+")
          return "at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"
        else
          return scount + " items";
      }
      else
        return ""
    }

    const getAllJobManagerJobsCount = () : any => {
      const count = (allJobManagerJobsCount.computedData["job-list"]?.count) ? allJobManagerJobsCount.computedData["job-list"].count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+")
          return "from at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"
        else
          if (count < 500)
            return "from " + scount + " items";
          else
            return "from at least " + scount + " items";
      }
      else
        return ""
    }

    // ********************************************************************************************************

    const [page, setPage] = useState<number | null>(1);
    const [indexStart, setIndexStart] = useState<number | null>(0);
    const [indexEnd, setIndexEnd] = useState<number | null>(getPerPage());
    const [refreshPagintion, setRefreshPagination] = useState<boolean>(false);

    const toggleRefreshPagination = () => {
      setRefreshPagination(!refreshPagintion);
    }

    const JobManagerJobsPagination = () => {

      const handleSetPage = (
        _event    : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPage   : number,
        _perPage  : number | undefined,
        startIdx  : number | undefined,
        endIdx    : number | undefined
      ) => {
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
      };

      const handlePerPageSelect = (
        _event      : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPerPage  : number,
        newPage     : number,
        startIdx    : number | undefined,
        endIdx      : number | undefined
      ) => {
        setPerPage(newPerPage);
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
        toggleRefreshPagination();
      };

      return (
        <Pagination
          isCompact       = {false}
          itemCount       = {sortedJobManagerItems.length}
          toggleTemplate  = {({ firstIndex, lastIndex }) => (<><b>{firstIndex} - {lastIndex}</b> of <b>{jobManagerJobsCount()}</b></>)}
          perPage         = {getPerPage()}
          page            = {(page !== null) ? page : undefined}
          onSetPage       = {handleSetPage}
          onPerPageSelect = {handlePerPageSelect}
        />
      )
    }

    // ********************************************************************************************************

    async function cancelJobManagerJob(JobManagerJobNum : any) {
      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }

      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "cancelJobManagerJob(java.lang.String)";
          const result = await jolokia.execute(mbean, operation, JobManagerJobNum);
          log.debug(logPrefix, "cancelJobManagerJob, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function cancelSelectedJobManagerJob () {
      if (!areSeveralJobManagerItemsSelected()) return;

      for (let selectedJobManagerItemsNumsIndex = 0; selectedJobManagerItemsNumsIndex < selectedJobManagerJobItemNums.length; selectedJobManagerItemsNumsIndex++) {
        const JobManagerJobNum = selectedJobManagerJobItemNums[selectedJobManagerItemsNumsIndex];
        await cancelJobManagerJob(JobManagerJobNum)
      }

      await computeJobManagerJobs();
    }

    // ********************************************************************************************************

    const JobManagerCancel = () => {

      const isDisabled = () : boolean => {
        return !context?.canCancelJobManagerJob || !areSeveralJobManagerItemsSelected();
      }

      if (isDisabled())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      async function handleModalCancelJob () {
        await cancelSelectedJobManagerJob();
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      return (
        <>
          <Tooltip content = {"Cancel the selected Job(s)"}>
            <Button
              isDisabled  = {isDisabled()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {<Icon status = "danger" iconSize = "md"><StopIcon/></Icon>}
            />
          </Tooltip>
          <Modal
            titleIconVariant  = "danger"
            variant           = {ModalVariant.small}
            isOpen            = {isModalOpen}
            onClose           = {handleModalCancel}
            title             = {"Cancel the " + ((selectedJobManagerJobItemNums.length > 1) ? selectedJobManagerJobItemNums.length + " selected Job Manager ?" : " selected Job ?")}
            actions           = {[
                                  <Button key = "confirm" variant = "danger" isBlock size = "sm" onClick = {handleModalCancelJob}>Confirm</Button>,
                                  <Button key = "cancel" variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
                                ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md">
              {((selectedJobManagerJobItemNums.length > 1)
                ? "All the " + selectedJobManagerJobItemNums.length + " selected Job will be canceled"
                : "The selected Job will be canceled")}
            </Title>
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    async function suspendResumeJobManagerJob (JobManagerJobNum : any) {
      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }

      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "suspendResumeJobManagerJob(java.lang.String)";
          const result = await jolokia.execute(mbean, operation, JobManagerJobNum);
          log.debug(logPrefix, "suspendResumeJobManagerJob, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function suspendResumeSelectedJobManagerJob () {
      if (!areSeveralJobManagerItemsSelected()) return;

      for (let selectedJobManagerItemsNumsIndex = 0; selectedJobManagerItemsNumsIndex < selectedJobManagerJobItemNums.length; selectedJobManagerItemsNumsIndex++) {
        const JobManagerJobNum = selectedJobManagerJobItemNums[selectedJobManagerItemsNumsIndex];
        await suspendResumeJobManagerJob(JobManagerJobNum);
      }

      await computeJobManagerJobs();
    }

    // ********************************************************************************************************

    const JobManagerSuspendResume = () => {

      const isDisabled = () : boolean => {
        return !context?.canSuspendResumeJobManagerJob || !areSeveralJobManagerItemsSelected();
      }

      if (isDisabled())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      async function handleModalSuspendResume () {
        await suspendResumeSelectedJobManagerJob();
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      return (
        <>
          <Tooltip content = {(jobManagerDetails?.actions & 2) === 2 ? "Resume the selected Job(s)" : "Suspend the selected Job(s)"}>
            <Button
              isDisabled  = {isDisabled()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {
                <Icon status = "danger" iconSize = "md">
                  {(jobManagerDetails?.actions & 2) === 2 ? <PlayIcon/> : <PauseIcon/>}
                </Icon>
              }
            />
          </Tooltip>
          <Modal
            titleIconVariant  = "warning"
            variant           = {ModalVariant.small}
            isOpen            = {isModalOpen}
            onClose           = {handleModalCancel}
            title             = {((jobManagerDetails?.actions & 2) === 2 ? "Resume" : "Suspend") + " the " + ((selectedJobManagerJobItemNums.length > 1) ? selectedJobManagerJobItemNums.length + " selected Job Manager ?" : " selected Job ?")}
            actions           = {[
                                  <Button key = "confirm" variant = "danger" isBlock size = "sm" onClick = {handleModalSuspendResume}>{((jobManagerDetails?.actions & 2) === 2 ? "Resume" : "Suspend")}</Button>,
                                  <Button key = "cancel" variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
                                ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md">
              {((selectedJobManagerJobItemNums.length > 1)
                ? "All the " + selectedJobManagerJobItemNums.length + " selected Job will be suspended or resumed"
                : (jobManagerDetails?.actions & 2) === 2 ? "The selected Job will be resumed" : "The selected Job will be suspended")}
            </Title>
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    async function deleteJobManagerJob (JobManagerJobNum : any) {
      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }

      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "deleteJobManagerJob(java.lang.String)";
          const result = await jolokia.execute(mbean, operation, JobManagerJobNum);
          log.debug(logPrefix, "deleteJobManagerJob, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function deleteSelectedJobManagerJob () {
      if (!areSeveralJobManagerItemsSelected()) return;

      for (let selectedJobManagerItemsNumsIndex = 0; selectedJobManagerItemsNumsIndex < selectedJobManagerJobItemNums.length; selectedJobManagerItemsNumsIndex++) {
        const JobManagerJobNum = selectedJobManagerJobItemNums[selectedJobManagerItemsNumsIndex];
        await deleteJobManagerJob(JobManagerJobNum) ;
      }

      await computeJobManagerJobs();
    }

    // ********************************************************************************************************

    const JobManagerJobDelete = () => {

      const isDisabled = () : boolean => {
        return !context?.canDeleteJobManagerJob || !areSeveralJobManagerItemsSelected();
      }

      if (isDisabled())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      async function handleModalDelete () {
        await deleteSelectedJobManagerJob();
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      return (
        <>
          <Tooltip content = {"Delete the selected Job(s)"}>
            <Button
              isDisabled  = {isDisabled()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {<Icon status = "danger" iconSize = "md"><TrashIcon/></Icon>}
            />
          </Tooltip>
          <Modal
            titleIconVariant  = "danger"
            variant           = {ModalVariant.small}
            isOpen            = {isModalOpen}
            onClose           = {handleModalCancel}
            title             = {"Delete the " + ((selectedJobManagerJobItemNums.length > 1) ? selectedJobManagerJobItemNums.length + " selected Job Manager ?" : " selected Job ?")}
            actions           = {[
                                  <Button key = "confirm" variant = "danger" isBlock size = "sm" onClick = {handleModalDelete}>Delete</Button>,
                                  <Button key = "cancel" variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
                                ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md">
              {((selectedJobManagerJobItemNums.length > 1)
                ? "All the " + selectedJobManagerJobItemNums.length + " selected Job will be permanently deleted"
                : "The selected Job will be permanently deleted")}
            </Title>
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    const formatJobManagerJobAsText = (jobManagerJobJson : any) : String[] => {
      const jobManagerJobText : String[] = [];

      try {
        jobManagerJobText.push(" ------------------------------- \n");
        jobManagerJobText.push("|  Graphtalk Application Error  |\n");
        jobManagerJobText.push(" ------------------------------- \n\n");

        const job_list = jobManagerJobJson.job_list;

        jobManagerJobText.push(
          "The error #" + job_list.num + " was reported by " + job_list.userId
          + " on " + JulianDateTime.formatJulianAsDate(job_list.startDate)
          + " at " + JulianDateTime.formatJulianAsTime(job_list.startTime)
          + "\n"
        );
        jobManagerJobText.push("Language: " + job_list.language + "\n");
        jobManagerJobText.push("Level: " + job_list.level + "\n");
        jobManagerJobText.push("Inner Error Number: " + job_list.inner_error_number + "\n\n");

        const jobManagerJobBody = (job_list?.detail) ? job_list.detail : null;
        const jobManagerJobBodyLength = jobManagerJobBody.length;

        for (let jobManagerJobMsgIndex = jobManagerJobBodyLength - 1; jobManagerJobMsgIndex >= 0; jobManagerJobMsgIndex--) {
          const jobManagerJobMsg        = jobManagerJobBody[jobManagerJobMsgIndex]
          const jobManagerJobMsgHeader  = jobManagerJobMsg[0];
          const jobManagerJobMsgBody    = jobManagerJobMsg[1];

          jobManagerJobText.push(jobManagerJobMsgHeader + "\n");
          jobManagerJobText.push("-------------------------------------------------------------------------------\n");

          for (let jobManagerJobMsgBodyIndex = 0; jobManagerJobMsgBodyIndex < jobManagerJobMsgBody.length; jobManagerJobMsgBodyIndex++) {
            jobManagerJobText.push(jobManagerJobMsgBody[jobManagerJobMsgBodyIndex] + "\n");
          }

          jobManagerJobText.push("\n");
        };

        jobManagerJobText.push("-------------------------------------------------------------------------------\n");
        jobManagerJobText.push("-------------------------------------------------------------------------------\n");
        jobManagerJobText.push("Related Request: " + job_list.request + "\n");
      }
      catch (error) {
        jobManagerJobText.push("ERROR IN JSON: " + error + "\n\n");
        jobManagerJobText.push(jobManagerJobJson.toString());
        jobManagerJobText.push("\n");
      }

      return jobManagerJobText;
    }

    // ********************************************************************************************************

    const JobManagerJobDownload = () => {

      if (selectedJobManagerJobItemNums.length !== 1)
        return <></>;

      const JobManagerJobNum = selectedJobManagerJobItemNums[0];

      async function downloadJobAsText() : Promise<any> {
        let jolokia = null;
        try {
          jolokia = await jolokiaService.getJolokia();
        }
        catch (error) {
          log.error(logPrefix, "JobManagerJobDownload retrieveJolokia, error: ", error);
        }

        let jobManagerJobJson : any = null;

        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveJobManagerJob(java.lang.Integer)";
            const result = await jolokia.execute(mbean, operation, JobManagerJobNum);
            jobManagerJobJson = (result === undefined || result === null) ? null : JSON.parse(result.toString());
          }
        }

        if (jobManagerJobJson !== null) {
          const jobManagerJobText : any[] = formatJobManagerJobAsText(jobManagerJobJson);
          const jobManagerJobBlob = new Blob(jobManagerJobText, {type : "text/plain"});
          const jobManagerJobUrl = URL.createObjectURL(jobManagerJobBlob);
          const jobManagerJobLink = document.createElement("a");
          jobManagerJobLink.download = "jobmanagerjob-" + JobManagerJobNum + ".txt";
          jobManagerJobLink.href = jobManagerJobUrl;
          jobManagerJobLink.click();
        }
      }

      const handleDownload = () => {
        downloadJobAsText()
          .then(() => {
            log.debug(logPrefix, "JobManagerJobDownload downloadJobAsText OK");
          })
          .catch(error => {
            log.debug(logPrefix, "JobManagerJobDownload downloadJobAsText exception: ", error);
          })
      }

      return (
        <Tooltip content = {"Download the selected Job"}>
          <Button
            isDisabled  = {selectedJobManagerJobItemNums.length !== 1}
            variant     = "plain"
            size        = "default"
            onClick     = {handleDownload}
            icon        = {<Icon status = "info" iconSize = "md"><DownloadIcon/></Icon>}
          />
        </Tooltip>
      )
    }

    // ********************************************************************************************************
    return (
      <Drawer tabIndex = {-1} isInline isExpanded = {true}>
        <DrawerContent tabIndex = {-1} panelContent = {jobManagerPanelContent} style = {{paddingTop : "0px"}}>
          <JobManagerSearch/>
          <JobManagerJobsTable/>
        </DrawerContent>
      </Drawer>
    )
  }

  // ********************************************************************************************************

  const [context, setContext] = useState<any | null>(null);

  async function retrieveContext() {
    const context = await getContext();
    setContext(context);
    log.debug(logPrefix, "context: ", context);
  }

  // ********************************************************************************************************

  async function computeJobManagerJobs() {
    await retrieveContext();

    await refreshJolokia();

    let jobManagerJobsNew = { ...jobManagerJobs };
    let allJobManagerCountNew = { ...allJobManagerJobsCount };

    await retrieveJobManagerJobs(jobManagerJobsNew);
    await retrieveAllJobManagerCount(allJobManagerCountNew);

    try {
      jobManagerJobsNew.computedData = jobManagerJobsNew.retrievedData;
      allJobManagerCountNew.computedData = allJobManagerCountNew.retrievedData;
    } catch (e) { }

    setJobManagerJobs(jobManagerJobsNew);
    setAllJobManagerJobsCount(allJobManagerCountNew);
  }

  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying GtMonitor ****************************************************************************");

  return (
    <>
      {isProcessControlView ? (
        <ProcessControl
          isProcessControlView = {isProcessControlView}
          handleCheckboxChange = {handleCheckboxChange}
        />
      ) : 
      (
        <GraphTalkComponent tabIndex = {-1} title = "Job Control" onCompute = {computeJobManagerJobs}>
          <JobManagerItems/>
        </GraphTalkComponent>

      )}
    </>
  )
}

export default JobManager;