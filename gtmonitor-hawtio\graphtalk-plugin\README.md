# GraphTalk Hawtio plugin TypeScript project

A Hawtio v4 plugin for GraphTalk project written in TypeScript. This project doesn't run standalone, but is supposed to be used with other Spring Boot, Quarkus, or WAR projects.

Since a Hawtio plugin is based on React and [Webpack Module Federation](https://module-federation.github.io/), this project uses Yarn and [CRACO](https://craco.js.org/) as the build tools.
You can use any JS/TS tools for developing a Hawtio plugin so long as they can build a React and Webpack Module Federation application.

## Key components

The key components in the plugin project are as follows:

| File/Directory                                      | Description |
|-----------------------------------------------------| ----------- |
| [src/graphtalkplugin](./src/graphtalkplugin)        | This is where the actual code of the plugin is located. | 
| [pom.xml](./pom.xml)                                | This project uses <PERSON><PERSON> as the primary tool for building. Here, the `frontend-maven-plugin` is used to trigger the build of `sample-plugin` TypeScript project. |

## How to build

```console
mvn install
```
