
import * as JulianDateTime from './JulianDateTime'


import { Logger, jolokiaService } from '@hawtio/react'

import { IJolokiaSimple } from '@jolokia.js/simple'

import {
  Badge,
  Bullseye,
  Button,
  DatePicker, TimePicker,
  EmptyState, EmptyStateVariant, EmptyStateHeader, EmptyStateIcon,
  Divider,
  Drawer, DrawerPanelContent, DrawerContent, DrawerContentBody,
  ExpandableSection,
  Grid, GridItem,
  Icon,
  Modal, ModalVariant,
  Pagination,
  Split, SplitItem,
  Text, TextContent, TextInput, TextVariants,
  Title,
  Tooltip,
  isValidDate,
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionToggle,
  Checkbox, Dropdown, DropdownItem, DropdownList, MenuToggle, MenuToggleElement,
  Radio
} from '@patternfly/react-core'

import {
  Table,
  Thead,
  Tr,
  Th, ThProps,
  Tbody,
  Td,
  InnerScrollContainer
} from '@patternfly/react-table';

import {DownloadIcon, EraserIcon, SearchIcon, TrashIcon, EditIcon} from '@patternfly/react-icons/dist/esm/icons'

import React, { useEffect, useRef, useState } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import { getContext } from './Context';

import "./Batches.css";

import './GlobalPlugins.css';

import {
  batchManagerExecutionsItemsMap,
  batchManagerExecutionSlicesMap, 
  batchManagerExecutionErrorsMap,
  getUserParametersProp,
  getExecutionDetailsProp,
  getDynamicFontColor,
  getStatusIcon,
  getDates,
  batchCompletedExecutionsItemsMap,
  batchUncompletedExecutionsItemsMap,
  displayValue,
  AccordionSectionKeys,
  getDeltaValue,
  HEIGHT_SEARCH_EXPANDED,
  HEIGHT_SEARCH_COLLAPSED,
  TOP_TABLE_HEIGHT_EXPANDED,
  TOP_TABLE_HEIGHT_COLLAPSED,
  executionStatuses,
  uncompletedStatuses,
  completedStatuses,
  formNumbersList
} from "./BatchesHelpers"



// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-batches");

const logPrefix             = "** Batches ** ";

//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Batch Manager Executions
// ********************************************************************************************************
// ********************************************************************************************************

const adminPath = "com.csc.gtmonitor.*:type=Hawtio";
 
const Batches : React.FunctionComponent = () => {

  type BatchManagerExecutionsType = {
    retrievedData       : any,
    computedData        : any
  }
 
  // ********************************************************************************************************

  const [batchManagerExecutions, setBatchManagerExecutions] = useState<BatchManagerExecutionsType> ({
    retrievedData       : false,
    computedData        : []
  })

  const [allBatchExecutionsCount, setAllBatchExecutionsCount] = useState<BatchManagerExecutionsType>({
    retrievedData       : false,
    computedData        : []
  })
  
  const [isDaySummaryView, setIsDaySummaryView] = useState<boolean>(false);
 
  // ********************************************************************************************************
 
  const expandedSectionsRef = useRef<string[]>([]); 

  // ********************************************************************************************************

  const batchExecutionsPanelContentSize = useRef<any>("30%");

  // ********************************************************************************************************
  
  const defaultSortIndex      = 0;
  const defaultSortDirection  = 'desc';

  const activeSortIndex = useRef<any>(defaultSortIndex);
  const activeSortDirection = useRef<any>(defaultSortDirection);


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia : IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  
  // ********************************************************************************************************
  // ********************************************************************************************************
  // Search Criterias
  // ********************************************************************************************************
  // ********************************************************************************************************

  const searchExpanded   = useRef<boolean>(true);
  const setSearchExpanded = (newSearchExpanded : boolean) => { searchExpanded.current = newSearchExpanded; }
  const isSearchExpanded = () => { return searchExpanded.current }

  const defaultSearchStart     = 0;
  const defaultSearchNum       = 50;
  const defaultSearchCount     = 1;
  const defaultSearchId        = null;
  const defaultSearchName      = null;
  const defaultSearchSlice     = null;
  const defaultSearchDateMin   = null;
  const defaultSearchTimeMin   = null;
  const defaultSearchDateMax   = null;
  const defaultSearchTimeMax   = null;
  
  const searchStart           = useRef<number|null>(null);
  const searchNum             = useRef<number|null>(defaultSearchNum);
  const searchCount           = useRef<number|null>(null);
  const searchId              = useRef<number|null>(null);
  const searchName            = useRef<string|null>(null);
  const searchSlice           = useRef<string|null>(null);
  const searchDateMin         = useRef<Date|null>(null);
  const searchTimeMin         = useRef<Date|null>(null);
  const searchDateMax         = useRef<Date|null>(null);
  const searchTimeMax         = useRef<Date|null>(null);

  const searchReset = () => {
    searchStart.current         = null;
    searchNum.current           = defaultSearchNum;
    searchCount.current         = null;
    searchId.current            = null;
    searchName.current          = null;
    searchSlice.current         = null;
    searchDateMin.current       = null;
    searchTimeMin.current       = null;
    searchDateMax.current       = null;
    searchTimeMax.current       = null;
  }

  
  // ********************************************************************************************************
  // ********************************************************************************************************
  // Pagination Variables
  // ********************************************************************************************************
  // ********************************************************************************************************

  const defaultPerPage = 25;

  const perPage = useRef<number>(defaultPerPage);

  const setPerPage = (newPerPage : number) => {
    perPage.current = newPerPage;
  }

  const getPerPage = () : number => {
    return perPage.current;
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  const retrieveBatchManagerExecutionsSignature = "retrieveBatchManagerExecutions(java.lang.String)";

  async function retrieveBatchManagerExecutions(batchManagerExecutions : any, newArgs? : any) {
    const args : any = newArgs || { "sort" : "-num", "_attributes" : "step_counter,error_counter,warning_counter, slice_ctrl, slice_number,slices,slice_ctrl_type" };
    
    const argStart   = (searchStart.current   === null) ? defaultSearchStart    : searchStart.current;
    const argNum     = (searchNum.current     === null) ? defaultSearchNum      : searchNum.current;
    const argCount   = (searchCount.current   === null) ? defaultSearchCount    : searchCount.current;
    const argId      = (searchId.current      === null) ? defaultSearchId       : searchId.current;
    const argName    = (searchName.current    === null) ? defaultSearchName     : searchName.current;
    const argSlice   = (searchSlice.current   === null) ? defaultSearchSlice    : searchSlice.current;
    const argDateMin = (searchDateMin.current === null) ? defaultSearchDateMin  : JulianDateTime.dateToJulian(searchDateMin.current);
    const argTimeMin = (searchTimeMin.current === null) ? defaultSearchTimeMin  : JulianDateTime.timeToJulian(searchTimeMin.current);
    const argDateMax = (searchDateMax.current === null) ? defaultSearchDateMax  : JulianDateTime.dateToJulian(searchDateMax.current);
    const argTimeMax = (searchTimeMax.current === null) ? defaultSearchTimeMax  : JulianDateTime.timeToJulian(searchTimeMax.current);

    if (argStart   !== null && argStart        > 0) args["_start"]   = argStart;     else args["_start"] = 0;
    if (argNum     !== null && argNum          > 0) args["_num"]     = argNum;
    if (argCount   !== null && argCount        > 0) args["_count"]   = argCount;
    if (argId      !== null && argId           > 0) args["execution_id"]      = argId;
    if (argName    !== null && argName.length  > 0) args["batch_name"]   = argName;
    if (argSlice   !== null && argSlice.length > 0) args["slice_ctrl"]   = argSlice;
    if (argDateMin !== null && argDateMin      > 0) args["date_min"] = argDateMin;
    if (argTimeMin !== null && argTimeMin      > 0) args["time_min"] = argTimeMin;
    if (argDateMax !== null && argDateMax      > 0) args["date_max"] = argDateMax;
    if (argTimeMax !== null && argTimeMax      > 0) args["time_max"] = argTimeMax;

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveBatchManagerExecutionsSignature;
          log.debug(logPrefix, "retrieveBatchManagerExecutions, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrieveBatchManagerExecutions, result : ", json);
          batchManagerExecutions.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrieveBatchManagerExecutions, exception: ", e);
      }
    }
  }

  async function retrieveAllBatchExecutionsCount(allBatchExecutionsCount : any) {
    const args : any = { "_num" : 0};

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveBatchManagerExecutionsSignature;
          log.debug(logPrefix, "retrieveBatchManagerExecutions, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrieveBatchManagerExecutions, result: ", json);
          allBatchExecutionsCount.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrieveBatchManagerExecutions, exception: ", e);
      }
    }
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************
 
  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying Batch Manager Executions ****************************************************************************");

  // ********************************************************************************************************

  const BatchManagerExecutionsItems = (props : any) => {
  
    const stickyColumn          = -1;   // no sticky column, else put the column index like 0 for the first one

    const [activeSortRefresh, setActiveSortRefresh] = useState<boolean>(false);
    const toggleActiveSortRefresh = () => { setActiveSortRefresh(!activeSortRefresh); }

    const setActiveSortIndex = (sortIndex : any) => { activeSortIndex.current = sortIndex;  toggleActiveSortRefresh(); }
    const getActiveSortIndex = () : any => { return activeSortIndex.current; }

    const setActiveSortDirection = (sortDirection : any) => { activeSortDirection.current = sortDirection; toggleActiveSortRefresh(); }
    const getActiveSortDirection = () : any => { return activeSortDirection.current; }

    let batchManagerExecutionsItems : {[key : number] : any}[] = batchManagerExecutions.computedData.batch_executions?.items;
    if (batchManagerExecutionsItems === null || batchManagerExecutionsItems === undefined) {
      batchManagerExecutionsItems = [];
    }
  
    let sortedBatchManagerExecutionsItems = batchManagerExecutionsItems;
    if (activeSortIndex !== null) {
      sortedBatchManagerExecutionsItems = batchManagerExecutionsItems.sort((a, b) => {
        const column = batchManagerExecutionsItemsMap[getActiveSortIndex()];
        const key     : any = column.key;
        const aValue  : any = a[key];
        const bValue  : any = b[key];
        if (typeof aValue === 'number') {
          // Numeric sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as number) - (bValue as number);
          }
          return (bValue as number) - (aValue as number);
        }
        else {
          // String sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as string).localeCompare(bValue as string);
          }
          return (bValue as string).localeCompare(aValue as string);
        }
      });
    }
  
    const getSortParams = (columnIndex : number) : ThProps['sort'] => ({
      sortBy : {
        index             : getActiveSortIndex(),
        direction         : getActiveSortDirection(),
        defaultDirection  : 'asc' // starting sort direction when first sorting a column. Defaults to 'asc'
      },
      onSort : (_event, index, direction) => {
        setActiveSortIndex(index);
        setActiveSortDirection(direction);
      },
      columnIndex
    });

    // ********************************************************************************************************

    const isBatchItemsSelectable = (batchManagerExecutionItem : any) => batchManagerExecutionItem.execution_id !== '0'; // Arbitrary logic for this example

    const selectableBatchManagerExecutionsItems = batchManagerExecutionsItems.filter(isBatchItemsSelectable);

    const [selectableBatchExecutionsItemsNums, setSelectedBatchExecutionsItemsNums] = useState<string[]>([]);

    const setBatchExecutionsItemsSelected = (batchManagerExecutionItem : any, isSelecting = true) =>
      setSelectedBatchExecutionsItemsNums((prevSelected) => {
        const otherSelectedBatchExecutionsItemsNums = prevSelected.filter((w) => w !== batchManagerExecutionItem.execution_id);
        return isSelecting && isBatchItemsSelectable(batchManagerExecutionItem) ? [...otherSelectedBatchExecutionsItemsNums, batchManagerExecutionItem.execution_id]
                                                                     : otherSelectedBatchExecutionsItemsNums;
      });

    const selectAllBatchExecutionsItems = (isSelecting = true) => 
      setSelectedBatchExecutionsItemsNums(isSelecting ? selectableBatchManagerExecutionsItems.map((batchManagerExecutionItem : any) => batchManagerExecutionItem.execution_id) : []);

    const areAllBatchExecutionsItemsSelected = () => { return (selectableBatchExecutionsItemsNums.length === selectableBatchManagerExecutionsItems.length) };

    const areSeveralBatchExecutionsItemsSelected = () => { return (selectableBatchExecutionsItemsNums.length > 0) };

    const isBatchExecutionsItemsSelected = (batchManagerExecutionItem : any) => selectableBatchExecutionsItemsNums.includes(batchManagerExecutionItem.execution_id);

    const [recentSelectedRowIndex, setRecentSelectedRowIndex] = useState<number | null>(null);

    // ********************************************************************************************************

    const shiftKey = useRef(false);
    const setShiftKey = (state : boolean) => { shiftKey.current = state; }
    const isShiftKey = () : boolean => { return shiftKey.current; }

    const controlKey = useRef(false);
    const setControlKey = (state : boolean) => { controlKey.current = state; }
    const isControlKey = () : boolean => { return controlKey.current; }

    // ********************************************************************************************************

    const onSelectBatchManagerExecutionItem = (batchManagerExecutionItem : any, rowIndex : number, isSelecting : boolean) => {
      // If the user does control + selecting the checkboxes, then selected checkbox should be added to existing ones
      // If the user does shift + selecting the checkboxes, then all intermediate checkboxes should be selected
      if (isShiftKey() && recentSelectedRowIndex !== null) {
        const numberSelected = rowIndex - recentSelectedRowIndex;
        const intermediateIndexes =
          numberSelected > 0
            ? Array.from(new Array(numberSelected + 1), (_x, i) => i + recentSelectedRowIndex)
            : Array.from(new Array(Math.abs(numberSelected) + 1), (_x, i) => i + rowIndex);
        intermediateIndexes.forEach((index) => setBatchExecutionsItemsSelected(batchManagerExecutionsItems[index], isSelecting));
      }
      else
      if (isControlKey())
        setBatchExecutionsItemsSelected(batchManagerExecutionItem, isSelecting);
      else
      if (isBatchItemsSelectable(batchManagerExecutionItem) && !isBatchExecutionsItemsSelected(batchManagerExecutionItem))
        setSelectedBatchExecutionsItemsNums([batchManagerExecutionItem.execution_id]);
      else
        setSelectedBatchExecutionsItemsNums([]);

      setRecentSelectedRowIndex(rowIndex);
    };

    // ********************************************************************************************************
  
    useEffect(() => {
      const onKeyDown = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(true);
        if (e.key === 'Control')
          setControlKey(true);
      };

      const onKeyUp = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(false);
        if (e.key === 'Control')
          setControlKey(false);
      };
  
      document.addEventListener('keydown', onKeyDown);
      document.addEventListener('keyup',   onKeyUp);
  
      return () => {
        document.removeEventListener('keydown', onKeyDown);
        document.removeEventListener('keyup',   onKeyUp);
      };
    }, []);
  
    // ********************************************************************************************************

    // Selected rows are tracked by the batch manager executions  num field from each row.
    // This is to prevent state from being based on the row order index which is subject to sorting.

    // ********************************************************************************************************

    const BatchManagerExecutionDetails = () => {
      const [batchManagerExecutionDetails, setBatchManagerExecutionDetails] = useState<any>(null);      
      const [expandedSections, setExpandedSections] = useState<string[]>([]);
      const [batchExecutionSlices, setBatchExecutionSlices] = useState<any>(null);
      const [batchExecutionErrors, setBatchExecutionErrors] = useState<any>(null);
      // ********************************************************************************************************


      async function retrievBatchManagerExecutionDetails(batchManagerExecutionItem : any) : Promise<any> {
        log.debug(logPrefix, "retrievBatchManagerExecutionDetails : " + batchManagerExecutionItem?.execution_id + " / " + batchManagerExecutionItem)

        const batchManagerExecutionNum = batchManagerExecutionItem?.execution_id;

        let jolokia = null;
        try {
          jolokia = await jolokiaService.getJolokia();
          log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        }
        catch (error) {
          log.error(logPrefix, "retrieveJolokia, error: ", error);
        }

        let json : any = null;
        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveBatchManagerExecution(java.lang.String,java.lang.String)";
            const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum, "");
            log.debug(logPrefix, "retrievBatchManagerExecutionDetails, result: ", result);
            json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
          }
        }

        log.debug(logPrefix, "retrievBatchManagerExecutionDetails, json: ", json);

        let batch_execution = (json === undefined || json === null) ? null : json.batch_execution;
        if (batch_execution === null) {
          log.debug(logPrefix, "retrievBatchManagerExecutionDetails, cannot retrieve batch manager execution details for: ", batchManagerExecutionNum);
          batch_execution = -1;
        }
        else
          log.debug(logPrefix, "retrievBatchManagerExecutionDetails, batch manager execution details retrieved for: ", batchManagerExecutionNum);

        setBatchManagerExecutionDetails(batch_execution);
      }
      
      async function retrievBatchManagerExecutionSlicesDetails(batchManagerExecutionItem : any) : Promise<any> {
        const batchManagerExecutionNum = batchManagerExecutionItem?.execution_id;
        let jolokia = null;
    
        try {
          jolokia = await jolokiaService.getJolokia();
          log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        } 
        catch (error) {
          log.error(logPrefix, "retrieveJolokia, error: ", error);
        }
    
        let json : any = null;
        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveBatchManagerExecutionSlices(java.lang.String,java.lang.String)";
            const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum, "");
            log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails, result: ", result);
            json = result === undefined || result === null ? null : JSON.parse(result.toString());
          }
        }
    
        log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails, json: ", json);
        let batch_execution_slices = json === undefined || json === null ? null : json.batch_execution_slices;
    
        if (batch_execution_slices === null) {
          log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails, cannot retrieve batch manager execution slices for: ", batchManagerExecutionNum);
          batch_execution_slices = -1;
        } 
        else {
          log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails, batch manager execution slices retrieved for: ", batchManagerExecutionNum);
        }
    
        setBatchExecutionSlices(batch_execution_slices);
      }
      
      async function retrieveBatchManagerExecutionErrorsDetails(batchManagerExecutionItem : any) : Promise<any> {
        const batchManagerExecutionNum = batchManagerExecutionItem?.execution_id;
        let jolokia = null;
    
        try {
          jolokia = await jolokiaService.getJolokia();
          log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        } 
        catch (error) {
          log.error(logPrefix, "retrieveJolokia, error: ", error);
        }
    
        let json : any = null;
        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveBatchManagerExecutionErrors(java.lang.String,java.lang.String)";
            const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum, "");
            log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails, result: ", result);
            json = result === undefined || result === null ? null : JSON.parse(result.toString());
          }
        }
    
        log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails, json: ", json);
        let batch_execution_errors = json === undefined || json === null ? null : json.batch_execution_errors;
    
        if (batch_execution_errors === null) {
          log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails, cannot retrieve batch manager execution errors for: ", batchManagerExecutionNum);
          batch_execution_errors = -1;
        } 
        else {
          log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails, batch manager execution errors retrieved for: ", batchManagerExecutionNum);
        }
    
        setBatchExecutionErrors(batch_execution_errors);
      }
  
      // *******************************************************************************************************

      useEffect(() => {
        const nbSelected = selectableBatchExecutionsItemsNums.length;
    
        if (nbSelected === 1) {
          const batchManagerExecutionItem : any = batchManagerExecutionsItems.find((item : any) => item.execution_id === selectableBatchExecutionsItemsNums[0]);
          retrievBatchManagerExecutionDetails(batchManagerExecutionItem)
            .then(() => {
              log.debug(logPrefix, "retrievBatchManagerExecutionDetails OK");
            })
            .catch((error) => {
              log.debug(logPrefix, "retrievBatchManagerExecutionDetails exception: ", error);
              setBatchManagerExecutionDetails(null);
            });

          retrievBatchManagerExecutionSlicesDetails(batchManagerExecutionItem)
            .then(() => {
              log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails OK");
            })
            .catch((error) => {
              log.debug(logPrefix, "retrievBatchManagerExecutionSlicesDetails exception: ", error);
              setBatchExecutionSlices(null);
            });

          retrieveBatchManagerExecutionErrorsDetails(batchManagerExecutionItem)
            .then(() => {
              log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails OK");
            })
            .catch((error) => {
              log.debug(logPrefix, "retrieveBatchManagerExecutionErrorsDetails exception: ", error);
              setBatchExecutionErrors(null);
            });
        } 
        else {
          setBatchManagerExecutionDetails(null);
          setBatchExecutionSlices(null);
          setBatchExecutionErrors(null);
        }
      }, [selectableBatchExecutionsItemsNums]);

      useEffect(() => {
        if (batchManagerExecutionDetails && batchManagerExecutionDetails !== -1) {
          if (expandedSectionsRef.current.length === 0) {
            setExpandedSections([
              AccordionSectionKeys.EXECUTION_DETAILS,
              AccordionSectionKeys.USER_PARAMETERS,
              AccordionSectionKeys.SLICES,
              AccordionSectionKeys.ERRORS,
              AccordionSectionKeys.WARNINGS,
            ]);
          }
          else {
            setExpandedSections(expandedSectionsRef.current);
          }
        }
        else {
          setExpandedSections([]);
        }
      }, [batchManagerExecutionDetails]);

      // ********************************************************************************************************
      const BatchManagerExecutionSlicesBody = (props : any) => {
      
        return (
          <div className = "info_tables_body_style">
            <Table 
              tabIndex    = {-1}
              className   = "custom-compact-info-table"
              aria-label  = "Batch Execution Slices" 
              variant     = "compact" 
              isStriped 
              borders     = {false} 
              >
              <Thead tabIndex = {-1}>
                <Tr tabIndex = {-1}>
                {
                  batchManagerExecutionSlicesMap.map((column) => (
                    <Th 
                      tabIndex = {-1}
                      key       = {column.key} 
                      className = "slices_table_style"
                    >
                      {column.header}
                    </Th>
                  ))
                }
                </Tr>
              </Thead>
              <Tbody tabIndex = {-1}>
              {
                batchExecutionSlices && batchExecutionSlices.items && batchExecutionSlices.items.length > 0 ? (
                  batchExecutionSlices.items.map((slice : any, index : number) => (
                    <Tr tabIndex = {-1} key = {slice.slice_id}>
                    {
                      batchManagerExecutionSlicesMap.map((column) => {
                        let value = slice[column.key];
                        if (column.key === "duration" && typeof value === 'number') {
                          value = formatDuration(value);
                        }
                        return (
                          <Td 
                            key       = {column.key} 
                            className = "slices_table_style"
                          >
                            {value ?? "N/A"}
                          </Td>
                        );
                      })
                    }
                    </Tr>
                  ))
                ) 
                :
                (
                  <Tr>
                    <Td colSpan = {batchManagerExecutionSlicesMap.length}>No slices available</Td>
                  </Tr>
                )
              }
              </Tbody>
            </Table>
          </div>
        );
      };

      async function retrieveBatchManagerExecutionError(batchManagerExecutionItem : any, eventNumber : number, stepNumber : number) : Promise<any> {
        const batchManagerExecutionNum = batchManagerExecutionItem?.execution_id;
        const errorId = eventNumber + "-" + stepNumber;
        let jolokia = null;

        try {
            jolokia = await jolokiaService.getJolokia();
            log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        } 
        catch (error) {
            log.error(logPrefix, "retrieveJolokia, error: ", error);
        }

        let json : any = null;
        if (jolokia !== null) {
            const response = await jolokia.search(adminPath);
            if (response != null && response.length > 0) {
                const mbean = response[0];
                const operation = "retrieveBatchManagerExecutionError(java.lang.String,java.lang.String,java.lang.String)";
                const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum, errorId, "");
                log.debug(logPrefix, "retrieveBatchManagerExecutionErrorDetails, result: ", result);
                json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
            }
        }

        log.debug(logPrefix, "retrieveBatchManagerExecutionErrorDetails, json: ", json);

        let error_details = json === undefined || json === null ? null : json.batch_execution_error;

        if (error_details === null) {
            log.debug(logPrefix, "retrieveBatchManagerExecutionErrorDetails, cannot retrieve error details for: ", errorId);
            error_details = -1;
        } 
        else {
            log.debug(logPrefix, "retrieveBatchManagerExecutionErrorDetails, error details retrieved for: ", errorId);
        }

        return error_details;
      }
      
      // ********************************************************************************************************
      const BatchManagerExecutionErrorsBody = (props : any) => {
        const [isModalOpen, setIsModalOpen] = useState(false);
        const [modalContent, setModalContent] = useState("Loading...");

        const fetchErrorDetails = async (eventNumber : number, stepNumber : number) => {
          setModalContent("Loading...");
          setIsModalOpen(true);
          const batchManagerExecutionItem : any = batchManagerExecutionsItems.find((item : any) => item.execution_id === selectableBatchExecutionsItemsNums[0]);

          try {
            const data = await retrieveBatchManagerExecutionError(batchManagerExecutionItem, eventNumber, stepNumber);
            if (data && data.error_detail) {
              setModalContent(formatErrorDetails(data.error_detail));
            } else {
              setModalContent("No additional details available.");
            }
          } 
          catch (error) {
            log.error(logPrefix, "Error fetching details:", error);
            setModalContent("Error fetching details.");
          }
        };

        const formatErrorDetails = (errorData : any) => { 
          if (!errorData) {
            return "No error details available.";
          }
        
          const extractErrorDetails = (data : any) => {
            let details : {name : string; fontWeight : string}[] = [];
            for (let i = data.length - 1; i >= 0; i--) {
              if (Array.isArray(data[i])) {
                  details.push({name : data[i][0], fontWeight : "bold"});
                  if (Array.isArray(data[i][1])) {
                      details.push(...data[i][1].map((item : string) => ({ name : item, fontWeight : "normal" })));
                  }
              }
            }
            return details;
          };
        
          const errorDetailsProp = extractErrorDetails(errorData);
        
          return (
            <div>
              <Table 
                tabIndex    = {-1} 
                isStriped  
                aria-label  = "Error Details modal" 
                variant     = "compact" 
                borders     = {false} 
                isStickyHeader
              >
                <Tbody tabIndex = {-1}>
                {
                  errorDetailsProp.map((item, index) => (
                    <ErrorProperty
                      key         = {index}
                      index       = {index}
                      name        = {item.name}
                      fontWeight  = {item.fontWeight}
                    />
                  ))
                }
                </Tbody>
              </Table>
            </div>
          );
        };

        const ErrorProperty = (props : any) => {
          const altValue = props?.altvalue ? <>({props.altvalue})</> : "";
        
          return (
            <Tr tabIndex = {-1} >
              <Td tabIndex = {-1} modifier = "fitContent">
                <p tabIndex = {-1} style = {{fontWeight : props.fontWeight, margin : 0}}>{props.name}</p>
              </Td>
              <Td tabIndex = {-1} modifier = "fitContent">
                <p tabIndex = {-1} style = {{fontWeight : 'normal', margin : 0}}>{props?.value}</p>
              </Td>
              <Td tabIndex = {-1}>
                <p tabIndex = {-1} style = {{fontWeight : 'normal', margin : 0}}>{altValue}</p>
              </Td>
            </Tr>
          );
        };

        return (

          <div className = "info_tables_body_style">
            <Table
              tabIndex    = {-1}
              className   = "custom-compact-info-table"
              aria-label  = "Batch Execution Errors"
              variant     = "compact"
              isStriped
              borders     = {false}
            >
              <Thead tabIndex = {-1}>
                <Tr tabIndex = {-1}>
                  {batchManagerExecutionErrorsMap.map((column) => (
                    <Th 
                      tabIndex = {-1}
                      key       = {column.key} 
                      className = "errors_table_style"
                    >
                      {column.header}
                    </Th>
                  ))}
                </Tr>
              </Thead>
              <Tbody tabIndex = {-1}>
              {
                batchExecutionErrors?.items?.length > 0 ? (
                  batchExecutionErrors.items.map((error : any) => (
                    <Tr
                      tabIndex  = {-1}
                      key       = {error.event_number}
                      onClick   = {() => fetchErrorDetails(error.event_number, error.step_number)}
                      style     = {{cursor : "pointer"}}
                    >
                      {batchManagerExecutionErrorsMap.map((column) => (
                        <Td
                          tabIndex = {-1}
                          key       = {column.key}
                          className = "errors_table_style"
                        >
                          {error[column.key] ?? "N/A"}
                        </Td>
                      ))}
                    </Tr>
                  ))
                )
                :
                (
                  <Tr>
                    <Td colSpan = {batchManagerExecutionErrorsMap.length}>No errors available</Td>
                  </Tr>
                )
              }
              </Tbody>
            </Table>

            <Modal
              title   = "Error Details"
              isOpen  = {isModalOpen}
              onClose = {() => setIsModalOpen(false)}
              variant = "large"
              style   = {{color : 'inherit !important'}}
            >
              <pre style = {{whiteSpace : "pre-wrap", color : "inherit !important"}}>{modalContent}</pre>
              <Button variant = "primary" onClick = {() => setIsModalOpen(false)}>Close</Button>
            </Modal>
          </div>
        );       
      };

      // ********************************************************************************************************      };

      const BatchManagerExecutionProperty = ({ name, value, altvalue, index, style = {} } : any) => {
        const rowClass = index % 2 === 0 ? "accordion-row-odd" : "accordion-row-even";
      
        return (
          <Tr tabIndex = {-1} className = {rowClass}>
            <Td tabIndex = {-1} modifier = "fitContent">
              <p
                tabIndex    = {-1}
                className   = "accordion-name-cell"
                style       = {{fontWeight : style?.fontWeight || 'bold'}}
              >
                {name}
              </p>
            </Td>
            <Td tabIndex = {-1} modifier = "fitContent" style = {{paddingLeft : "10px"}}>
              <p
                tabIndex    = {-1}
                className   = "accordion-value-cell"
                style       = {{color : style.color || 'inherit'}}
              >
                {value}
              </p>
            </Td>
            {altvalue && (
              <Td tabIndex = {-1} modifier = "fitContent">
                <p 
                  tabIndex  = {-1} 
                  className = "accordion-altvalue-cell"
                >
                  ({altvalue})
                </p>
              </Td>
            )}
          </Tr>
        );
      };

      const executionDetailsProp = getExecutionDetailsProp(batchManagerExecutionDetails);
      const userParametersProp = getUserParametersProp(batchManagerExecutionDetails);
      
      const formatDuration = (duration : number) => {
        if (duration && !isNaN(duration)) {
          return parseFloat(duration.toFixed(3));
        }
        return "N/A";
      };

      // ********************************************************************************************************

      const toggleSection = (id : string) => {
        setExpandedSections((prev) => {
          const newExpandedSections = prev.includes(id)
            ? prev.filter((section) => section !== id)
            : [...prev, id];
    
          expandedSectionsRef.current = newExpandedSections;
          return newExpandedSections;
        });
      };

      return (
        <GraphTalkComponentDiv hasScroll>
          <Accordion asDefinitionList togglePosition = "start">
            <AccordionItem>
              <AccordionToggle
                tabIndex    = {-1}
                onClick     = {() => toggleSection(AccordionSectionKeys.EXECUTION_DETAILS)}
                isExpanded  = {expandedSections.includes(AccordionSectionKeys.EXECUTION_DETAILS)}
                id          = {AccordionSectionKeys.EXECUTION_DETAILS}
              >
                Execution Details
              </AccordionToggle>
              <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.EXECUTION_DETAILS)}>
                {
                  batchManagerExecutionDetails ? (
                    executionDetailsProp.map((item, index) => (
                      <BatchManagerExecutionProperty
                        key     = {item.name}
                        index   = {index}
                        name    = {item.name}
                        value   = {<span style = {{paddingLeft : "10px"}}>{item.icon}<span style = {{paddingLeft : "10px"}}>{item.value}</span></span>}
                        style   = {{color : item.color || "inherit"}}
                      />
                    ))
                  )
                  :
                  (
                    <BatchManagerExecutionProperty name = "No Execution Details" style = {{fontWeight : 'normal'}}/>
                  )
                }
              </AccordionContent>
            </AccordionItem>
            <AccordionItem>
              <AccordionToggle
                tabIndex    = {-1}
                onClick     = {() => toggleSection(AccordionSectionKeys.USER_PARAMETERS)}
                isExpanded  = {expandedSections.includes(AccordionSectionKeys.USER_PARAMETERS)}
                id          =  {AccordionSectionKeys.USER_PARAMETERS}
              >
                User Parameters
              </AccordionToggle>
              <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.USER_PARAMETERS)}>
                {
                  batchManagerExecutionDetails ? (
                    userParametersProp.map((item, index) => (
                      <BatchManagerExecutionProperty
                        key     = {item.name}
                        index   = {index}
                        name    = {item.name}
                        value   = {item.value}
                      />
                    ))
                  )
                  :
                  (
                    <BatchManagerExecutionProperty name = "No User Parameters" style = {{fontWeight : 'normal'}}/>
                  )
                }
              </AccordionContent>
            </AccordionItem>
            <AccordionItem>
              <AccordionToggle
                tabIndex    = {-1}
                onClick     = {() => toggleSection(AccordionSectionKeys.SLICES)}
                isExpanded  = {expandedSections.includes(AccordionSectionKeys.SLICES)}
                id          = {AccordionSectionKeys.SLICES}
              >
                Slices {batchExecutionSlices?.items?.length > 0 ?
                  `(${batchExecutionSlices.items.length}/${batchExecutionSlices.items.length})` :
                  batchExecutionSlices?.items === undefined ? '' : `(0/${batchManagerExecutionDetails?.slice_number})`}
              </AccordionToggle>
              <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.SLICES)}>
                {
                  batchExecutionSlices?.items?.length > 0
                    ? <BatchManagerExecutionSlicesBody/>
                    : <BatchManagerExecutionProperty name = "No Slices" style = {{fontWeight : 'normal'}}/>
                }
              </AccordionContent>
            </AccordionItem>
            <AccordionItem>
              <AccordionToggle
                tabIndex    = {-1}
                onClick     = {() => toggleSection(AccordionSectionKeys.ERRORS)}
                isExpanded  = {expandedSections.includes(AccordionSectionKeys.ERRORS)}
                id          = {AccordionSectionKeys.ERRORS}
              >
                Errors {batchExecutionErrors?.count !== 0  ? `(${batchExecutionErrors?.count})` : ''}
              </AccordionToggle>
              <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.ERRORS)}>
                {
                  batchExecutionErrors?.items?.length > 0
                    ? <BatchManagerExecutionErrorsBody/>
                    : <BatchManagerExecutionProperty name = "No Errors" style = {{fontWeight : 'normal'}}/>
                }
              </AccordionContent>
            </AccordionItem>
            <AccordionItem>
              <AccordionToggle
                tabIndex    = {-1}
                onClick     = {() => toggleSection(AccordionSectionKeys.WARNINGS)}
                isExpanded  = {expandedSections.includes(AccordionSectionKeys.WARNINGS)}
                id          = {AccordionSectionKeys.WARNINGS}
              >
                Warnings {batchManagerExecutionDetails?.user_parameters?.warnings?.length > 0 ? `(${batchManagerExecutionDetails?.user_parameters.warnings.length})` : ''}
              </AccordionToggle>
              <AccordionContent isHidden = {!expandedSections.includes(AccordionSectionKeys.WARNINGS)}>
                {
                  Array.isArray(batchManagerExecutionDetails?.user_parameters?.warnings) &&
                    batchManagerExecutionDetails.user_parameters.warnings.length > 0 ? (
                    batchManagerExecutionDetails.user_parameters.warnings.flat().map((warning : any, index: any) => (
                      <BatchManagerExecutionProperty
                        key     = {index}
                        index   = {index}
                        value   = {warning}
                      />
                    ))
                  ) 
                  :
                  (
                    <BatchManagerExecutionProperty name = "No Warnings" style = {{fontWeight : 'normal'}}/>
                  )
                }
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </GraphTalkComponentDiv>
      )
    }

    // ********************************************************************************************************    

    const BatchManagerExecutionItem = (props : any) => {
      const { batchManagerExecutionItem, rowIndex, itemsMap, stickyOffsets } = props;
    
      return (
        <Tr
          tabIndex = {-1}
          style    = {{cursor : 'pointer', fontFamily : 'monospace'}}
          onClick  = {() => onSelectBatchManagerExecutionItem(batchManagerExecutionItem, rowIndex, true)}
        >
          {/* Select checkbox column */}
          <Td
            tabIndex          = {-1}
            className         = "custom-compact-table_padding"
            isStickyColumn
            stickyMinWidth    = "40px"
            stickyLeftOffset  = {stickyOffsets[0]}
            hasRightBorder
            select            = {{
                                  rowIndex    : rowIndex,
                                  onSelect    : (_event, isSelecting) =>
                                                  onSelectBatchManagerExecutionItem(batchManagerExecutionItem, rowIndex, isSelecting),
                                  isSelected  : isBatchExecutionsItemsSelected(batchManagerExecutionItem),
                                  isDisabled  : !isBatchItemsSelectable(batchManagerExecutionItem),
                                }}
          />
          {/* Data columns */}
          {
            itemsMap.map((column : any, columnIndex : number) => {
              const isSticky = columnIndex === 0 || columnIndex === 1;
              const stickyIndex = columnIndex + 1;
      
              return (
                <Td
                  tabIndex          = {-1}
                  key               = {column.key}
                  className         = "custom-compact-table_padding"
                  isStickyColumn    = {isSticky}
                  stickyMinWidth    = {isSticky ? column.width : undefined}
                  stickyLeftOffset  = {isSticky ? stickyOffsets[stickyIndex] : undefined}
                  hasRightBorder    = {isSticky}
                  dataLabel         = {column.key}
                  style             = {{
                                        color         : getDynamicFontColor(column.key, batchManagerExecutionItem[column.key]),
                                        textAlign     : column.align,
                                        padding       : column.padding,
                                        fontSize      : 'inherit',
                                      }}
                >
                {
                  (column.key === null) ? <></> :
                  column.header === 'Finished' || column.header === 'Started' ? (
                    getDates(column.header, batchManagerExecutionItem)
                  ) : (
                    <>
                      {getStatusIcon(column.key, batchManagerExecutionItem[column.key])}
                      <span style = {{paddingLeft : "10px"}}>
                      {
                        displayValue(batchManagerExecutionItem[column.key], column.type)
                      }
                      </span>
                    </>
                  )
                }
                </Td>
              );
          })
        }
        </Tr>
      );
    };
    
    // const BatchManagerExecutionItem = (props : any) => {
    //   const { batchManagerExecutionItem, rowIndex, itemsMap } = props;
    
    //   const stickyOffsets : string[] = [];
    //   let cumulativeWidth = 0;
    
    //   const stickyWidths = ['40px', ...itemsMap.slice(0, 2).map((col) => col.width || '0px')];
    
    //   stickyWidths.forEach((width) => {
    //     stickyOffsets.push(`${cumulativeWidth}px`);
    //     cumulativeWidth += parseInt(width, 10);
    //   });
    
    //   return (
    //     <Tr
    //       tabIndex = {-1}
    //       style    = {{cursor : 'pointer', fontFamily : 'monospace'}}
    //       onClick  = {() => onSelectBatchManagerExecutionItem(batchManagerExecutionItem, rowIndex, true)}
    //     >
    //       <Td
    //         tabIndex          = {-1}
    //         className         = "custom-compact-table_padding"
    //         isStickyColumn
    //         stickyMinWidth    = "40px"
    //         stickyLeftOffset  = {stickyOffsets[0]}
    //         hasRightBorder
    //         select            = {{
    //                               rowIndex   : rowIndex,
    //                               onSelect   : (_event, isSelecting) =>
    //                                              onSelectBatchManagerExecutionItem(batchManagerExecutionItem, rowIndex, isSelecting),
    //                               isSelected : isBatchExecutionsItemsSelected(batchManagerExecutionItem),
    //                               isDisabled : !isBatchItemsSelectable(batchManagerExecutionItem),
    //         }}
    //       />
    
    //       {itemsMap.map((column : any, columnIndex : number) => {
    //         const isSticky = columnIndex === 0 || columnIndex === 1;
    //         const stickyIndex = columnIndex + 1;
    
    //         return (
    //           <Td
    //             tabIndex          = {-1}
    //             key               = {column.key}
    //             className         = "custom-compact-table_padding"
    //             isStickyColumn    = {isSticky}
    //             stickyMinWidth    = {isSticky ? column.width : undefined}
    //             stickyLeftOffset  = {isSticky ? stickyOffsets[stickyIndex] : undefined}
    //             hasRightBorder    = {isSticky}
    //             dataLabel         = {column.key}
    //             style             = {{
    //                                   color         : getDynamicFontColor(column.key, batchManagerExecutionItem[column.key]),
    //                                   textAlign     : column.align,
    //                                   padding       : column.padding,
    //                                   fontSize      : 'inherit',
    //                                 }}
    //           >
    //             {column.header === 'Finished' || column.header === 'Started' ? (
    //               getDates(column.header, batchManagerExecutionItem)
    //             ) : (
    //               <>
    //                 {getStatusIcon(column.key, batchManagerExecutionItem[column.key])}
    //                 {displayValue(batchManagerExecutionItem[column.key], column.type)}
    //               </>
    //             )}
    //           </Td>
    //         );
    //       })}
    //     </Tr>
    //   );
    // };
   
    
    // ********************************************************************************************************

    const handleOnPanelContentResize = (_event : MouseEvent | TouchEvent | React.KeyboardEvent, newWidth : number, id : string) => {
      batchExecutionsPanelContentSize.current = newWidth + "px";
    };

    const batchManagerExecutionsPanelContent = (
      <DrawerPanelContent
        isResizable
        onResize      = {handleOnPanelContentResize}
        id            = "drawerPanelContent"
        defaultSize   = {batchExecutionsPanelContentSize.current}
        minSize       = "10%"
        maxSize       = "50%"
      >
        <DrawerContentBody tabIndex = {-1}>
          <BatchManagerExecutionDetails/>
        </DrawerContentBody>
      </DrawerPanelContent>
    );

    // ********************************************************************************************************

    const BatchManagerExecutionDate = (props : any) => {
      const ddMMyyyyFormat = (date : Date) =>
        (date === undefined || date === null) ? "" : `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;

      const datePickerRef = useRef<any>();

      const dateParse = (value : string) : any => {
        if (value === undefined || value === null) return null;
        const splittedDate = value.split('/').map(Number);
        if (splittedDate.length === 3) {
          const date = new Date(Date.UTC(splittedDate[2], splittedDate[1] - 1, splittedDate[0]));
          date.setHours (0, 0, 0);
          if (isValidDate(date) && value === ddMMyyyyFormat(date)) {
            return date
          }
        }
        return null;
      }

      const changeDate = (value : string, date? : Date | undefined) => {
        if (value !== undefined && value !== null && value.length > 0 && date !== undefined && isValidDate(date) && ddMMyyyyFormat(date) === value)
          props.dateHandler(date);
        else
          props.dateHandler(null);
      }

      const onDateChange = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        if (datePickerRef.current?.isCalendarOpen)
          changeDate(value, date);
      }

      const onDateBlur = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        changeDate(value, date);
      }

      return (
        <DatePicker ref = {datePickerRef}
          id          = {props.id}
          dateFormat  = {ddMMyyyyFormat}
          placeholder = "DD/MM/YYYY"
          dateParse   = {(value : string) => dateParse(value)}
          value       = {(props.date === undefined || props.Date === null) ? undefined : ddMMyyyyFormat(props.date)}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : {fontSize : "12px", padding : "10px 0px 0px 10px"}}}
          onChange    = {onDateChange}
          onBlur      = {onDateBlur}
        />
      )
    }

    // ********************************************************************************************************

    const BatchManagerExecutionTime = (props : any) => {
      const onTimeChange = (event : React.FormEvent<HTMLInputElement>, time : string, hour? : number | undefined, minute? : number | undefined, seconds? : number | undefined, isValid? : boolean | undefined) => {
        if (isValid && time !== null && time.length > 0 && hour != null && minute !== null && seconds !== null) {
          const dateTime = new Date(0);
          dateTime.setHours (hour ? hour : 0, minute ? minute : 0, seconds ? seconds : 0);
          props.timeHandler(dateTime);
        }
        else
          props.timeHandler(null);
      }

      return (
        <TimePicker
          id          = {props.id}
          is24Hour
          includeSeconds
          placeholder = 'HH:mm:ss'
          stepMinutes = {15}
          time        = {(props.time === undefined || props.time === null) ? undefined : props.time}
          onChange    = {onTimeChange}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : {fontSize : "12px", padding : "5px 0px 0px 10px"}}}
        />
      )
    }

    // ********************************************************************************************************

    const [heightDelta, setHeightDelta] = useState<number>(
      getDeltaValue(isSearchExpanded(), isDaySummaryView)
    );
    
    const refreshHeightDelta = () => {
      setHeightDelta(getDeltaValue(isSearchExpanded(), isDaySummaryView));
    };

    // ********************************************************************************************************

    const BatchManagerExecutionsSearch = () => {
      const [searchRefresh, setSearchRefresh] = useState<boolean>(false);      
      const [formRestartDate, setFormRestartDate] = useState<any>(null);
      const forceSearchRefresh = () => { setSearchRefresh(!searchRefresh) }
      const [isNumbersDropdownOpen, setIsNumbersDropdownOpen] = React.useState(false);
  
      const [formSearchId,      setFormSearchId     ] = useState<number|null>((searchId.current      !== undefined) ? searchId.current      : defaultSearchId);
      const [formSearchNum,     setFormSearchNum    ] = useState<number|null>((searchNum.current     !== undefined) ? searchNum.current     : null);
      const [formSearchName,    setFormSearchName   ] = useState<string|null>((searchName.current    !== undefined) ? searchName.current    : null);
      const [formSearchSlice,   setFormSearchSlice  ] = useState<string|null>((searchSlice.current   !== undefined) ? searchSlice.current   : null);
      const [formSearchDateMin, setFormSearchDateMin] = useState<Date  |null>((searchDateMin.current !== undefined) ? searchDateMin.current : null);
      const [formSearchTimeMin, setFormSearchTimeMin] = useState<Date  |null>((searchTimeMin.current !== undefined) ? searchTimeMin.current : null);
      const [formSearchDateMax, setFormSearchDateMax] = useState<Date  |null>((searchDateMax.current !== undefined) ? searchDateMax.current : null);
      const [formSearchTimeMax, setFormSearchTimeMax] = useState<Date  |null>((searchTimeMax.current !== undefined) ? searchTimeMax.current : null);

      const handleFormSearchDateMin = (date : any) => {
        setFormSearchDateMin((date !== undefined && date !== null) ? date : defaultSearchDateMin);
      }

      const handleFormSearchTimeMin = (time : any) => {
        setFormSearchTimeMin((time !== undefined && time !== null) ? time : defaultSearchTimeMin);
      }

      const handleFormSearchDateMax = (date : any) => {
        setFormSearchDateMax((date !== undefined && date !== null) ? date : defaultSearchDateMax);
      }

      const handleFormSearchTimeMax = (time : any) => {
        setFormSearchTimeMax((time !== undefined && time !== null) ? time : defaultSearchTimeMax);
      }

      const formSearchReset = () => {
        setFormSearchNum(defaultSearchNum);
        setFormSearchId(defaultSearchId);
        setFormSearchName(defaultSearchName);
        setFormSearchSlice(defaultSearchSlice);
        setFormSearchDateMin(defaultSearchDateMin);
        setFormSearchTimeMin(defaultSearchTimeMin);
        setFormSearchDateMax(defaultSearchDateMax);
        setFormSearchTimeMax(defaultSearchTimeMax);
        computeBatchManagerExecutions();
      }
  
      const onSearchSubmit = () => {
        searchNum.current     = formSearchNum     ? formSearchNum     : defaultSearchNum;
        searchId.current      = formSearchId      ? formSearchId      : defaultSearchId;
        searchName.current    = formSearchName    ? formSearchName    : defaultSearchName;
        searchSlice.current   = formSearchSlice   ? formSearchSlice   : defaultSearchSlice;
        searchDateMin.current = formSearchDateMin ? formSearchDateMin : defaultSearchDateMin;
        searchTimeMin.current = formSearchTimeMin ? formSearchTimeMin : defaultSearchTimeMin;
        searchDateMax.current = formSearchDateMax ? formSearchDateMax : defaultSearchDateMax;
        searchTimeMax.current = formSearchTimeMax ? formSearchTimeMax : defaultSearchTimeMax;
        computeBatchManagerExecutions();
      }

      const onSearchReset = () => {
        searchReset();
        formSearchReset();
        forceSearchRefresh();
      }

      const onToggleSearchExpanded = (_event : React.MouseEvent, isExpanded : boolean) => {
        setSearchExpanded(isExpanded);
        forceSearchRefresh();
        refreshHeightDelta();
      };

      const onSearchNumChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        const count = (allBatchExecutionsCount.computedData.batch_executions?.count) ? allBatchExecutionsCount.computedData.batch_executions.count : null;
        debugger
        setFormSearchNum(
          value !== undefined && value !== null && value !== "Infinity"
            ? parseInt(value)
            : count !== null
            ? count
            : defaultSearchNum
        );
      }

      const onSearchIdChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchId((value !== undefined && value !== null) ? parseInt(value) : defaultSearchId);
      }

      const onSearchNameChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchName((value !== undefined && value !== null) ? value : defaultSearchName);
      }

      const onSearchSliceChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchSlice((value !== undefined && value !== null) ? value : defaultSearchSlice);
      }

      async function retrievBatchManagerExecutionRestartDate(newDateArg? : any) : Promise<any> {        
        const args : any = {"_attributes" : "_none, uri, restart_date", "_count" : 0, "_num" : 1, "_start" : 0};
        const mergedArgs = {...args, ...newDateArg};
        await refreshJolokia();

        let json : any = null;
        if (jolokia !== null) {
            const response = await jolokia.search(adminPath);
            if (response != null && response.length > 0) {
              const mbean = response[0];
              const operation = retrieveBatchManagerExecutionsSignature;
              log.debug(logPrefix, "retrieveBatchManagerExecutions, execute : ", JSON.stringify(mergedArgs));
              const result = await jolokia.execute(mbean, operation, JSON.stringify(mergedArgs));  
              json = (result === null) ? null : JSON.parse(result.toString());
            }
        }

        log.info(logPrefix, "retrievBatchManagerExecutionRestartDate, json: ", json);

        let restart_date = (json?.batch_executions?.items === undefined || json?.batch_executions?.items === null ||json?.batch_executions?.items[0] === undefined || json?.batch_executions?.items[0] === null) ? null : json.batch_executions.items[0].restart_date;
        log.info(logPrefix, "retrievBatchManagerExecutionRestartDate, restart_date: ", restart_date);
        
        setFormRestartDate(restart_date);
        
        log.info(logPrefix, "retrievBatchManagerExecutionRestartDate, formRestartDate: ", formRestartDate);
      }

      useEffect(() => {
        if (formRestartDate !== null) {
          log.info(logPrefix, "formRestartDate has been updated: ", formRestartDate);
          onToday(); 
        }
      }, [formRestartDate]);


      const onToday = () => {
        const today = new Date();
        let currentDayStart = new Date();
        let currentDayEnd = new Date();

        if (typeof formRestartDate === "number") {
          const args : any = { "restart_date" : formRestartDate, "_attributes" : "step_counter, error_counter, warning_counter" }
          handleFormSearchDateMax(JulianDateTime.julianToDate(formRestartDate));
          searchDateMax.current = JulianDateTime.julianToDate(formRestartDate);
          handleFormSearchDateMin(JulianDateTime.julianToDate(formRestartDate));
          searchDateMin.current = JulianDateTime.julianToDate(formRestartDate);
          computeBatchManagerExecutions(args)
        }
        else {
          currentDayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          currentDayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate());

          handleFormSearchDateMin(currentDayStart);
          handleFormSearchDateMax(currentDayEnd);

          searchDateMin.current = currentDayStart;
          searchDateMax.current = currentDayEnd;
          computeBatchManagerExecutions();
        }
      };

      const onPrevious = () => {
        let julianDateMax : number | null;
        let newDateMax : number | null;
        if (searchDateMax.current instanceof Date && !isNaN(searchDateMax.current.getTime())){
          julianDateMax = JulianDateTime.dateToJulian(searchDateMax.current);
          newDateMax = julianDateMax - 1;
        } 
        else {
          julianDateMax = JulianDateTime.dateToJulian(new Date());
          newDateMax = julianDateMax - 1;
        }
        const arg : any = {"_sort" : "-restart_date", "date_max" : newDateMax};
        retrievBatchManagerExecutionRestartDate(arg);
      };

      const onNext = () => {
        let julianDateMin : number | null;
        let newDateMin : number | null;
        if (searchDateMin.current instanceof Date && !isNaN(searchDateMin.current.getTime())) {
          julianDateMin = JulianDateTime.dateToJulian(searchDateMin.current);
          newDateMin = julianDateMin + 1;;
          const arg : any = {"_sort" : "restart_date", "date_min" : newDateMin };
          retrievBatchManagerExecutionRestartDate(arg);
        } 
        else {
          onToday();
        }    
      };

      const PrevNextTodayButtons = () => {
        return (
          <>
            <GridItem tabIndex = {-1} span = {2}/>
            <GridItem tabIndex = {-1} span = {1}>
              <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onPrevious()}
                  >
                    &lt; Prev.
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onNext()}
                  >
                    Next &gt;
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onToday()}
                  >
                    Today
                  </Button>
                </SplitItem>
              </Split>
            </GridItem>
          </>
        );
      }
      useEffect(() => {
      }, [isDaySummaryView]); 
  
      const handleCheckboxChange = (checked : boolean) => {
        setIsDaySummaryView(checked);
      };

      const selectedNumbersLabel = formNumbersList.find((j) => j.value === formSearchNum)?.content || "All";
      
      const onFormNumbersSelect = (_event : React.MouseEvent<Element, MouseEvent> | undefined, value : string | number | undefined) => {
        if (typeof value === "number") {
          onSearchNumChange({} as React.FormEvent<HTMLInputElement>, value.toString());
        }
        setIsNumbersDropdownOpen(false);
      };
      
      return (
        <div style = {{position : 'relative'}}>
          <ExpandableSection toggleText = "Search Criteria" isIndented isExpanded = {isSearchExpanded()} onToggle = {onToggleSearchExpanded}>
            <Grid tabIndex = {-1} hasGutter = {false}>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>Number of batches</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <Dropdown
                      isOpen        = {isNumbersDropdownOpen}
                      onSelect      = {onFormNumbersSelect}
                      onOpenChange  = {(isOpen : boolean) => setIsNumbersDropdownOpen(isOpen)}
                      toggle        = {(toggleRef : React.Ref<MenuToggleElement>) => (
                                        <MenuToggle className = "font-size-13" ref = {toggleRef} onClick = {() => setIsNumbersDropdownOpen(!isNumbersDropdownOpen)} isExpanded = {isNumbersDropdownOpen}>
                                          {selectedNumbersLabel}
                                        </MenuToggle>
                                      )}
                      ouiaId = "JobsDropdown"
                      shouldFocusToggleOnSelect
                    >
                      <DropdownList>
                      {
                        formNumbersList.map((option, index) => (
                          <DropdownItem key = {index} value = {option.value}>
                            {option.content}
                          </DropdownItem>
                        ))
                      }
                      </DropdownList>
                    </Dropdown>                   
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {4}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>{getAllBatchExecutionsCount()}</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "search_box_titles_size_padding" tabIndex = {-1} style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>Date between</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <BatchManagerExecutionDate id = "searchDateMin" date = {formSearchDateMin} dateHandler = {handleFormSearchDateMin}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <BatchManagerExecutionTime id = "searchTimeMin" time = {formSearchTimeMin} timeHandler = {handleFormSearchTimeMin}/>
                      </SplitItem>
                    </Split>
                  </GridItem>
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>Id</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextInput
                      className = "font-size-13"
                      id        = "searchId"
                      type      = "number"
                      value     = {formSearchId ? formSearchId : ""}
                      onChange  = {onSearchIdChange}
                    />
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {4}/>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "search_box_titles_size_padding_60" tabIndex = {-1} style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>and</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <BatchManagerExecutionDate id = "searchDateMax" date = {formSearchDateMax} dateHandler = {handleFormSearchDateMax}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <BatchManagerExecutionTime id = "searchTimeMax" time = {formSearchTimeMax} timeHandler = {handleFormSearchTimeMax}/>
                      </SplitItem>
                    </Split>
                  </GridItem>
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>Name</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {6}>
                    <TextInput
                      className = "font-size-13"
                      id        = "searchName"
                      type      = "text"
                      value     = {formSearchName ? formSearchName : ""}
                      onChange  = {onSearchNameChange}
                    />
                  </GridItem>
                  {isSearchExpanded() && <PrevNextTodayButtons/>}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>Slice Controller</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {6}>
                    <TextInput
                      className = "font-size-13"
                      id        = "searchSlice"
                      type      = "text"
                      value     = {formSearchSlice ? formSearchSlice : ""}
                      onChange  = {onSearchSliceChange}
                    />
                  </GridItem>
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
                <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <SplitItem tabIndex = {-1} isFilled >
                  </SplitItem>
                  <SplitItem tabIndex = {-1} >
                    <Button
                      className = "font-size-13"
                      variant   = "primary"
                      size      = "xs"
                      onClick   = {() => onSearchSubmit()}
                      icon      = {<SearchIcon className = "font-size-13"/>}
                    >
                      Search
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1} >
                    <Button
                      className = "font-size-13"
                      variant   = "secondary"
                      size      = "xs"
                      onClick   = {() => onSearchReset()}
                      icon      = {<EraserIcon className = "font-size-13"/>}
                    >
                      Reset
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                  </SplitItem>
                </Split>
              </GridItem>
            </Grid>
          </ExpandableSection>
          <div  className = "top-right-corner">
            <div style = {{position : 'relative'}}>
              {!isSearchExpanded() && (
                <div className = "prev-next-today-container">
                  <PrevNextTodayButtons/>
                </div>
              )}
            </div>
            <div className = "checkbox-title-container">
              <TextContent tabIndex = {-1} className = "day_summary_style">
                <Text tabIndex = {-1} component = {TextVariants.p}>
                  Day Summary
                </Text>
              </TextContent>
            </div>
            <div className = "checkbox-container">
              <Checkbox
                id        = "checkbox-reversed"
                name      = "checkbox-reversed"
                onChange  = {(_, checked) => handleCheckboxChange(checked)}
                isChecked = {isDaySummaryView}
                className = "font-size-12"
              />
            </div>
          </div>
        </div>
      )
    }

    //*********************************************************************************************************

    const BatchTablesSeparatedByStatus = () => {
      const completedItems = sortedBatchManagerExecutionsItems.filter(
        (item : any) => completedStatuses.includes(item.status)
      );

      const uncompletedItems = sortedBatchManagerExecutionsItems.filter(
        (item : any) => uncompletedStatuses.includes(item.status)
      );
    
      const [topTableHeight, setTopTableHeight] = useState(isSearchExpanded() ? TOP_TABLE_HEIGHT_EXPANDED : TOP_TABLE_HEIGHT_COLLAPSED);
      const [dragging, setDragging] = useState(false);
      const [startY, setStartY] = useState(0);
      const [initialHeight, setInitialHeight] = useState(0);
    
      const startResize = (e : React.MouseEvent) => {
        setDragging(true);
        setStartY(e.clientY);
        setInitialHeight(topTableHeight);
        document.body.style.userSelect = 'none';
      };
    
      const handleResize = (e : MouseEvent) => {
        if (dragging) {
          const diff = e.clientY - startY;
          const newHeight = initialHeight + (diff / window.innerHeight) * 100;
    
          if (newHeight > 10 && newHeight < 90) {
            setTopTableHeight(newHeight);
          }
        }
      };
    
      const stopResize = () => {
        setDragging(false);
        document.body.style.userSelect = '';
      };
    
      useEffect(() => {
        if (dragging) {
          window.addEventListener('mousemove', handleResize);
          window.addEventListener('mouseup', stopResize);
        }
        return () => {
          window.removeEventListener('mousemove', handleResize);
          window.removeEventListener('mouseup', stopResize);
        };
      }, [dragging]);

      const stickyWidths = ['40px', ...batchManagerExecutionsItemsMap.slice(0, 2).map(col => col.width || '0px')];
      const stickyOffsets : string[] = [];
      let cumulativeWidth = 0;
    
      stickyWidths.forEach(width => {
        stickyOffsets.push(`${cumulativeWidth}px`);
        cumulativeWidth += parseFloat(width);
      });

      return (
        <GraphTalkComponentDiv delta = {heightDelta} className = "flex-column-container" >
          {/* Completed Table Section */}
          <div
            style = {{
              flexBasis     : `${topTableHeight}%`,
              flexGrow      : 0,
              flexShrink    : 0,
              overflow      : 'hidden',
              display       : 'flex',
              flexDirection : 'column',
            }}
          >
            <Divider tabIndex = {-1} component = "hr" className = "padding-top-10"/>
            <Split tabIndex = {-1} hasGutter style = {{alignItems : 'center'}}>
              <SplitItem tabIndex = {-1} className = "padding-left-10">
                <TextContent tabIndex = {-1}>
                  <Text className = "font-size-13" tabIndex = {-1}>Completed:</Text>
                </TextContent>
              </SplitItem>
              <SplitItem tabIndex = {-1}>
                <Badge tabIndex = {-1} className = "font-size-12">{completedItems.length}</Badge>
              </SplitItem>
            </Split>
      
            <div style = {{flex : 1, overflow : 'auto'}}>
              <div className = "table-scroll-container">
                <Table
                  tabIndex    = {-1}
                  className   = "custom-compact-table"
                  isStriped   = {completedItems.length > 0}
                  aria-label  =  "Completed Batch Executions"
                  variant     = "compact"
                  borders     = {false}
                  isStickyHeader
                >
                  <Thead tabIndex = {-1}>
                    <Tr tabIndex = {-1}>
                      <Th
                        tabIndex          = {-1}
                        isStickyColumn
                        stickyMinWidth    = "40px"
                        stickyLeftOffset  =  {stickyOffsets[0]}
                        hasRightBorder
                        select            = {{
                                              onSelect : (_event, isSelecting) => selectAllBatchExecutionsItems(isSelecting),
                                              isSelected : areSeveralBatchExecutionsItemsSelected(),
                                            }}
                        aria-label = "Batch Completed Table Select"
                      />
                      {
                        batchCompletedExecutionsItemsMap.map((column : any, columnIndex : number) => {
                          const isSticky = columnIndex === 0 || columnIndex === 1;
                          return (
                            <Th
                              tabIndex          = {-1}
                              key               = {column.key}
                              className         = "pf-v5-c-table__th truncate-header"
                              isStickyColumn    = {isSticky}
                              stickyMinWidth    = {isSticky ? column.width : undefined}
                              stickyLeftOffset  = {isSticky ? stickyOffsets[columnIndex + 1] : undefined}
                              hasRightBorder    = {isSticky}
                              sort              = {(column.key === null) ? undefined : getSortParams(columnIndex)}
                              modifier          = "truncate"
                              style             = {{
                                                    justifyItems  : column.align,
                                                    width         : column.width,
                                                    minWidth      : column.width,
                                                  }}
                            >
                              {column.header}
                            </Th> 
                          );
                        })
                      }
                    </Tr>
                  </Thead>
                  <Tbody tabIndex = {-1}>
                  {
                    completedItems.length > 0 ? (
                      completedItems.map((item : any, index : number) => (
                        <BatchManagerExecutionItem 
                          key                       = {index} 
                          batchManagerExecutionItem = {item} 
                          rowIndex                  = {index} 
                          itemsMap                  = {batchCompletedExecutionsItemsMap}     
                          stickyOffsets             = {stickyOffsets}                     
                        />
                      ))
                    ) 
                    :
                    (
                      <Tr tabIndex = {-1}>
                        <Td tabIndex = {-1} colSpan = {batchCompletedExecutionsItemsMap.length + 1}>
                          <Bullseye tabIndex = {-1}>
                            <EmptyState tabIndex = {-1} variant = {EmptyStateVariant.xs}>
                              <EmptyStateHeader tabIndex = {-1} titleText = "No Completed Batch Executions Found" icon = {<EmptyStateIcon icon = {SearchIcon} className = "font-size-14"/>}/>
                            </EmptyState>
                          </Bullseye>
                        </Td>
                      </Tr>
                    )
                  }
                  </Tbody>
                </Table>
              </div>
            </div>
          </div>
      
          {/* Divider for resizing */}
          <div
            className = "resize-divider"
            style = {{
                      height          : '10px',
                      cursor          : 'ns-resize',
                      display         : 'flex',
                      alignItems      : 'center',
                      justifyContent  : 'center',
                      flexShrink      : 0,
                    }}
            onMouseDown = {startResize}
          >
            <div style = {{display : 'flex', flexDirection : 'column', gap : '2px'}}>
              <div className = "detach-icon-color" style = {{width : '15px', height : '1px', borderRadius : '1px'}}/>
              <div className = "detach-icon-color" style = {{width : '15px', height : '1px', borderRadius : '1px'}}/>
            </div>
          </div>
      
          {/* Uncompleted Table Section */}
          <div
            style = {{
              flexBasis     : `${100 - topTableHeight}%`,
              flexGrow      : 0,
              flexShrink    : 0,
              overflow      : 'hidden',
              display       : 'flex',
              flexDirection : 'column',
            }}
          >
            <Divider tabIndex = {-1} component = "hr" className = "padding-top-10"/>
            <Split tabIndex = {-1} hasGutter style = {{alignItems : 'center'}}>
              <SplitItem tabIndex = {-1} className = "padding-left-10">
                <TextContent tabIndex = {-1}>
                  <Text className = "font-size-13" tabIndex = {-1}>Uncompleted:</Text>
                </TextContent>
              </SplitItem>
              <SplitItem tabIndex = {-1}>
                <Badge tabIndex = {-1} className = "font-size-12">{uncompletedItems.length}</Badge>
              </SplitItem>
            </Split>
      
            <div style = {{flex : 1, overflow : 'auto'}}>
              <div className = "table-scroll-container">
                <Table
                  tabIndex    = {-1}
                  className   = "custom-compact-table"
                  isStriped   = {uncompletedItems.length > 0}
                  aria-label  = "Uncompleted Batch Executions"
                  variant     = "compact"
                  borders     = {false}
                  isStickyHeader
                >
                  <Thead tabIndex = {-1}>
                    <Tr tabIndex = {-1}>
                      <Th
                        tabIndex          = {-1}
                        isStickyColumn
                        stickyMinWidth    = "40px"
                        stickyLeftOffset  = {stickyOffsets[0]}
                        hasRightBorder
                        select            = {{
                                              onSelect : (_event, isSelecting) => selectAllBatchExecutionsItems(isSelecting),
                                              isSelected : areSeveralBatchExecutionsItemsSelected(),
                                            }}
                        aria-label        = "Batch Uncompleted Table Select"
                      />
                      {
                        batchUncompletedExecutionsItemsMap.map((column : any, columnIndex : number) => {
                          const isSticky = columnIndex === 0 || columnIndex === 1;
                          return (
                            <Th
                              tabIndex          = {-1}
                              key               = {column.key}
                              className         = "pf-v5-c-table__th truncate-header"
                              isStickyColumn    = {isSticky}
                              stickyMinWidth    = {isSticky ? column.width : undefined}
                              stickyLeftOffset  = {isSticky ? stickyOffsets[columnIndex + 1] : undefined}
                              hasRightBorder    = {isSticky}
                              sort              = {(column.key === null) ? undefined : getSortParams(columnIndex)}
                              modifier          = "truncate"
                              style             = {{
                                                    justifyItems  : column.align,
                                                    width         : column.width,
                                                    minWidth      : column.width,
                                                  }}
                            >
                              {column.header}
                            </Th>
                          );
                        })
                      }
                    </Tr>
                  </Thead>
                  <Tbody tabIndex = {-1}>
                  {
                    uncompletedItems.length > 0 ? (
                      uncompletedItems.map((item : any, index : number) => (
                        <BatchManagerExecutionItem 
                          key                       = {index} 
                          batchManagerExecutionItem = {item} 
                          rowIndex                  = {index} 
                          itemsMap                  = {batchUncompletedExecutionsItemsMap}     
                          stickyOffsets             = {stickyOffsets}                     
                        />
                      ))
                    ) 
                    :
                    (
                      <Tr tabIndex = {-1}>
                        <Td tabIndex = {-1} colSpan = {batchUncompletedExecutionsItemsMap.length + 1}>
                          <Bullseye tabIndex = {-1}>
                            <EmptyState tabIndex = {-1} variant = {EmptyStateVariant.xs}>
                              <EmptyStateHeader tabIndex = {-1} titleText = "No Uncompleted Batch Executions Found" icon = {<EmptyStateIcon icon = {SearchIcon} className = "font-size-14"/>}/>
                            </EmptyState>
                          </Bullseye>
                        </Td>
                      </Tr>
                    )
                  }
                  </Tbody>
                </Table>
              </div>
            </div>
          </div>
        </GraphTalkComponentDiv>
      );      
    };

    const BatchManagerExecutionsTable = () => {
      const stickyWidths = ['40px', ...batchManagerExecutionsItemsMap.slice(0, 2).map(col => col.width || '0px')];
      const stickyOffsets : string[] = [];
      let cumulativeWidth = 0;
    
      stickyWidths.forEach(width => {
        stickyOffsets.push(`${cumulativeWidth}px`);
        cumulativeWidth += parseFloat(width);
      });
    
      return (
        <div className = "flex-column-container" style = {{height : isSearchExpanded() ? HEIGHT_SEARCH_EXPANDED : HEIGHT_SEARCH_COLLAPSED}}>
          <Divider tabIndex = {-1} component = "hr" className = "padding-top-10"/>
          <Split tabIndex = {-1} hasGutter style = {{alignItems : 'center'}}>
            <SplitItem tabIndex = {-1} className = "padding-left-10">
              <TextContent tabIndex = {-1}>
                <Text className = "font-size-13" tabIndex = {-1}>Selected:</Text>
              </TextContent>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <Badge tabIndex = {-1} className = "font-size-12">{selectableBatchExecutionsItemsNums.length}</Badge>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <BatchManagerExecutionsDelete/>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <BatchManagerExecutionsUpdateStatus/>
            </SplitItem>
            <SplitItem tabIndex = {-1} isFilled>
              <BatchManagerExecutionsPagination/>
            </SplitItem>
          </Split>
    
          <GraphTalkComponentDiv delta = {heightDelta}>
            <InnerScrollContainer>
              <Table
                className   = "custom-compact-table"
                tabIndex    = {-1}
                isStriped   = {sortedBatchManagerExecutionsItems.length > 0}
                aria-label  = "Batch Executions table"
                variant     = "compact"
                borders     = {false}
                isStickyHeader
              >
                <Thead tabIndex = {-1}>
                  <Tr tabIndex = {-1}>
                    <Th
                      tabIndex          = {-1}
                      isStickyColumn
                      stickyMinWidth    = "40px"
                      stickyLeftOffset  = {stickyOffsets[0]}
                      hasRightBorder
                      select            = {{
                                            onSelect : (_event, isSelecting) => selectAllBatchExecutionsItems(isSelecting),
                                            isSelected : areSeveralBatchExecutionsItemsSelected(),
                                          }}
                      aria-label        = "Batch Executions Table Select"
                    />
    
                    {batchManagerExecutionsItemsMap.map((column : any, columnIndex : number) => {
                      const isSticky = columnIndex === 0 || columnIndex === 1;
                      return (
                        <Th
                          key               = {column.key}
                          className         = "pf-v5-c-table__th truncate-header"
                          tabIndex          = {-1}
                          isStickyColumn    = {isSticky}
                          stickyMinWidth    = {isSticky ? column.width : undefined}
                          stickyLeftOffset  = {isSticky ? stickyOffsets[columnIndex + 1] : undefined}
                          hasRightBorder    = {isSticky}
                          sort              = {getSortParams(columnIndex)}
                          modifier          = "truncate"
                          style             = {{
                                                width         : column.width,
                                                minWidth      : column.width,
                                                justifyItems  : column.align,
                                              }}
                        >
                          {column.header}
                        </Th>
                      );
                    })}
                  </Tr>
                </Thead>
    
                <Tbody tabIndex = {-1}>
                {
                  sortedBatchManagerExecutionsItems.length > 0 ?
                  (
                    sortedBatchManagerExecutionsItems.slice(indexStart, indexEnd).map((item : any, index : number) => (
                      <BatchManagerExecutionItem
                        key                       = {index}
                        tabIndex                  = {-1}
                        batchManagerExecutionItem = {item}
                        rowIndex                  = {index}
                        itemsMap                  = {batchManagerExecutionsItemsMap}
                        stickyOffsets             = {stickyOffsets}
                      />
                    ))
                  )
                  :
                  (
                    <Tr tabIndex = {-1}>
                      <Td tabIndex = {-1} colSpan = {batchManagerExecutionsItemsMap.length + 1}>
                        <Bullseye tabIndex = {-1}>
                          <EmptyState tabIndex = {-1} variant = {EmptyStateVariant.xs}>
                            <EmptyStateHeader
                              tabIndex  = {-1}
                              titleText = "No Batch Executions Found"
                              icon      = {<EmptyStateIcon icon = {SearchIcon} className = "font-size-14"/>}
                            />
                          </EmptyState>
                        </Bullseye>
                      </Td>
                    </Tr>
                  )
                }
                </Tbody>
              </Table>
            </InnerScrollContainer>
          </GraphTalkComponentDiv>
        </div>
      );
    };

    // ********************************************************************************************************

    const batchManagerExecutionsCount = () : any => {
      const count = (batchManagerExecutions.computedData.batch_executions?.count) ? batchManagerExecutions.computedData.batch_executions.count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+") 
          return "at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"
        else
          return scount + " items";
      }
      else
        return ""
    }

    const getAllBatchExecutionsCount = () : any => {
      const count = (allBatchExecutionsCount.computedData.batch_executions?.count) ? allBatchExecutionsCount.computedData.batch_executions.count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+") 
            return "from at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"                  
        else
        if (count < 500)
          return "from " + scount + " items";
        else
          return "from at least " + scount + " items";
      }
      else
        return ""
    }

    // ********************************************************************************************************

    const [page,       setPage      ] = useState<number | null>(1);
    const [indexStart, setIndexStart] = useState<number | null>(0);
    const [indexEnd,   setIndexEnd  ] = useState<number | null>(getPerPage());
    const [refreshPagintion, setRefreshPagination] = useState<boolean>(false);

    const toggleRefreshPagination = () => {
      setRefreshPagination(!refreshPagintion);
    }
  
    const BatchManagerExecutionsPagination = () => {
      
      const handleSetPage = (
        _event      : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPage     : number,
        _perPage    : number | undefined,
        startIdx    : number | undefined,
        endIdx      : number | undefined
      ) => {
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
      };
  
      const handlePerPageSelect = (
        _event      : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPerPage  : number,
        newPage     : number,
        startIdx    : number | undefined,
        endIdx      : number | undefined
      ) => {
        setPerPage(newPerPage);
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
        toggleRefreshPagination();
      };

      return (
        <Pagination
          className         = "pf-v5-c-menu-toggle.pf-m-plain"
          isCompact         = {false}
          itemCount         = {sortedBatchManagerExecutionsItems.length}
          toggleTemplate    = {({firstIndex, lastIndex}) => (<><b>{firstIndex} - {lastIndex}</b> of <b>{batchManagerExecutionsCount()}</b></>)}
          perPage           = {getPerPage()}
          page              = {(page !== null) ? page : undefined}
          onSetPage         = {handleSetPage}
          onPerPageSelect   = {handlePerPageSelect}
        />
      )
    }

    // ********************************************************************************************************

    async function updateBatchManagerExecutionStatus (batchManagerExecutionNum : any, status : any) {
      if (!context.canUpdateBatchManagerExecutionStatus)
        return;

      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }
  
      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "updateBatchManagerExecutionStatus(java.lang.String,java.lang.String)";
          const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum, status);
          log.debug(logPrefix, "updateBatchManagerExecutionStatus, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function updateBatchManagerExecutionsStatus (status : any) {
      if (!context.canUpdateBatchManagerExecutionStatus)
        return;

      if (!areSeveralBatchExecutionsItemsSelected()) return;

      for (let selectedBatchExecutionsItemsNumsIndex = 0; selectedBatchExecutionsItemsNumsIndex < selectableBatchExecutionsItemsNums.length; selectedBatchExecutionsItemsNumsIndex++) {
        const batchManagerExecutionNum = selectableBatchExecutionsItemsNums[selectedBatchExecutionsItemsNumsIndex];
        await updateBatchManagerExecutionStatus(batchManagerExecutionNum, status);
      }

      await computeBatchManagerExecutions();
    }

    // ********************************************************************************************************

    const BatchManagerExecutionsUpdateStatus = () => {
      if (!context.canUpdateBatchManagerExecutionStatus)
        return <></>;

      if (!areSeveralBatchExecutionsItemsSelected())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const [selectedStatus, setSelectedStatus] = useState<any>(null);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      async function handleModalUpdate () {
        await updateBatchManagerExecutionsStatus(selectedStatus)
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      const handleStatus = (event : any, checked : boolean) => {
        setSelectedStatus(event.target.id)
      }

      return (
        <>
          <Tooltip content = {"Update status of the selected Batch Execution(s)"}>
            <Button
              isDisabled  = {!areSeveralBatchExecutionsItemsSelected()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {<Icon status = "warning" iconSize = "md"><EditIcon/></Icon>}
            />
          </Tooltip>
          <Modal
            titleIconVariant      = "warning"
            variant               = {ModalVariant.small}
            isOpen                = {isModalOpen}
            onClose               = {handleModalCancel}
            title                 = {"Update status of the " + ((selectableBatchExecutionsItemsNums.length > 1) ? selectableBatchExecutionsItemsNums.length + " Batch Executions" : " Batch Execution")}
            actions               = {[
                                      <Button key = "confirm" variant = "warning"   isBlock size = "sm" onClick = {handleModalUpdate} isDisabled = {selectedStatus === null}>Update</Button>,
                                      <Button key = "cancel"  variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
                                    ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md" style = {{paddingBottom : "12px"}}>Select status:</Title>
            {
              executionStatuses.map((status : any) =>
                <Radio
                  tabIndex  = {0}
                  key       = {status.id}
                  name      = "status"
                  onChange  = {(event, checked) => handleStatus(event, checked)}
                  isChecked = {status.id === selectedStatus}
                  label     = {status.label}
                  id        = {status.id}
                  color     = {status.color}
                  style     = {{color : status.color}}
                />
            )
            }
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    async function deleteBatchManagerExecution (batchManagerExecutionNum : any) {
      if (!context.canDeleteBatchManagerExecution)
        return;

      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }
  
      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "deleteBatchManagerExecution(java.lang.String)";
          const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum);
          log.debug(logPrefix, "deleteBatchManagerExecution, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function deleteSelectedBatchManagerExecutions () {
      if (!areSeveralBatchExecutionsItemsSelected())
        return;

      for (let selectedBatchExecutionsItemsNumsIndex = 0; selectedBatchExecutionsItemsNumsIndex < selectableBatchExecutionsItemsNums.length; selectedBatchExecutionsItemsNumsIndex++) {
        const batchManagerExecutionNum = selectableBatchExecutionsItemsNums[selectedBatchExecutionsItemsNumsIndex];
        await deleteBatchManagerExecution(batchManagerExecutionNum);
      }

      await computeBatchManagerExecutions();
    }

    // ********************************************************************************************************

    const BatchManagerExecutionsDelete = () => {
     
      if (!context.canDeleteBatchManagerExecution)
        return <></>;

      if (!areSeveralBatchExecutionsItemsSelected())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      async function handleModalDelete () {
        await deleteSelectedBatchManagerExecutions();
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      return (
        <>
          <Tooltip content = {"Delete the selected Batch Execution (s)"}>
            <Button
              isDisabled  = {!areSeveralBatchExecutionsItemsSelected()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {<Icon status = "danger" iconSize = "md"><TrashIcon/></Icon>}
            />
          </Tooltip>
          <Modal
            titleIconVariant      = "danger"
            variant               = {ModalVariant.small}
            isOpen                = {isModalOpen}
            onClose               = {handleModalCancel}
            title                 = {"Delete the " + ((selectableBatchExecutionsItemsNums.length > 1) ? selectableBatchExecutionsItemsNums.length + " Batch Manager Executions" : " Batch Manager Execution")}
            actions               = {[
                                      <Button key = "confirm" variant = "danger"    isBlock size = "sm" onClick = {handleModalDelete}>Delete</Button>,
                                      <Button key = "cancel"  variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
                                    ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md">
            {
              (selectableBatchExecutionsItemsNums.length > 1)
                ? "All the " + selectableBatchExecutionsItemsNums.length + " selected Batch Executions will be permanently deleted"
                : "The selected Batch Execution will be permanently deleted"
            }
            </Title>
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    const formatBatchManagerExecutionAsText = (batchManagerExecutionJson : any) : String[] => {
			const batchManagerExecutionText : String[] = [];

      try {
        batchManagerExecutionText.push(" ------------------------------- \n");
        batchManagerExecutionText.push("|  Graphtalk Application Error  |\n");
        batchManagerExecutionText.push(" ------------------------------- \n\n");

        const batch_execution = batchManagerExecutionJson.batch_execution;

        batchManagerExecutionText.push (
            "The batch #" + batch_execution.execution_id + " was reported by " + batch_execution.userId
          + " on " + JulianDateTime.formatJulianAsDate(batch_execution.startDate)
          + " at " + JulianDateTime.formatJulianAsTime(batch_execution.startTime)
          + "\n"
        );
        batchManagerExecutionText.push ("Language: " + batch_execution.language + "\n");
        batchManagerExecutionText.push ("Level: " + batch_execution.level + "\n");
        batchManagerExecutionText.push ("Inner Error Number: " + batch_execution.inner_error_number + "\n\n");

        const batchManagerExecutionBody = (batch_execution?.detail) ? batch_execution.detail : null;
        const batchManagerExecutionBodyLength = batchManagerExecutionBody.length;

        for (let batchManagerExecutionMsgIndex = batchManagerExecutionBodyLength - 1; batchManagerExecutionMsgIndex >= 0; batchManagerExecutionMsgIndex--) {
          const batchManagerExecutionMsg       = batchManagerExecutionBody[batchManagerExecutionMsgIndex]
          const batchManagerExecutionMsgHeader = batchManagerExecutionMsg[0];
          const batchManagerExecutionMsgBody   = batchManagerExecutionMsg[1];

          batchManagerExecutionText.push (batchManagerExecutionMsgHeader + "\n");
          batchManagerExecutionText.push ("-------------------------------------------------------------------------------\n");

          for (let batchManagerExecutionMsgBodyIndex = 0; batchManagerExecutionMsgBodyIndex < batchManagerExecutionMsgBody.length; batchManagerExecutionMsgBodyIndex++) {
            batchManagerExecutionText.push(batchManagerExecutionMsgBody[batchManagerExecutionMsgBodyIndex] + "\n");
          }

          batchManagerExecutionText.push("\n");
        };

        batchManagerExecutionText.push ("-------------------------------------------------------------------------------\n");
        batchManagerExecutionText.push ("-------------------------------------------------------------------------------\n");
        batchManagerExecutionText.push ("Related Request: " + batch_execution.request + "\n");
      }
      catch (error) {
				batchManagerExecutionText.push ("ERROR IN JSON: " + error + "\n\n");
				batchManagerExecutionText.push (batchManagerExecutionJson.toString());
				batchManagerExecutionText.push ("\n");
      }

      return batchManagerExecutionText;
    }

    // ********************************************************************************************************

    const BatchManagerExecutionDownload = () => {
      if (selectableBatchExecutionsItemsNums.length !== 1)
          return <></>;

      const batchManagerExecutionNum = selectableBatchExecutionsItemsNums[0];
   
      async function downloadBatchManagerExecutionAsText() : Promise<any> {
        let jolokia = null;
        try {
          jolokia = await jolokiaService.getJolokia();
        }
        catch (error) {
          log.error(logPrefix, "BatchManagerExecutionDownload retrieveJolokia, error: ", error);
        }
      
        let batchManagerExecutionJson : any = null;

        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveBatchManagerExecution(java.lang.Integer)";
            const result = await jolokia.execute(mbean, operation, batchManagerExecutionNum);
            batchManagerExecutionJson = (result === undefined || result === null) ? null : JSON.parse(result.toString());
          }
        }

        if (batchManagerExecutionJson !== null) {
          const batchManagerExecutionText : any[] = formatBatchManagerExecutionAsText(batchManagerExecutionJson);
          const batchManagerExecutionBlob = new Blob(batchManagerExecutionText, {type : "text/plain"});
          const batchManagerExecutionUrl = URL.createObjectURL(batchManagerExecutionBlob);
          const batchManagerExecutionLink = document.createElement("a");
          batchManagerExecutionLink.download = "batchmanagerexecution-" + batchManagerExecutionNum + ".txt";
          batchManagerExecutionLink.href = batchManagerExecutionUrl;
          batchManagerExecutionLink.click();
        }
      }

      const handleDownload = () => {
        downloadBatchManagerExecutionAsText()
          .then  (()    => {
            log.debug(logPrefix, "BatchManagerExecutionDownload downloadBatchManagerExecutionAsText OK");
          })
          .catch (error => {
            log.debug(logPrefix, "BatchManagerExecutionDownload downloadBatchManagerExecutionAsText exception: ", error);
          })
      }

      return (
        <Tooltip content = {"Download the selected Batch Executions"}>
          <Button
            isDisabled  = {selectableBatchExecutionsItemsNums.length !== 1}
            variant     = "plain"
            size        = "default"
            onClick     = {handleDownload}
            icon        = {<Icon status = "info" iconSize = "md"><DownloadIcon/></Icon>}
          />
        </Tooltip>
      )
    }

    // ********************************************************************************************************

    return (
      <Drawer tabIndex = {-1} isInline isExpanded = {true}>
        <DrawerContent tabIndex = {-1} panelContent = {batchManagerExecutionsPanelContent}>
          <BatchManagerExecutionsSearch/>
          {
            isDaySummaryView
              ? <BatchTablesSeparatedByStatus/>
              : <BatchManagerExecutionsTable/>
          }
        </DrawerContent>
      </Drawer>
    )
  }

  // ********************************************************************************************************

  const [context, setContext] = useState<any | null>(null);

  async function retrieveContext() {
    const context = await getContext();
    setContext(context);
    log.debug(logPrefix, "context: ", context);
  }

  // ********************************************************************************************************

  async function computeBatchManagerExecutions(args?: any) {
    await retrieveContext();

    await refreshJolokia();

    let batchManagerExecutionsNew = {...batchManagerExecutions};
    let allBatchExecutionsCountNew = {...allBatchExecutionsCount};

    await retrieveBatchManagerExecutions(batchManagerExecutionsNew, args);
    await retrieveAllBatchExecutionsCount(allBatchExecutionsCountNew);

    log.debug(logPrefix, "Batch Manager Count:", allBatchExecutionsCountNew);

    try {
      batchManagerExecutionsNew.computedData = batchManagerExecutionsNew.retrievedData;
      allBatchExecutionsCountNew.computedData = allBatchExecutionsCountNew.retrievedData;
    } 
    catch(e) {}

    setBatchManagerExecutions(batchManagerExecutionsNew);
    setAllBatchExecutionsCount(allBatchExecutionsCountNew);
  }

  // ********************************************************************************************************
 
  log.debug(logPrefix, "Displaying GtMonitor ****************************************************************************");

  return (
    <GraphTalkComponent tabIndex = {-1} title = "Batches" onCompute = {computeBatchManagerExecutions}>
      <BatchManagerExecutionsItems tabIndex = {-1}/>
    </GraphTalkComponent>
  )
}

export default Batches;