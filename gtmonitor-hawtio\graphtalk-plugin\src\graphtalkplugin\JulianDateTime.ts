// ********************************************************************************************************
// ********************************************************************************************************
//
// Julian Date and Time management
//
// ********************************************************************************************************
// ********************************************************************************************************

export function dateToJulian(date : Date) : number | null
{
  if (date === null) return null;

  date = new Date (date); // Need a clone
  date.setHours (0, 0, 0);

  // 86 400 000 = 1000*60*60*24
  return Math.round (date.getTime() / 86400000) + 62092;
};

// ********************************************************************************************************
export function julianToDate(julian : number) : Date
{
  const refJulian = Date.UTC(1800, 0, 1, 0, 0, 0, 0);
//  const date = new Date(refJulian + julian * 86400000);
  const date = new Date ((julian - (((new Date()).getTimezoneOffset() > 0 ) ? 62091 : 62092)) * 86400000);

//  date.setHours (0, 0, 0);

  return date;
};

// ********************************************************************************************************
export function timeToJulian(date : Date | null) : number | null
{
  if (date === null) return null;

  return date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds();
};

// ********************************************************************************************************
export function julianToTime(julian : number) : Date
{
  const date = new Date(0);

  if (julian !== 999999)
  {
    date.setHours (Math.floor (julian / 3600), Math.floor ((julian % 3600) / 60), (julian % 3600) % 60);
  }

  return date;
};

// ********************************************************************************************************
export function formatJulianAsDateTime(julian : number, isTime : boolean) : string
{
  if ( julian === 0 && !isTime) return "00/00/0000";
  if ( julian === 999999) return "99/99/9999";

  let date : Date, result : string;

  if (isTime)
  {
    // convert to "HH:mm:ss"
    date = julianToTime (julian);
    result = date.getHours().toString().padStart(2, "0") + ":" +
             date.getMinutes().toString().padStart(2, "0") + ":" +
             date.getSeconds().toString().padStart(2, "0");
  }
  else
  {
    // convert to "DD/MM/YYYY"
    date = julianToDate (julian);
    result = date.getDate().toString().padStart(2, "0") + "/" +
             (date.getMonth() + 1).toString().padStart(2, "0") + "/" +
             date.getFullYear().toString();
  }

  return result;
};

// ********************************************************************************************************
export function formatJulianAsDate (julian : number) : string
{
  return formatJulianAsDateTime(julian, false);
};

// ********************************************************************************************************
export function formatJulianAsTime (julian : number) : string
{
  return formatJulianAsDateTime(julian, true);
};
